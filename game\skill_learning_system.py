"""
Enhanced Contextual Skill Learning System for Me? Reincarnated? - Beta Version
"""
from typing import Dict, List, Any, Optional, Tuple, Set
import random
from .skill_system import SkillFusionSystem, Skill, SkillType, SkillRarity
from enum import Enum

class SkillTier(Enum):
    BEGINNER = "beginner"
    BASIC = "basic"
    ADVANCED = "advanced"
    MASTER = "master"
    LEGENDARY = "legendary"

class SpecializationPath(Enum):
    BERSERKER = "berserker"
    TACTICAL = "tactical"
    SHADOW = "shadow"
    ELEMENTAL = "elemental"
    GUARDIAN = "guardian"
    SCHOLAR = "scholar"
    WILD = "wild"

class SkillLearningSystem:
    """Enhanced skill learning system with tiered progression and specialization paths"""

    def __init__(self):
        self.skill_system = SkillFusionSystem()
        self.learning_triggers = self._initialize_learning_triggers()
        self.action_counters = {}  # Track repeated actions for skill learning
        self.learning_thresholds = self._initialize_learning_thresholds()
        self.skills_learned_by_category = {}  # Track progression in each category
        self.tutorial_boost_remaining = 5  # First 5 skills get 50% reduction
        self.specialization_points = 0
        self.chosen_specializations: Set[SpecializationPath] = set()
        self.locked_paths: Set[SpecializationPath] = set()
        self.skill_prerequisites = self._initialize_skill_prerequisites()
        self._add_learnable_skills_to_database()

    def _initialize_learning_triggers(self) -> Dict[str, Dict[str, Any]]:
        """Define triggers for contextual skill learning with tiered progression"""
        return {
            # Beginner Combat Skills (1-2 repetitions)
            "first_combat": {
                "skills": ["Basic Strike", "Combat Awareness"],
                "conditions": {"times": 1},
                "tier": SkillTier.BEGINNER,
                "category": "combat"
            },
            "combat_action": {
                "skills": ["Battle Reflexes", "Combat Instinct"],
                "conditions": {"times": 2},
                "tier": SkillTier.BEGINNER,
                "category": "combat"
            },
            # Weapon skills for creatures that can realistically use weapons
            "weapon_combat": {
                "skills": ["Weapon Familiarity", "Tool Mastery"],
                "conditions": {"times": 3, "creature": ["Goblin", "Human", "Elf", "Dwarf", "Orc"]},
                "tier": SkillTier.BASIC,
                "category": "combat"
            },

            # Basic Combat Skills (2-4 repetitions)
            "combat_victory": {
                "skills": ["Combat Mastery", "Battle Instinct", "Warrior's Focus"],
                "conditions": {"victories": 3, "level": 2},
                "tier": SkillTier.BASIC,
                "category": "combat"
            },
            "multiple_victories": {
                "skills": ["Combat Veteran", "Bloodlust", "Tactical Strike"],
                "conditions": {"victories": 8, "level": 4},
                "tier": SkillTier.ADVANCED,
                "category": "combat"
            },

            # Beginner Exploration Skills
            "first_exploration": {
                "skills": ["Basic Observation", "Environmental Awareness"],
                "conditions": {"times": 1},
                "tier": SkillTier.BEGINNER,
                "category": "exploration"
            },
            "explore_forest": {
                "skills": ["Nature Affinity", "Forest Navigation", "Plant Knowledge"],
                "conditions": {"times": 3},
                "tier": SkillTier.BASIC,
                "category": "exploration"
            },
            "explore_cave": {
                "skills": ["Dark Vision", "Cave Navigation", "Underground Sense"],
                "conditions": {"times": 3},
                "tier": SkillTier.BASIC,
                "category": "exploration"
            },
            "explore_ruins": {
                "skills": ["Ancient Knowledge", "Trap Detection", "Ruin Reader"],
                "conditions": {"times": 4, "level": 2},
                "tier": SkillTier.ADVANCED,
                "category": "exploration"
            },

            # Beginner Social Skills
            "first_interaction": {
                "skills": ["Basic Communication", "Social Awareness"],
                "conditions": {"times": 1},
                "tier": SkillTier.BEGINNER,
                "category": "social"
            },
            "talk_to_npc": {
                "skills": ["Diplomacy", "Social Insight", "Persuasion"],
                "conditions": {"times": 3},
                "tier": SkillTier.BASIC,
                "category": "social"
            },
            "help_npc": {
                "skills": ["Leadership", "Empathy", "Inspiration"],
                "conditions": {"times": 4},
                "tier": SkillTier.ADVANCED,
                "category": "social"
            },

            # Beginner Stealth Skills
            "first_stealth": {
                "skills": ["Basic Stealth", "Quiet Movement"],
                "conditions": {"times": 1},
                "tier": SkillTier.BEGINNER,
                "category": "stealth"
            },
            "sneak_action": {
                "skills": ["Stealth", "Silent Movement", "Shadow Affinity"],
                "conditions": {"times": 3},
                "tier": SkillTier.BASIC,
                "category": "stealth"
            },
            "hide_action": {
                "skills": ["Camouflage", "Ambush", "Invisibility"],
                "conditions": {"times": 4},
                "tier": SkillTier.ADVANCED,
                "category": "stealth"
            },

            # Resistance Skills
            "poison_damage_taken": {
                "skills": ["Poison Resistance", "Toxin Immunity", "Venom Mastery"],
                "conditions": {"times": 2},
                "tier": SkillTier.BASIC,
                "category": "resistance"
            },
            "fire_damage_taken": {
                "skills": ["Heat Resistance", "Fire Immunity", "Flame Mastery"],
                "conditions": {"times": 2},
                "tier": SkillTier.BASIC,
                "category": "resistance"
            },
            "cold_damage_taken": {
                "skills": ["Cold Resistance", "Ice Immunity", "Frost Mastery"],
                "conditions": {"times": 2},
                "tier": SkillTier.BASIC,
                "category": "resistance"
            },

            # Creature-Specific Skills
            "use_web_spin": {
                "skills": ["Web Mastery", "Trap Expertise", "Silk Weaving"],
                "conditions": {"times": 5, "creature": ["Spider"]},
                "tier": SkillTier.ADVANCED,
                "category": "mastery"
            },
            "use_absorb": {
                "skills": ["Absorption Mastery", "Mimic", "Perfect Assimilation"],
                "conditions": {"times": 6, "creature": ["Slime"]},
                "tier": SkillTier.ADVANCED,
                "category": "mastery"
            },
            "use_tool": {
                "skills": ["Tool Mastery", "Crafting", "Engineering"],
                "conditions": {"times": 4, "creature": ["Goblin"]},
                "tier": SkillTier.ADVANCED,
                "category": "mastery"
            },

            # Survival Skills
            "survive_low_hp": {
                "skills": ["Survival Instinct", "Last Stand", "Death Defiance"],
                "conditions": {"times": 3},
                "tier": SkillTier.ADVANCED,
                "category": "survival"
            },
            "heal_frequently": {
                "skills": ["Regeneration", "Self Care", "Healing Mastery"],
                "conditions": {"times": 6},
                "tier": SkillTier.BASIC,
                "category": "survival"
            },

            # Discovery Skills
            "discover_secret": {
                "skills": ["Investigation", "Keen Eye", "Secret Finder"],
                "conditions": {"times": 2},
                "tier": SkillTier.BASIC,
                "category": "discovery"
            },
            "find_treasure": {
                "skills": ["Treasure Hunter", "Luck", "Fortune's Favor"],
                "conditions": {"times": 3},
                "tier": SkillTier.ADVANCED,
                "category": "discovery"
            }
        }

    def _initialize_learning_thresholds(self) -> Dict[str, int]:
        """Define base learning thresholds by tier"""
        return {
            SkillTier.BEGINNER: 1,
            SkillTier.BASIC: 2,
            SkillTier.ADVANCED: 4,
            SkillTier.MASTER: 8,
            SkillTier.LEGENDARY: 16
        }

    def _initialize_skill_prerequisites(self) -> Dict[str, List[str]]:
        """Define skill prerequisites for advanced abilities"""
        return {
            # Tier 2 Combat Skills
            "Combat Veteran": ["Combat Mastery", "Battle Instinct"],
            "Bloodlust": ["Combat Mastery", "Last Stand"],
            "Tactical Strike": ["Battle Instinct", "Warrior's Focus"],
            "Berserker Rage": ["Bloodlust", "Death Defiance"],
            "Perfect Strike": ["Tactical Strike", "Combat Veteran"],

            # Tier 2 Stealth Skills
            "Shadow Master": ["Stealth", "Silent Movement", "Camouflage"],
            "Assassinate": ["Ambush", "Shadow Affinity"],
            "Phase Walk": ["Invisibility", "Shadow Master"],

            # Tier 2 Magic Skills
            "Elemental Mastery": ["Fire Immunity", "Ice Immunity", "Toxin Immunity"],
            "Perfect Assimilation": ["Absorption Mastery", "Mimic"],
            "Silk Weaving": ["Web Mastery", "Trap Expertise"],

            # Tier 2 Social Skills
            "Master Diplomat": ["Diplomacy", "Social Insight", "Persuasion"],
            "Inspiring Leader": ["Leadership", "Empathy", "Inspiration"],

            # Tier 3 Ultimate Skills
            "Apex Predator": ["Combat Veteran", "Bloodlust", "Death Defiance"],
            "Shadow God": ["Shadow Master", "Phase Walk", "Assassinate"],
            "Nature's Avatar": ["Nature Affinity", "Plant Knowledge", "Forest Navigation"],
            "Master of All": ["Tool Mastery", "Crafting", "Engineering"],

            # Specialization Path Skills
            "Berserker's Wrath": ["Bloodlust", "Last Stand"],  # Locks Tactical path
            "Tactical Genius": ["Tactical Strike", "Combat Veteran"],  # Locks Berserker path
            "Shadow Lord": ["Shadow Master", "Assassinate"],  # Locks Guardian path
            "Guardian's Resolve": ["Survival Instinct", "Self Care"],  # Locks Shadow path
            "Elemental Sage": ["Elemental Mastery", "Ancient Knowledge"],  # Locks Wild path
            "Wild Spirit": ["Nature Affinity", "Survival Instinct"],  # Locks Scholar path
        }

    def _add_learnable_skills_to_database(self):
        """Add expanded learnable skills to the skill system database"""
        learnable_skills = [
            # BEGINNER TIER SKILLS (Tier 1) - Easy to learn, immediate impact
            # Combat Beginner Skills
            Skill("Basic Strike", "Improved basic attack accuracy (+15% hit chance)", SkillType.COMBAT, SkillRarity.BASIC, 3, 0, ["accuracy_boost"], {}),
            Skill("Combat Awareness", "Detect enemy attacks earlier (+10% dodge)", SkillType.COMBAT, SkillRarity.BASIC, 0, 0, ["dodge_boost"], {}),
            Skill("Combat Instinct", "Natural fighting instincts (+5% damage, +5% dodge)", SkillType.COMBAT, SkillRarity.BASIC, 2, 0, ["damage_boost", "dodge_boost"], {}),
            Skill("Battle Reflexes", "React faster in combat (+15% speed)", SkillType.COMBAT, SkillRarity.BASIC, 0, 0, ["speed_boost"], {}),

            # Weapon/Tool Skills (for creatures capable of using tools)
            Skill("Weapon Familiarity", "Better handling of all weapons (+10% damage)", SkillType.COMBAT, SkillRarity.BASIC, 5, 0, ["weapon_bonus"], {}),
            Skill("Tool Mastery", "Skilled use of tools and implements (+15% tool effectiveness)", SkillType.UTILITY, SkillRarity.BASIC, 3, 0, ["tool_bonus"], {}),

            # Exploration Beginner Skills
            Skill("Basic Observation", "Notice more details in the environment", SkillType.UTILITY, SkillRarity.BASIC, 0, 0, ["observation_bonus"], {}),
            Skill("Environmental Awareness", "Better understanding of surroundings", SkillType.UTILITY, SkillRarity.BASIC, 0, 0, ["environment_bonus"], {}),

            # Social Beginner Skills
            Skill("Basic Communication", "Improved basic social interactions", SkillType.UTILITY, SkillRarity.BASIC, 0, 0, ["social_bonus"], {}),
            Skill("Social Awareness", "Read basic emotions and intentions", SkillType.UTILITY, SkillRarity.BASIC, 0, 0, ["empathy_bonus"], {}),

            # Stealth Beginner Skills
            Skill("Basic Stealth", "Move with reduced noise (+25% stealth)", SkillType.UTILITY, SkillRarity.BASIC, 3, 0, ["stealth_bonus"], {}),
            Skill("Quiet Movement", "Walk more quietly (+15% stealth)", SkillType.UTILITY, SkillRarity.BASIC, 0, 0, ["silent_move"], {}),

            # BASIC TIER SKILLS (Tier 2) - Solid foundation abilities
            # Combat Basic Skills
            Skill("Combat Mastery", "Mastery of combat fundamentals (+25% damage, +15% accuracy)", SkillType.COMBAT, SkillRarity.BASIC, 8, 0, ["combat_mastery"], {}),
            Skill("Battle Instinct", "Supernatural combat awareness (+20% dodge, +10% crit)", SkillType.COMBAT, SkillRarity.BASIC, 5, 0, ["battle_instinct"], {}),
            Skill("Warrior's Focus", "Maintain concentration in battle (+25% accuracy)", SkillType.COMBAT, SkillRarity.BASIC, 0, 0, ["focus_bonus"], {}),

            # Exploration Basic Skills
            Skill("Nature Affinity", "Deep connection with natural environments (+30% nature interaction)", SkillType.UTILITY, SkillRarity.BASIC, 0, 0, ["nature_mastery"], {}),
            Skill("Forest Navigation", "Never get lost in forest areas (immunity to forest maze effects)", SkillType.UTILITY, SkillRarity.BASIC, 0, 0, ["forest_navigation"], {}),
            Skill("Plant Knowledge", "Identify useful plants and herbs (+50% herb effectiveness)", SkillType.UTILITY, SkillRarity.BASIC, 0, 0, ["plant_mastery"], {}),
            Skill("Dark Vision", "See clearly in complete darkness", SkillType.UTILITY, SkillRarity.BASIC, 0, 0, ["night_vision"], {}),
            Skill("Cave Navigation", "Expert knowledge of underground passages", SkillType.UTILITY, SkillRarity.BASIC, 0, 0, ["cave_mastery"], {}),
            Skill("Underground Sense", "Feel vibrations and air currents underground", SkillType.UTILITY, SkillRarity.BASIC, 0, 0, ["underground_sense"], {}),

            # Social Basic Skills
            Skill("Diplomacy", "Master of negotiation and peaceful resolution (+40% social success)", SkillType.UTILITY, SkillRarity.BASIC, 0, 0, ["diplomacy_mastery"], {}),
            Skill("Social Insight", "Read complex emotions and hidden motives", SkillType.UTILITY, SkillRarity.BASIC, 5, 0, ["social_mastery"], {}),
            Skill("Persuasion", "Convince others to see your point of view (+30% persuasion)", SkillType.UTILITY, SkillRarity.BASIC, 0, 0, ["persuasion_bonus"], {}),

            # Stealth Basic Skills
            Skill("Stealth", "Move like a shadow (+50% stealth effectiveness)", SkillType.UTILITY, SkillRarity.BASIC, 6, 0, ["stealth_mastery"], {}),
            Skill("Silent Movement", "Move in complete silence (immunity to sound detection)", SkillType.UTILITY, SkillRarity.BASIC, 4, 0, ["silent_mastery"], {}),
            Skill("Shadow Affinity", "Blend with shadows (+35% stealth in dark areas)", SkillType.UTILITY, SkillRarity.BASIC, 0, 0, ["shadow_bonus"], {}),

            # Resistance Basic Skills
            Skill("Poison Resistance", "Strong resistance to toxins (-75% poison damage)", SkillType.RESISTANCE, SkillRarity.BASIC, 0, 0, ["poison_resistance"], {}),
            Skill("Heat Resistance", "Withstand extreme heat (-75% fire damage)", SkillType.RESISTANCE, SkillRarity.BASIC, 0, 0, ["fire_resistance"], {}),
            Skill("Cold Resistance", "Endure freezing temperatures (-75% ice damage)", SkillType.RESISTANCE, SkillRarity.BASIC, 0, 0, ["ice_resistance"], {}),

            # Survival Basic Skills
            Skill("Regeneration", "Slowly heal wounds over time (+5 HP per turn)", SkillType.UTILITY, SkillRarity.BASIC, 0, 0, ["auto_heal"], {}),
            Skill("Self Care", "Enhanced healing abilities (+100% healing effectiveness)", SkillType.UTILITY, SkillRarity.BASIC, 0, 0, ["heal_mastery"], {}),

            # Discovery Basic Skills
            Skill("Investigation", "Expert at finding hidden things (+75% search success)", SkillType.UTILITY, SkillRarity.BASIC, 3, 0, ["search_mastery"], {}),
            Skill("Keen Eye", "Notice the smallest details (+50% perception)", SkillType.UTILITY, SkillRarity.BASIC, 0, 0, ["perception_mastery"], {}),

            # ADVANCED TIER SKILLS (Tier 3) - Powerful specialized abilities
            # Combat Advanced Skills
            Skill("Combat Veteran", "Veteran of countless battles (+40% damage, +25% accuracy, +20% crit)", SkillType.COMBAT, SkillRarity.ADVANCED, 15, 0, ["veteran_mastery"], {}),
            Skill("Bloodlust", "Gain power from defeating enemies (+10% damage per recent kill, max 50%)", SkillType.COMBAT, SkillRarity.ADVANCED, 0, 0, ["bloodlust"], {}),
            Skill("Tactical Strike", "Precisely target enemy weaknesses (+100% crit chance vs weak points)", SkillType.COMBAT, SkillRarity.ADVANCED, 12, 0, ["tactical_mastery"], {}),
            Skill("Death Defiance", "Refuse to die easily (survive fatal damage once per combat)", SkillType.COMBAT, SkillRarity.ADVANCED, 0, 0, ["death_save"], {}),

            # Exploration Advanced Skills
            Skill("Ancient Knowledge", "Understanding of lost civilizations (+100% lore bonus, read ancient texts)", SkillType.UTILITY, SkillRarity.ADVANCED, 0, 0, ["ancient_mastery"], {}),
            Skill("Trap Detection", "Supernatural ability to sense traps (auto-detect all traps)", SkillType.UTILITY, SkillRarity.ADVANCED, 8, 0, ["trap_sense"], {}),
            Skill("Ruin Reader", "Decipher the secrets of ancient ruins (+200% treasure find in ruins)", SkillType.UTILITY, SkillRarity.ADVANCED, 0, 0, ["ruin_mastery"], {}),

            # Social Advanced Skills
            Skill("Leadership", "Inspire others to greatness (+50% ally effectiveness)", SkillType.UTILITY, SkillRarity.ADVANCED, 10, 0, ["leadership_mastery"], {}),
            Skill("Empathy", "Feel the emotions of others as your own (predict NPC actions)", SkillType.UTILITY, SkillRarity.ADVANCED, 0, 0, ["empathy_mastery"], {}),
            Skill("Inspiration", "Rally others in desperate times (boost ally morale in combat)", SkillType.UTILITY, SkillRarity.ADVANCED, 8, 0, ["inspiration_bonus"], {}),

            # Stealth Advanced Skills
            Skill("Camouflage", "Become one with the environment (+75% stealth, immunity to visual detection)", SkillType.UTILITY, SkillRarity.ADVANCED, 10, 0, ["camouflage_mastery"], {}),
            Skill("Ambush", "Strike from the shadows with devastating force (+200% damage from stealth)", SkillType.COMBAT, SkillRarity.ADVANCED, 15, 25, ["ambush_mastery"], {}),
            Skill("Invisibility", "Become truly invisible for short periods (3 turns of complete invisibility)", SkillType.UTILITY, SkillRarity.ADVANCED, 0, 15, ["invisibility"], {}),

            # Resistance Advanced Skills
            Skill("Toxin Immunity", "Complete immunity to all poisons and toxins", SkillType.RESISTANCE, SkillRarity.ADVANCED, 0, 0, ["poison_immunity"], {}),
            Skill("Fire Immunity", "Walk through flames unharmed (immunity to fire damage)", SkillType.RESISTANCE, SkillRarity.ADVANCED, 0, 0, ["fire_immunity"], {}),
            Skill("Ice Immunity", "Unaffected by cold and ice (immunity to ice damage)", SkillType.RESISTANCE, SkillRarity.ADVANCED, 0, 0, ["ice_immunity"], {}),

            # Mastery Advanced Skills
            Skill("Web Mastery", "Master of web-spinning (+200% web strength and versatility)", SkillType.UTILITY, SkillRarity.ADVANCED, 0, 0, ["web_mastery"], {}),
            Skill("Trap Expertise", "Create deadly and complex traps (+300% trap effectiveness)", SkillType.UTILITY, SkillRarity.ADVANCED, 12, 0, ["trap_mastery"], {}),
            Skill("Absorption Mastery", "Perfect absorption of nutrients and abilities (+150% absorption)", SkillType.UTILITY, SkillRarity.ADVANCED, 0, 0, ["absorb_mastery"], {}),
            Skill("Tool Mastery", "Master of all tools and weapons (+50% effectiveness with all equipment)", SkillType.UTILITY, SkillRarity.ADVANCED, 0, 0, ["tool_mastery"], {}),
            Skill("Crafting", "Create complex items and equipment (unlock advanced crafting)", SkillType.UTILITY, SkillRarity.ADVANCED, 8, 0, ["crafting_mastery"], {}),
            Skill("Engineering", "Design and build mechanical devices (unlock engineering)", SkillType.UTILITY, SkillRarity.ADVANCED, 12, 0, ["engineering"], {}),

            # Survival Advanced Skills
            Skill("Survival Instinct", "Preternatural ability to avoid death (+50% chance to avoid fatal damage)", SkillType.UTILITY, SkillRarity.ADVANCED, 0, 0, ["survival_mastery"], {}),
            Skill("Last Stand", "Become more powerful when near death (+100% damage when below 25% HP)", SkillType.COMBAT, SkillRarity.ADVANCED, 0, 0, ["last_stand"], {}),

            # Discovery Advanced Skills
            Skill("Treasure Hunter", "Supernatural ability to find valuable items (+200% treasure find rate)", SkillType.UTILITY, SkillRarity.ADVANCED, 0, 0, ["treasure_mastery"], {}),
            Skill("Luck", "Fortune favors you in all endeavors (+25% success rate on all actions)", SkillType.UTILITY, SkillRarity.ADVANCED, 0, 0, ["luck_mastery"], {}),
            Skill("Fortune's Favor", "Incredible luck in dangerous situations (reroll failed saves)", SkillType.UTILITY, SkillRarity.ADVANCED, 0, 0, ["fortune_mastery"], {}),
            Skill("Secret Finder", "Uncover the deepest secrets (auto-find all hidden content)", SkillType.UTILITY, SkillRarity.ADVANCED, 5, 0, ["secret_mastery"], {})
        ]

        # Add all learnable skills to the skill system database
        for skill in learnable_skills:
            self.skill_system.skill_database[skill.name] = skill

    def record_action(self, action_type: str, context: Dict[str, Any] = None) -> List[str]:
        """Record an action and check for skill learning opportunities with tiered progression"""
        if context is None:
            context = {}

        learned_skills = []

        # Increment action counter
        if action_type not in self.action_counters:
            self.action_counters[action_type] = 0
        self.action_counters[action_type] += 1

        # Check if this action triggers skill learning
        if action_type in self.learning_triggers:
            trigger = self.learning_triggers[action_type]
            category = trigger.get("category", "general")
            tier = trigger.get("tier", SkillTier.BASIC)

            # Calculate required repetitions with progressive scaling
            required_reps = self._calculate_required_repetitions(category, tier, context)

            # Check if conditions are met
            if self._check_learning_conditions_enhanced(action_type, trigger["conditions"], context, required_reps):
                # Learn a skill from the available options
                available_skills = self._filter_learnable_skills_enhanced(trigger["skills"], context)
                if available_skills:
                    learned_skill = self._select_skill_to_learn(available_skills, context)
                    if learned_skill:
                        learned_skills.append(learned_skill)

                        # Track skills learned in this category
                        if category not in self.skills_learned_by_category:
                            self.skills_learned_by_category[category] = 0
                        self.skills_learned_by_category[category] += 1

                        # Apply tutorial boost
                        if self.tutorial_boost_remaining > 0:
                            self.tutorial_boost_remaining -= 1

                        # Reset counter for this action to prevent immediate re-learning
                        self.action_counters[action_type] = 0

                        # Check for specialization path locks
                        self._check_specialization_locks(learned_skill)

        return learned_skills

    def _calculate_required_repetitions(self, category: str, tier: SkillTier, context: Dict[str, Any]) -> int:
        """Calculate required repetitions based on tier, category progression, and character level"""
        base_requirement = self.learning_thresholds[tier]

        # Progressive scaling: each skill in category increases requirement
        category_count = self.skills_learned_by_category.get(category, 0)
        progression_multiplier = 1 + (category_count * 0.5)  # 50% increase per skill in category

        # Level scaling: higher level = slightly more repetitions needed
        character_level = context.get("character_level", 1)
        level_multiplier = 1 + ((character_level - 1) * 0.1)  # 10% increase per level above 1

        # Tutorial boost: 50% reduction for first 5 skills
        tutorial_multiplier = 0.5 if self.tutorial_boost_remaining > 0 else 1.0

        final_requirement = base_requirement * progression_multiplier * level_multiplier * tutorial_multiplier
        return max(1, int(final_requirement))  # Minimum 1 repetition

    def _check_learning_conditions_enhanced(self, action_type: str, conditions: Dict[str, Any], context: Dict[str, Any], required_reps: int) -> bool:
        """Enhanced condition checking with dynamic requirements"""

        # Check action count requirement
        if "times" in conditions:
            if self.action_counters.get(action_type, 0) < required_reps:
                return False

        # Check other specific conditions
        if "victories" in conditions:
            if context.get("combat_victories", 0) < conditions["victories"]:
                return False

        if "level" in conditions:
            if context.get("character_level", 1) < conditions["level"]:
                return False

        if "creature" in conditions:
            creature_types = conditions["creature"]
            if context.get("creature_type", "") not in creature_types:
                return False

        return True

    def _filter_learnable_skills_enhanced(self, skill_names: List[str], context: Dict[str, Any]) -> List[str]:
        """Enhanced skill filtering with prerequisite checking"""
        current_abilities = context.get("current_abilities", [])
        available_skills = []

        for skill_name in skill_names:
            # Skip if already known
            if skill_name in current_abilities:
                continue

            # Check prerequisites
            if skill_name in self.skill_prerequisites:
                prerequisites = self.skill_prerequisites[skill_name]
                if not all(prereq in current_abilities for prereq in prerequisites):
                    continue

            # Check specialization locks
            if self._is_skill_locked_by_specialization(skill_name):
                continue

            available_skills.append(skill_name)

        return available_skills

    def _select_skill_to_learn(self, available_skills: List[str], context: Dict[str, Any]) -> str:
        """Select which skill to learn from available options"""
        if not available_skills:
            return None

        # Prioritize beginner skills for new players
        if self.tutorial_boost_remaining > 0:
            beginner_skills = [skill for skill in available_skills if self._is_beginner_skill(skill)]
            if beginner_skills:
                return random.choice(beginner_skills)

        # Otherwise, random selection
        return random.choice(available_skills)

    def _is_beginner_skill(self, skill_name: str) -> bool:
        """Check if a skill is a beginner-tier skill"""
        beginner_skills = [
            "Basic Strike", "Combat Awareness", "Combat Instinct", "Battle Reflexes",
            "Basic Observation", "Environmental Awareness", "Basic Communication",
            "Social Awareness", "Basic Stealth", "Quiet Movement"
        ]
        return skill_name in beginner_skills

    def _check_specialization_locks(self, learned_skill: str):
        """Check if learning this skill locks any specialization paths"""
        specialization_locks = {
            "Berserker's Wrath": (SpecializationPath.BERSERKER, SpecializationPath.TACTICAL),
            "Tactical Genius": (SpecializationPath.TACTICAL, SpecializationPath.BERSERKER),
            "Shadow Lord": (SpecializationPath.SHADOW, SpecializationPath.GUARDIAN),
            "Guardian's Resolve": (SpecializationPath.GUARDIAN, SpecializationPath.SHADOW),
            "Elemental Sage": (SpecializationPath.ELEMENTAL, SpecializationPath.WILD),
            "Wild Spirit": (SpecializationPath.WILD, SpecializationPath.SCHOLAR)
        }

        if learned_skill in specialization_locks:
            chosen_path, locked_path = specialization_locks[learned_skill]
            self.chosen_specializations.add(chosen_path)
            self.locked_paths.add(locked_path)

    def _is_skill_locked_by_specialization(self, skill_name: str) -> bool:
        """Check if a skill is locked due to specialization choices"""
        skill_specializations = {
            "Berserker's Wrath": SpecializationPath.BERSERKER,
            "Tactical Genius": SpecializationPath.TACTICAL,
            "Shadow Lord": SpecializationPath.SHADOW,
            "Guardian's Resolve": SpecializationPath.GUARDIAN,
            "Elemental Sage": SpecializationPath.ELEMENTAL,
            "Wild Spirit": SpecializationPath.WILD
        }

        if skill_name in skill_specializations:
            required_path = skill_specializations[skill_name]
            return required_path in self.locked_paths

        return False

    def _check_learning_conditions(self, action_type: str, conditions: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """Check if all conditions for learning are met"""

        # Check action count requirement
        if "times" in conditions:
            if self.action_counters.get(action_type, 0) < conditions["times"]:
                return False

        # Check other specific conditions
        if "victories" in conditions:
            if context.get("combat_victories", 0) < conditions["victories"]:
                return False

        if "level" in conditions:
            if context.get("character_level", 1) < conditions["level"]:
                return False

        if "creature" in conditions:
            creature_types = conditions["creature"]
            if context.get("creature_type", "") not in creature_types:
                return False

        return True

    def _filter_learnable_skills(self, skill_names: List[str], context: Dict[str, Any]) -> List[str]:
        """Filter skills that can actually be learned (not already known)"""
        current_abilities = context.get("current_abilities", [])
        return [skill for skill in skill_names if skill not in current_abilities]

    def analyze_action_for_learning(self, user_input: str, character, location: str, memory_system=None) -> List[str]:
        """Analyze a user action and determine what skills might be learned with enhanced detection"""
        learned_skills = []
        action_lower = user_input.lower()

        # Build context for learning checks
        context = {
            "character_level": character.level,
            "creature_type": character.creature_type,
            "current_abilities": character.abilities,
            "combat_victories": character.combat_victories,
            "location": location
        }

        # First-time action detection for beginner skills
        total_skills_learned = sum(self.skills_learned_by_category.values())

        # Combat actions (with first-time detection)
        if any(word in action_lower for word in ["attack", "fight", "battle", "combat"]):
            if total_skills_learned == 0:  # Very first action
                learned_skills.extend(self.record_action("first_combat", context))
            else:
                learned_skills.extend(self.record_action("combat_action", context))

            # Check for weapon-based combat (only for creatures that can use weapons)
            if any(word in action_lower for word in ["weapon", "sword", "axe", "bow", "staff", "club", "dagger", "spear"]):
                learned_skills.extend(self.record_action("weapon_combat", context))

        # Exploration actions (with first-time detection)
        if any(word in action_lower for word in ["explore", "search", "investigate", "look around"]):
            if self.skills_learned_by_category.get("exploration", 0) == 0:
                learned_skills.extend(self.record_action("first_exploration", context))

            if "forest" in location.lower():
                learned_skills.extend(self.record_action("explore_forest", context))
            elif "cave" in location.lower():
                learned_skills.extend(self.record_action("explore_cave", context))
            elif "ruins" in location.lower():
                learned_skills.extend(self.record_action("explore_ruins", context))

        # Social actions (with first-time detection)
        if any(word in action_lower for word in ["talk", "speak", "greet", "ask", "tell"]):
            if self.skills_learned_by_category.get("social", 0) == 0:
                learned_skills.extend(self.record_action("first_interaction", context))
            learned_skills.extend(self.record_action("talk_to_npc", context))

        if any(word in action_lower for word in ["help", "assist", "aid", "support"]):
            learned_skills.extend(self.record_action("help_npc", context))

        # Stealth actions (with first-time detection)
        if any(word in action_lower for word in ["sneak", "creep", "quietly", "stealthily"]):
            if self.skills_learned_by_category.get("stealth", 0) == 0:
                learned_skills.extend(self.record_action("first_stealth", context))
            learned_skills.extend(self.record_action("sneak_action", context))

        if any(word in action_lower for word in ["hide", "conceal", "duck", "cover"]):
            learned_skills.extend(self.record_action("hide_action", context))

        # Skill usage tracking
        if "web" in action_lower and "Web Spin" in character.abilities:
            learned_skills.extend(self.record_action("use_web_spin", context))

        if "absorb" in action_lower and "Absorb" in character.abilities:
            learned_skills.extend(self.record_action("use_absorb", context))

        if any(word in action_lower for word in ["use tool", "craft", "build", "make"]):
            learned_skills.extend(self.record_action("use_tool", context))

        # Survival situations
        if character.stats["hp"] < character.stats["max_hp"] * 0.2:  # Low HP
            learned_skills.extend(self.record_action("survive_low_hp", context))

        if any(word in action_lower for word in ["heal", "rest", "recover"]):
            learned_skills.extend(self.record_action("heal_frequently", context))

        # Discovery actions
        if any(word in action_lower for word in ["secret", "hidden", "discover", "find"]):
            learned_skills.extend(self.record_action("discover_secret", context))

        if any(word in action_lower for word in ["treasure", "loot", "valuable"]):
            learned_skills.extend(self.record_action("find_treasure", context))

        return learned_skills

    def get_skill_learning_hints(self, character, location: str) -> List[str]:
        """Get enhanced hints about skills that could be learned through actions"""
        hints = []

        context = {
            "character_level": character.level,
            "creature_type": character.creature_type,
            "current_abilities": character.abilities,
            "combat_victories": character.combat_victories,
            "location": location
        }

        # Check what skills are close to being learned
        for action_type, trigger in self.learning_triggers.items():
            conditions = trigger["conditions"]
            category = trigger.get("category", "general")
            tier = trigger.get("tier", SkillTier.BASIC)
            current_count = self.action_counters.get(action_type, 0)

            # Calculate required repetitions for this action
            required = self._calculate_required_repetitions(category, tier, context)

            if "times" in conditions:
                if current_count >= 0 and current_count < required:  # Show hints even for 0 progress
                    # Check if other conditions are met
                    if self._check_other_conditions_enhanced(conditions, context):
                        available_skills = self._filter_learnable_skills_enhanced(trigger["skills"], context)
                        if available_skills:
                            remaining = required - current_count
                            action_name = action_type.replace("_", " ").title()
                            tier_name = tier.value.title()

                            # Show tutorial boost if applicable
                            boost_text = " (Tutorial Boost!)" if self.tutorial_boost_remaining > 0 else ""

                            # Only show if we have some progress or it's a beginner skill
                            if current_count > 0 or tier == SkillTier.BEGINNER:
                                hints.append(f"{tier_name}: {action_name} ({remaining} more) → {', '.join(available_skills[:2])}{boost_text}")

        # Add specialization warnings
        if self.locked_paths:
            locked_names = [path.value.title() for path in self.locked_paths]
            hints.append(f"⚠️ Locked Paths: {', '.join(locked_names)}")

        # Add prerequisite hints for advanced skills
        prereq_hints = self._get_prerequisite_hints(context)
        hints.extend(prereq_hints)

        return hints[:5]  # Show up to 5 hints

    def _get_prerequisite_hints(self, context: Dict[str, Any]) -> List[str]:
        """Get hints about skills that could be unlocked with prerequisites"""
        hints = []
        current_abilities = context.get("current_abilities", [])

        for skill_name, prerequisites in self.skill_prerequisites.items():
            if skill_name in current_abilities:
                continue

            missing_prereqs = [prereq for prereq in prerequisites if prereq not in current_abilities]
            if len(missing_prereqs) == 1:  # Only one prerequisite missing
                hints.append(f"🔓 Learn '{missing_prereqs[0]}' to unlock '{skill_name}'")
            elif len(missing_prereqs) <= 2 and len(prerequisites) > 2:  # Close to unlocking
                hints.append(f"🔓 Need {len(missing_prereqs)} more skills for '{skill_name}'")

        return hints[:2]  # Limit prerequisite hints

    def _check_other_conditions_enhanced(self, conditions: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """Enhanced condition checking for hints"""
        if "level" in conditions and context.get("character_level", 1) < conditions["level"]:
            return False

        if "creature" in conditions:
            creature_types = conditions["creature"]
            if context.get("creature_type", "") not in creature_types:
                return False

        if "victories" in conditions and context.get("combat_victories", 0) < conditions["victories"]:
            return False

        return True

    def get_specialization_status(self) -> Dict[str, Any]:
        """Get current specialization status"""
        return {
            "chosen_paths": [path.value for path in self.chosen_specializations],
            "locked_paths": [path.value for path in self.locked_paths],
            "available_paths": [path.value for path in SpecializationPath if path not in self.locked_paths],
            "tutorial_boost_remaining": self.tutorial_boost_remaining,
            "skills_by_category": dict(self.skills_learned_by_category)
        }

    def can_learn_skill(self, skill_name: str, character) -> bool:
        """Check if a character can learn a specific skill"""
        if skill_name in character.abilities:
            return False

        # Check prerequisites
        if skill_name in self.skill_prerequisites:
            prerequisites = self.skill_prerequisites[skill_name]
            if not all(prereq in character.abilities for prereq in prerequisites):
                return False

        # Check specialization locks
        if self._is_skill_locked_by_specialization(skill_name):
            return False

        return True

    def _check_other_conditions(self, conditions: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """Check non-count conditions for learning"""
        if "level" in conditions and context.get("character_level", 1) < conditions["level"]:
            return False

        if "creature" in conditions:
            creature_types = conditions["creature"]
            if context.get("creature_type", "") not in creature_types:
                return False

        if "victories" in conditions and context.get("combat_victories", 0) < conditions["victories"]:
            return False

        return True

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for saving with enhanced data"""
        return {
            "action_counters": self.action_counters,
            "skills_learned_by_category": self.skills_learned_by_category,
            "tutorial_boost_remaining": self.tutorial_boost_remaining,
            "specialization_points": self.specialization_points,
            "chosen_specializations": [path.value for path in self.chosen_specializations],
            "locked_paths": [path.value for path in self.locked_paths]
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SkillLearningSystem':
        """Create from dictionary with enhanced data loading"""
        system = cls()
        system.action_counters = data.get("action_counters", {})
        system.skills_learned_by_category = data.get("skills_learned_by_category", {})
        system.tutorial_boost_remaining = data.get("tutorial_boost_remaining", 5)
        system.specialization_points = data.get("specialization_points", 0)

        # Load specialization paths
        chosen_paths = data.get("chosen_specializations", [])
        system.chosen_specializations = {SpecializationPath(path) for path in chosen_paths}

        locked_paths = data.get("locked_paths", [])
        system.locked_paths = {SpecializationPath(path) for path in locked_paths}

        return system
