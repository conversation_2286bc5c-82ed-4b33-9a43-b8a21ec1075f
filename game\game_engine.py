"""
Main game engine for Me? Reincarnated?
"""
import async<PERSON>
from typing import Dict, Any, Optional, List
from enum import Enum

from .character import Character
from .memory_system import MemorySystem
from .save_system import SaveSystem
from .skill_system import SkillFusionSystem
from .skill_learning_system import SkillLearningSystem
from .combat_system import CombatSystem
from .evolution_system import EvolutionSystem
from .location_system import LocationSystem
from .item_system import ItemSystem
from .difficulty_system import DifficultyAdjustmentSystem
from .lore_system import LoreSystem
from .portrait_system import PortraitSystem
from api.gemini_client import GeminiClient
import config

class GameState(Enum):
    MENU = "menu"
    CHARACTER_CREATION = "character_creation"
    PLAYING = "playing"
    COMBAT = "combat"
    PAUSED = "paused"

class GameEngine:
    def __init__(self):
        """Initialize the game engine"""
        self.state = GameState.MENU
        self.character = Character()
        self.memory = MemorySystem()
        self.save_system = SaveSystem()
        self.gemini_client = GeminiClient()

        # New game systems
        self.skill_system = SkillFusionSystem()
        self.skill_learning_system = SkillLearningSystem()
        self.combat_system = CombatSystem()
        self.evolution_system = EvolutionSystem()
        self.location_system = LocationSystem()
        self.item_system = ItemSystem()
        self.difficulty_system = DifficultyAdjustmentSystem()
        self.lore_system = LoreSystem()
        self.portrait_system = PortraitSystem()

        # Character creation state
        self.creation_stage = "backstory"  # backstory -> name -> traits -> occupation -> creature
        self.selected_backstory = None

        # Game world state
        self.current_location = "Mysterious Forest"
        self.world_state = {
            "day": 1,
            "time": "morning",
            "weather": "clear"
        }

        # Game session data
        self.playtime = 0
        self.last_auto_save = 0

    async def start_new_game(self):
        """Start a new game"""
        self.state = GameState.CHARACTER_CREATION
        self.creation_stage = "backstory"
        self.character = Character()
        self.memory = MemorySystem()
        self.selected_backstory = None

        # Initialize starting location
        self.memory.update_current_context(location=self.current_location)

        # Present backstory selection
        backstory_options = "\n".join([
            f"{i+1}. {scenario.name}: {scenario.description}"
            for i, scenario in enumerate(self.lore_system.backstory_scenarios)
        ])

        return f"""Welcome to your final moments...

Your life is ending, but this is not the end of your story. How did you meet your fate?

{backstory_options}

Choose your backstory by entering the number (1-{len(self.lore_system.backstory_scenarios)}) or type 'random' for a surprise:"""

    async def process_character_creation(self, user_input: str) -> str:
        """Process character creation input"""
        if self.creation_stage == "backstory":
            # Handle backstory selection
            user_choice = user_input.strip().lower()

            if user_choice == "random":
                self.selected_backstory = self.lore_system.get_random_backstory()
            else:
                try:
                    choice_num = int(user_choice) - 1
                    if 0 <= choice_num < len(self.lore_system.backstory_scenarios):
                        self.selected_backstory = self.lore_system.backstory_scenarios[choice_num]
                    else:
                        return f"Please enter a number between 1 and {len(self.lore_system.backstory_scenarios)}, or 'random'."
                except ValueError:
                    return f"Please enter a number between 1 and {len(self.lore_system.backstory_scenarios)}, or 'random'."

            if self.selected_backstory:
                self.creation_stage = "name"
                return f"""{self.selected_backstory.death_scene}

{self.selected_backstory.transition_scene}

{self.selected_backstory.reincarnation_explanation}

The voice asks gently: "What would you like to be called in this new world?" """

        elif self.creation_stage == "name":
            self.character.name = user_input.strip()
            self.creation_stage = "traits"
            response = await self.gemini_client.generate_character_creation_response(user_input, "name")
            return response + "\n\nPlease describe your personality traits (e.g., 'curious, brave, analytical'):"

        elif self.creation_stage == "traits":
            traits = [trait.strip() for trait in user_input.split(',')]
            self.character.traits = traits
            self.creation_stage = "occupation"
            response = await self.gemini_client.generate_character_creation_response(user_input, "traits")
            return response + "\n\nWhat was your occupation in your previous life?"

        elif self.creation_stage == "occupation":
            self.character.occupation = user_input.strip()
            self.creation_stage = "creature"
            response = await self.gemini_client.generate_character_creation_response(user_input, "occupation")

            # Enhanced creature selection with detailed information
            creature_options = []
            for i, creature in enumerate(config.STARTING_CREATURES):
                stats = creature['base_stats']
                abilities = ", ".join(creature['abilities'])
                evolutions = ", ".join(creature['evolution_paths'])

                creature_info = f"""
{i+1}. {creature['name']} - {creature['description']}
   Stats: HP:{stats['hp']} MP:{stats['mp']} ATK:{stats['attack']} DEF:{stats['defense']} SPD:{stats['speed']}
   Starting Abilities: {abilities}
   Evolution Paths: {evolutions}"""
                creature_options.append(creature_info)

            creature_display = "\n".join(creature_options)

            return response + f"""\n\nThe time has come to choose your new form. Each creature offers a unique path to power:

{creature_display}

Enter the number (1-{len(config.STARTING_CREATURES)}) of your chosen form:"""

        elif self.creation_stage == "creature":
            # Find matching creature - handle both numeric and name input
            chosen_creature = None
            user_choice = user_input.strip()

            # Try numeric input first
            try:
                choice_num = int(user_choice) - 1
                if 0 <= choice_num < len(config.STARTING_CREATURES):
                    chosen_creature = config.STARTING_CREATURES[choice_num]["name"]
            except ValueError:
                # Fall back to name matching
                user_choice_lower = user_choice.lower()
                for creature in config.STARTING_CREATURES:
                    if creature["name"].lower() in user_choice_lower:
                        chosen_creature = creature["name"]
                        break

            if chosen_creature:
                self.character.set_creature_type(chosen_creature)
                self.state = GameState.PLAYING

                # Record important events
                self.memory.add_important_event(f"Reincarnated as a {chosen_creature}", "reincarnation")
                self.memory.discover_location(self.current_location, "A mysterious forest where you first awakened")

                # Generate character portrait
                if config.ENABLE_PORTRAIT_GENERATION:
                    character_data = self.character.to_dict()
                    self.portrait_system.preload_character_portraits(character_data)

                # Generate opening scene
                opening_prompt = f"""
                Generate an opening scene for a reincarnated {chosen_creature} named {self.character.name}
                who just awakened in a mysterious forest. The character was formerly a {self.character.occupation}
                with traits: {', '.join(self.character.traits)}.

                Describe their first moments of consciousness, what they see, feel, and their immediate surroundings.
                End with some options for what they can do first.
                """

                opening_scene = await self.gemini_client.generate_text(opening_prompt)
                self.memory.add_short_term_event(f"Awakened as {chosen_creature} in {self.current_location}")

                return f"Your transformation is complete!\n\n{opening_scene}"
            else:
                creature_names = [creature["name"] for creature in config.STARTING_CREATURES]
                return f"I didn't understand your choice. Please enter a number (1-{len(config.STARTING_CREATURES)}) or choose from: {', '.join(creature_names)}."

        return "Something went wrong with character creation."

    async def process_game_action(self, user_input: str) -> str:
        """Process player actions during gameplay"""
        if self.state != GameState.PLAYING:
            return "Game is not in playing state."

        # Add user action to memory
        self.memory.add_short_term_event(f"Player: {user_input}")

        # Record action for difficulty tracking
        self.difficulty_system.record_action()

        # Get current context for AI
        location_context = self.memory.get_location_context(self.current_location)

        # Build world context
        world_context = f"Location: {location_context}. Time: {self.world_state['time']} of day {self.world_state['day']}."

        # Check for contextual skill learning
        learned_skills = self.skill_learning_system.analyze_action_for_learning(
            user_input, self.character, self.current_location, self.memory
        )

        # Apply learned skills
        skill_learning_messages = []
        for skill_name in learned_skills:
            self.character.learn_ability(skill_name)
            skill_learning_messages.append(f"🎓 You learned a new skill: {skill_name}!")
            self.memory.add_important_event(f"Learned skill: {skill_name}", "skill_learning")

        # Generate response using Gemini
        game_state_dict = self.get_game_state()
        response = await self.gemini_client.generate_game_response(
            user_input, game_state_dict, list(self.memory.short_term_memory), world_context
        )

        # Add skill learning messages to response
        if skill_learning_messages:
            response += "\n\n" + "\n".join(skill_learning_messages)

        # Check for difficulty adjustment
        should_adjust, new_difficulty = self.difficulty_system.should_adjust_difficulty()
        if should_adjust:
            adjustment_message = self.difficulty_system.adjust_difficulty(new_difficulty)
            response += f"\n\n{adjustment_message}"

        # Add AI response to memory
        self.memory.add_short_term_event(f"Game: {response[:100]}...")

        # Check for auto-save
        self.playtime += 1
        if self.playtime - self.last_auto_save >= config.AUTO_SAVE_INTERVAL:
            self.auto_save()

        return response

    def get_game_state(self) -> Dict[str, Any]:
        """Get complete game state for saving"""
        return {
            "character": self.character.to_dict(),
            "memory": self.memory.to_dict(),
            "current_location": self.current_location,
            "world_state": self.world_state,
            "playtime": self.playtime,
            "creation_stage": self.creation_stage,
            "state": self.state.value
        }

    def load_game_state(self, game_state: Dict[str, Any]):
        """Load game state from save data"""
        try:
            # Load character
            if "character" in game_state:
                self.character = Character.from_dict(game_state["character"])

            # Load memory
            if "memory" in game_state:
                self.memory = MemorySystem.from_dict(game_state["memory"])

            # Load world state
            self.current_location = game_state.get("current_location", "Mysterious Forest")
            self.world_state = game_state.get("world_state", self.world_state)
            self.playtime = game_state.get("playtime", 0)
            self.creation_stage = game_state.get("creation_stage", "name")

            # Load game state
            state_value = game_state.get("state", "menu")
            self.state = GameState(state_value)

            return True

        except Exception as e:
            print(f"Error loading game state: {e}")
            return False

    def save_game(self, save_name: Optional[str] = None) -> bool:
        """Save the current game"""
        game_state = self.get_game_state()
        return self.save_system.save_game(game_state, save_name)

    def auto_save(self) -> bool:
        """Perform auto-save"""
        game_state = self.get_game_state()
        success = self.save_system.auto_save(game_state)
        if success:
            self.last_auto_save = self.playtime
        return success

    def load_game(self, save_name: str) -> bool:
        """Load a saved game"""
        game_state = self.save_system.load_game(save_name)
        if game_state:
            return self.load_game_state(game_state)
        return False

    def get_character_status(self) -> str:
        """Get formatted character status"""
        if self.character.name:
            status = self.character.get_status_summary()

            # Add fusion hints
            fusion_hints = self.skill_system.get_fusion_hints(self.character.abilities)
            if fusion_hints:
                status += f"\n\nFusion Hints:\n" + "\n".join(f"• {hint}" for hint in fusion_hints[:3])

            # Add skill learning hints
            learning_hints = self.skill_learning_system.get_skill_learning_hints(self.character, self.current_location)
            if learning_hints:
                status += f"\n\nSkill Learning Progress:\n" + "\n".join(f"• {hint}" for hint in learning_hints[:3])

            # Add evolution progress
            eligible_evolutions = self.evolution_system.check_evolution_eligibility(self.character, self.memory)
            if eligible_evolutions:
                status += f"\n\nEvolution Status:\n"
                for evo_name, missing_reqs in eligible_evolutions:
                    if not missing_reqs:
                        status += f"• {evo_name}: READY TO EVOLVE!\n"
                    else:
                        status += f"• {evo_name}: Missing {len(missing_reqs)} requirements\n"

            return status
        return "No character created yet."

    async def process_combat_action(self, action: str, target: str = None) -> str:
        """Process combat actions"""
        if self.state != GameState.COMBAT:
            return "Not in combat!"

        result = self.combat_system.execute_character_action(action, target)

        if result["success"]:
            response = "\n".join(result["messages"])

            # Check if combat ended
            if result.get("combat_ended"):
                self.state = GameState.PLAYING
                if result.get("victory"):
                    # Handle victory tracking (experience already awarded in combat system)
                    if "rewards" in result:
                        rewards = result["rewards"]
                        # Record combat victory for evolution tracking (without double experience)
                        self.character.combat_victories += 1

                        # Check for combat-based skill learning
                        combat_learned_skills = self.skill_learning_system.record_action(
                            "combat_victory",
                            {
                                "character_level": self.character.level,
                                "creature_type": self.character.creature_type,
                                "current_abilities": self.character.abilities,
                                "combat_victories": self.character.combat_victories
                            }
                        )

                        # Apply combat learned skills
                        for skill_name in combat_learned_skills:
                            self.character.learn_ability(skill_name)
                            response += f"\n🎓 Combat experience taught you: {skill_name}!"
                            self.memory.add_important_event(f"Learned skill from combat: {skill_name}", "skill_learning")

                        if "experience" in rewards:
                            # Track for difficulty system
                            self.difficulty_system.record_combat_result(True, 5)  # Assume 5 turn duration for now
                        if "loot" in rewards:
                            for item in rewards["loot"]:
                                self.character.inventory.append(item)

                    # Check for skill fusions after combat
                    await self.check_skill_fusions()

                    # Check for evolution eligibility
                    await self.check_evolution_eligibility()

            return response
        else:
            return result.get("message", "Combat action failed")

    async def check_skill_fusions(self) -> str:
        """Check and automatically perform skill fusions"""
        possible_fusions = self.skill_system.check_fusion_possibilities(self.character.abilities)
        fusion_messages = []

        for fusion_name in possible_fusions:
            success, message, fused_skill = self.skill_system.perform_fusion(
                self.character.abilities, fusion_name, self.character.creature_type
            )

            if success and fused_skill:
                # Remove component skills and add fused skill
                recipe = self.skill_system.fusion_recipes[fusion_name]
                for component in recipe["components"]:
                    if component in self.character.abilities:
                        self.character.abilities.remove(component)

                self.character.learn_ability(fused_skill.name)
                self.character.discover_fusion(fusion_name, recipe["components"])

                fusion_messages.append(f"🌟 SKILL FUSION! {message}")

                # Record in memory
                self.memory.add_important_event(f"Discovered skill fusion: {fusion_name}", "fusion")

        return "\n".join(fusion_messages) if fusion_messages else ""

    async def check_evolution_eligibility(self) -> str:
        """Check if character can evolve"""
        eligible_evolutions = self.evolution_system.check_evolution_eligibility(self.character, self.memory)
        ready_evolutions = [name for name, missing in eligible_evolutions if not missing]

        if ready_evolutions:
            evolution_name = ready_evolutions[0]  # Take first available evolution
            success, story, details = self.evolution_system.perform_evolution(self.character, evolution_name)

            if success:
                self.memory.add_important_event(f"Evolved into {self.character.creature_type}", "evolution")

                # Generate portrait for evolved form
                if config.ENABLE_PORTRAIT_GENERATION:
                    character_data = self.character.to_dict()
                    self.portrait_system.queue_portrait_generation(character_data, "evolved", priority=1)

                return f"\n🎉 EVOLUTION! {story}\n\nNew abilities: {', '.join(details['new_abilities'])}"

        return ""

    async def start_combat(self, enemy_name: str) -> str:
        """Start a combat encounter"""
        result = self.combat_system.start_combat(self.character, enemy_name, self.current_location)

        if result["success"]:
            self.state = GameState.COMBAT
            return result["message"]
        else:
            return result["message"]

    def get_available_actions(self) -> List[Dict[str, Any]]:
        """Get available actions based on current game state"""
        if self.state == GameState.COMBAT:
            return self.combat_system.get_available_actions(self.character)
        elif self.state == GameState.PLAYING:
            # Regular gameplay actions
            actions = [
                {"action": "explore", "name": "Explore", "description": "Look around the current area"},
                {"action": "move", "name": "Move", "description": "Travel to a connected location"},
                {"action": "inventory", "name": "Inventory", "description": "Check your items and equipment"},
                {"action": "skills", "name": "Skills", "description": "View your abilities and fusion options"}
            ]

            # Add location-specific actions
            location = self.location_system.get_location(self.current_location)
            if location:
                encounters = self.location_system.get_available_encounters(
                    self.current_location, self.character.level, self.character.abilities
                )
                for encounter in encounters[:3]:  # Show up to 3 encounters
                    actions.append({
                        "action": "encounter",
                        "name": encounter.name,
                        "description": encounter.description,
                        "encounter_type": encounter.encounter_type.value
                    })

            return actions

        return []

    async def get_character_portrait(self):
        """Get the current character's portrait"""
        if self.character.name and config.ENABLE_PORTRAIT_GENERATION:
            character_data = self.character.to_dict()
            evolution_stage = "evolved" if hasattr(self.character, 'evolution_count') and self.character.evolution_count > 0 else "base"
            return await self.portrait_system.get_character_portrait(character_data, evolution_stage)
        return None

    def get_portrait_system_info(self) -> Dict[str, Any]:
        """Get information about the portrait system"""
        return self.portrait_system.get_cache_info()
