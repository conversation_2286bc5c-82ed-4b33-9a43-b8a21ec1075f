["tests/test_character.py::TestCharacter::test_character_initialization", "tests/test_character.py::TestCharacter::test_experience_overflow", "tests/test_character.py::TestCharacter::test_gain_experience_and_level_up", "tests/test_character.py::TestCharacter::test_get_status_summary", "tests/test_character.py::TestCharacter::test_heal", "tests/test_character.py::TestCharacter::test_learn_ability", "tests/test_character.py::TestCharacter::test_set_creature_type", "tests/test_character.py::TestCharacter::test_take_damage", "tests/test_character.py::TestCharacter::test_to_dict_and_from_dict", "tests/test_character.py::TestCharacter::test_use_mp", "tests/test_combat_system.py::TestCombatSystem::test_available_actions", "tests/test_combat_system.py::TestCombatSystem::test_character_attack_action", "tests/test_combat_system.py::TestCombatSystem::test_character_defend_action", "tests/test_combat_system.py::TestCombatSystem::test_character_flee_action", "tests/test_combat_system.py::TestCombatSystem::test_combat_initialization", "tests/test_combat_system.py::TestCombatSystem::test_combat_initialization_invalid_enemy", "tests/test_combat_system.py::TestCombatSystem::test_combat_status_tracking", "tests/test_combat_system.py::TestCombatSystem::test_combat_system_initialization", "tests/test_combat_system.py::TestCombatSystem::test_combat_victory_handling", "tests/test_combat_system.py::TestCombatSystem::test_damage_calculation", "tests/test_combat_system.py::TestCombatSystem::test_enemy_ai_behavior", "tests/test_combat_system.py::TestCombatSystem::test_enemy_creation", "tests/test_combat_system.py::TestCombatSystem::test_enemy_serialization", "tests/test_combat_system.py::TestCombatSystem::test_multiple_enemy_types", "tests/test_combat_system.py::TestCombatSystem::test_skill_usage_in_combat", "tests/test_difficulty_system.py::TestDifficultyAdjustmentSystem::test_action_and_skill_tracking", "tests/test_difficulty_system.py::TestDifficultyAdjustmentSystem::test_combat_modifier_application", "tests/test_difficulty_system.py::TestDifficultyAdjustmentSystem::test_combat_result_limit", "tests/test_difficulty_system.py::TestDifficultyAdjustmentSystem::test_combat_result_recording", "tests/test_difficulty_system.py::TestDifficultyAdjustmentSystem::test_death_recording", "tests/test_difficulty_system.py::TestDifficultyAdjustmentSystem::test_difficulty_adjustment_insufficient_data", "tests/test_difficulty_system.py::TestDifficultyAdjustmentSystem::test_difficulty_adjustment_too_easy", "tests/test_difficulty_system.py::TestDifficultyAdjustmentSystem::test_difficulty_adjustment_too_hard", "tests/test_difficulty_system.py::TestDifficultyAdjustmentSystem::test_difficulty_modifiers", "tests/test_difficulty_system.py::TestDifficultyAdjustmentSystem::test_difficulty_system_initialization", "tests/test_difficulty_system.py::TestDifficultyAdjustmentSystem::test_location_discovery", "tests/test_difficulty_system.py::TestDifficultyAdjustmentSystem::test_performance_metrics_calculation", "tests/test_difficulty_system.py::TestDifficultyAdjustmentSystem::test_performance_summary", "tests/test_difficulty_system.py::TestDifficultyAdjustmentSystem::test_quest_completion_tracking", "tests/test_difficulty_system.py::TestDifficultyAdjustmentSystem::test_reward_modifier_application", "tests/test_difficulty_system.py::TestDifficultyAdjustmentSystem::test_serialization", "tests/test_difficulty_system.py::TestDifficultyAdjustmentSystem::test_skill_modifier_application", "tests/test_enhanced_character_creation.py::TestEnhancedCharacterCreation::test_backstory_selection_invalid", "tests/test_enhanced_character_creation.py::TestEnhancedCharacterCreation::test_backstory_selection_numeric", "tests/test_enhanced_character_creation.py::TestEnhancedCharacterCreation::test_backstory_selection_random", "tests/test_enhanced_character_creation.py::TestEnhancedCharacterCreation::test_creature_selection_by_name", "tests/test_enhanced_character_creation.py::TestEnhancedCharacterCreation::test_creature_selection_invalid", "tests/test_enhanced_character_creation.py::TestEnhancedCharacterCreation::test_creature_selection_numeric", "tests/test_enhanced_character_creation.py::TestEnhancedCharacterCreation::test_enhanced_creature_selection", "tests/test_enhanced_character_creation.py::TestEnhancedCharacterCreation::test_new_creature_stats", "tests/test_enhanced_character_creation.py::TestEnhancedCharacterCreation::test_start_new_game_shows_backstory", "tests/test_enhanced_character_creation.py::TestEvolutionSystemIntegration::test_new_creature_evolution_paths", "tests/test_enhanced_character_creation.py::TestExpandedCreatures::test_creature_info_completeness", "tests/test_enhanced_character_creation.py::TestExpandedCreatures::test_creatures_database_structure", "tests/test_enhanced_character_creation.py::TestExpandedCreatures::test_get_creature_info", "tests/test_enhanced_character_creation.py::TestExpandedCreatures::test_new_creatures_in_config", "tests/test_enhanced_character_creation.py::TestLoreSystem::test_get_all_backstory_names", "tests/test_enhanced_character_creation.py::TestLoreSystem::test_get_backstory_by_name", "tests/test_enhanced_character_creation.py::TestLoreSystem::test_get_random_backstory", "tests/test_enhanced_character_creation.py::TestLoreSystem::test_lore_system_initialization", "tests/test_enhanced_character_creation.py::TestSkillFusionIntegration::test_new_creature_fusion_recipes", "tests/test_enhanced_character_creation.py::TestSkillFusionIntegration::test_new_creature_skills_in_database", "tests/test_enhanced_skill_learning.py::TestEnhancedSkillLearning::test_beginner_skill_prioritization", "tests/test_enhanced_skill_learning.py::TestEnhancedSkillLearning::test_can_learn_skill", "tests/test_enhanced_skill_learning.py::TestEnhancedSkillLearning::test_enhanced_skill_hints", "tests/test_enhanced_skill_learning.py::TestEnhancedSkillLearning::test_prerequisite_hints", "tests/test_enhanced_skill_learning.py::TestEnhancedSkillLearning::test_progressive_scaling", "tests/test_enhanced_skill_learning.py::TestEnhancedSkillLearning::test_serialization_enhanced", "tests/test_enhanced_skill_learning.py::TestEnhancedSkillLearning::test_skill_prerequisites", "tests/test_enhanced_skill_learning.py::TestEnhancedSkillLearning::test_specialization_paths", "tests/test_enhanced_skill_learning.py::TestEnhancedSkillLearning::test_specialization_status", "tests/test_enhanced_skill_learning.py::TestEnhancedSkillLearning::test_tiered_learning_system", "tests/test_enhanced_skill_learning.py::TestEnhancedSkillLearning::test_tutorial_boost", "tests/test_memory_system.py::TestMemorySystem::test_current_context_updates", "tests/test_memory_system.py::TestMemorySystem::test_important_events", "tests/test_memory_system.py::TestMemorySystem::test_location_context", "tests/test_memory_system.py::TestMemorySystem::test_location_discovery", "tests/test_memory_system.py::TestMemorySystem::test_lore_learning", "tests/test_memory_system.py::TestMemorySystem::test_memory_initialization", "tests/test_memory_system.py::TestMemorySystem::test_memory_summaries", "tests/test_memory_system.py::TestMemorySystem::test_npc_context", "tests/test_memory_system.py::TestMemorySystem::test_npc_interactions", "tests/test_memory_system.py::TestMemorySystem::test_quest_completion", "tests/test_memory_system.py::TestMemorySystem::test_serialization", "tests/test_memory_system.py::TestMemorySystem::test_short_term_memory", "tests/test_memory_system.py::TestMemorySystem::test_world_changes", "tests/test_portrait_integration.py::TestPortraitIntegration::test_character_creation_triggers_portrait_generation", "tests/test_portrait_integration.py::TestPortraitIntegration::test_evolution_triggers_portrait_generation", "tests/test_portrait_integration.py::TestPortraitIntegration::test_full_character_creation_flow", "tests/test_portrait_integration.py::TestPortraitIntegration::test_game_engine_has_portrait_system", "tests/test_portrait_integration.py::TestPortraitIntegration::test_get_character_portrait_disabled", "tests/test_portrait_integration.py::TestPortraitIntegration::test_get_character_portrait_method", "tests/test_portrait_integration.py::TestPortraitIntegration::test_get_character_portrait_no_character", "tests/test_portrait_integration.py::TestPortraitIntegration::test_portrait_caching_integration", "tests/test_portrait_integration.py::TestPortraitIntegration::test_portrait_queue_integration", "tests/test_portrait_integration.py::TestPortraitIntegration::test_portrait_system_info", "tests/test_portrait_integration.py::TestPortraitIntegration::test_portrait_system_with_different_creatures", "tests/test_portrait_system.py::TestPortraitSystem::test_backstory_illustration_disabled", "tests/test_portrait_system.py::TestPortraitSystem::test_build_backstory_prompt", "tests/test_portrait_system.py::TestPortraitSystem::test_cache_operations", "tests/test_portrait_system.py::TestPortraitSystem::test_disabled_portrait_generation", "tests/test_portrait_system.py::TestPortraitSystem::test_disabled_preload", "tests/test_portrait_system.py::TestPortraitSystem::test_generate_evolution_portraits", "tests/test_portrait_system.py::TestPortraitSystem::test_get_character_portrait_with_cache", "tests/test_portrait_system.py::TestPortraitSystem::test_get_evolution_paths", "tests/test_portrait_system.py::TestPortraitSystem::test_portrait_system_initialization", "tests/test_portrait_system.py::TestPortraitSystem::test_portrait_system_integration_with_character", "tests/test_portrait_system.py::TestPortraitSystem::test_preload_character_portraits", "tests/test_portrait_system.py::TestPortraitSystem::test_queue_portrait_generation", "tests/test_response_consistency.py::TestResponseConsistency::test_analyze_character_state", "tests/test_response_consistency.py::TestResponseConsistency::test_build_consistent_prompt", "tests/test_response_consistency.py::TestResponseConsistency::test_build_enhanced_context", "tests/test_response_consistency.py::TestResponseConsistency::test_build_narrative_continuity", "tests/test_response_consistency.py::TestResponseConsistency::test_dynamic_prompt_building", "tests/test_response_consistency.py::TestResponseConsistency::test_post_process_response_basic_cleanup", "tests/test_response_consistency.py::TestResponseConsistency::test_post_process_response_capitalization", "tests/test_response_consistency.py::TestResponseConsistency::test_post_process_response_length_validation", "tests/test_skill_learning_system.py::TestSkillLearningSystem::test_action_analysis_for_learning", "tests/test_skill_learning_system.py::TestSkillLearningSystem::test_action_recording", "tests/test_skill_learning_system.py::TestSkillLearningSystem::test_combat_victory_learning", "tests/test_skill_learning_system.py::TestSkillLearningSystem::test_creature_specific_learning", "tests/test_skill_learning_system.py::TestSkillLearningSystem::test_learning_conditions", "tests/test_skill_learning_system.py::TestSkillLearningSystem::test_multiple_skill_learning_triggers", "tests/test_skill_learning_system.py::TestSkillLearningSystem::test_serialization", "tests/test_skill_learning_system.py::TestSkillLearningSystem::test_skill_filtering", "tests/test_skill_learning_system.py::TestSkillLearningSystem::test_skill_learning_hints", "tests/test_skill_learning_system.py::TestSkillLearningSystem::test_skill_learning_system_initialization", "tests/test_skill_system.py::TestSkillFusionSystem::test_basic_skill_creation", "tests/test_skill_system.py::TestSkillFusionSystem::test_complex_fusion_combinations", "tests/test_skill_system.py::TestSkillFusionSystem::test_creature_specific_fusion", "tests/test_skill_system.py::TestSkillFusionSystem::test_failed_fusion_missing_components", "tests/test_skill_system.py::TestSkillFusionSystem::test_fusion_hints", "tests/test_skill_system.py::TestSkillFusionSystem::test_fusion_possibility_detection", "tests/test_skill_system.py::TestSkillFusionSystem::test_fusion_recipe_validation", "tests/test_skill_system.py::TestSkillFusionSystem::test_skill_database_access", "tests/test_skill_system.py::TestSkillFusionSystem::test_skill_effects_and_requirements", "tests/test_skill_system.py::TestSkillFusionSystem::test_skill_fusion_system_initialization", "tests/test_skill_system.py::TestSkillFusionSystem::test_skill_serialization", "tests/test_skill_system.py::TestSkillFusionSystem::test_successful_fusion"]