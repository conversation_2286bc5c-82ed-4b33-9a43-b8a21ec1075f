{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://contactcenterinsights.googleapis.com/", "batchPath": "batch", "canonicalName": "Contactcenterinsights", "description": "", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/contact-center/insights/docs", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "contactcenterinsights:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://contactcenterinsights.mtls.googleapis.com/", "name": "contactcenterinsights", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"bulkDeleteFeedbackLabels": {"description": "Delete feedback labels in bulk using a filter.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}:bulkDeleteFeedbackLabels", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.bulkDeleteFeedbackLabels", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource for new feedback labels.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}:bulkDeleteFeedbackLabels", "request": {"$ref": "GoogleCloudContactcenterinsightsV1BulkDeleteFeedbackLabelsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "bulkDownloadFeedbackLabels": {"description": "Download feedback labels in bulk from an external source. Currently supports exporting Quality AI example conversations with transcripts and question bodies.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}:bulkDownloadFeedbackLabels", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.bulkDownloadFeedbackLabels", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource for new feedback labels.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}:bulkDownloadFeedbackLabels", "request": {"$ref": "GoogleCloudContactcenterinsightsV1BulkDownloadFeedbackLabelsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "bulkUploadFeedbackLabels": {"description": "Upload feedback labels from an external source in bulk. Currently supports labeling Quality AI example conversations.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}:bulkUploadFeedbackLabels", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.bulkUploadFeedbackLabels", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource for new feedback labels.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}:bulkUploadFeedbackLabels", "request": {"$ref": "GoogleCloudContactcenterinsightsV1BulkUploadFeedbackLabelsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getEncryptionSpec": {"description": "Gets location-level encryption key specification.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/encryptionSpec", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.getEncryptionSpec", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the encryption spec resource to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/encryptionSpec$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContactcenterinsightsV1EncryptionSpec"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getSettings": {"description": "Gets project-level settings.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/settings", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.getSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the settings resource to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/settings$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContactcenterinsightsV1Settings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "listAllFeedbackLabels": {"description": "List all feedback labels by project number.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}:listAllFeedbackLabels", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.listAllFeedbackLabels", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter to reduce results to a specific subset in the entire project. Supports disjunctions (OR) and conjunctions (AND). Supported fields: * `issue_model_id` * `qa_question_id` * `min_create_time` * `max_create_time` * `min_update_time` * `max_update_time` * `feedback_label_type`: QUALITY_AI, TOPIC_MODELING", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of feedback labels to return in the response. A valid page size ranges from 0 to 100,000 inclusive. If the page size is zero or unspecified, a default page size of 100 will be chosen. Note that a call might return fewer results than the requested page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The value returned by the last `ListAllFeedbackLabelsResponse`. This value indicates that this is a continuation of a prior `ListAllFeedbackLabels` call and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of all feedback labels per project.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}:listAllFeedbackLabels", "response": {"$ref": "GoogleCloudContactcenterinsightsV1ListAllFeedbackLabelsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "queryMetrics": {"description": "Query metrics.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}:queryMetrics", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.queryMetrics", "parameterOrder": ["location"], "parameters": {"location": {"description": "Required. The location of the data. \"projects/{project}/locations/{location}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+location}:queryMetrics", "request": {"$ref": "GoogleCloudContactcenterinsightsV1QueryMetricsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateSettings": {"description": "Updates project-level settings.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/settings", "httpMethod": "PATCH", "id": "contactcenterinsights.projects.locations.updateSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. The resource name of the settings resource. Format: projects/{project}/locations/{location}/settings", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/settings$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudContactcenterinsightsV1Settings"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1Settings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"analysisRules": {"methods": {"create": {"description": "Creates a analysis rule.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/analysisRules", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.analysisRules.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the analysis rule. Required. The location to create a analysis rule for. Format: `projects//locations/` or `projects//locations/`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/analysisRules", "request": {"$ref": "GoogleCloudContactcenterinsightsV1AnalysisRule"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1AnalysisRule"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a analysis rule.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/analysisRules/{analysisRulesId}", "httpMethod": "DELETE", "id": "contactcenterinsights.projects.locations.analysisRules.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the analysis rule to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/analysisRules/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get a analysis rule.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/analysisRules/{analysisRulesId}", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.analysisRules.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the AnalysisRule to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/analysisRules/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContactcenterinsightsV1AnalysisRule"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists analysis rules.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/analysisRules", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.analysisRules.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of analysis rule to return in the response. If this value is zero, the service will select a default size. A call may return fewer objects than requested. A non-empty `next_page_token` in the response indicates that more data is available.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The value returned by the last `ListAnalysisRulesResponse`; indicates that this is a continuation of a prior `ListAnalysisRules` call and the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the analysis rules.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/analysisRules", "response": {"$ref": "GoogleCloudContactcenterinsightsV1ListAnalysisRulesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a analysis rule.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/analysisRules/{analysisRulesId}", "httpMethod": "PATCH", "id": "contactcenterinsights.projects.locations.analysisRules.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the analysis rule. Format: projects/{project}/locations/{location}/analysisRules/{analysis_rule}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/analysisRules/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. The list of fields to be updated. If the update_mask is not provided, the update will be applied to all fields.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudContactcenterinsightsV1AnalysisRule"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1AnalysisRule"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "authorizedViewSets": {"methods": {"create": {"description": "Create AuthorizedViewSet", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.authorizedViewSets.create", "parameterOrder": ["parent"], "parameters": {"authorizedViewSetId": {"description": "Optional. A unique ID for the new AuthorizedViewSet. This ID will become the final component of the AuthorizedViewSet's resource name. If no ID is specified, a server-generated ID will be used. This value should be 4-64 characters and must match the regular expression `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`. See go/aip/122#resource-id-segments", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the AuthorizedViewSet.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/authorizedViewSets", "request": {"$ref": "GoogleCloudContactcenterinsightsV1AuthorizedViewSet"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1AuthorizedViewSet"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an AuthorizedViewSet.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}", "httpMethod": "DELETE", "id": "contactcenterinsights.projects.locations.authorizedViewSets.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "Optional. If set to true, all of this AuthorizedViewSet's child resources will also be deleted. Otherwise, the request will only succeed if it has none.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The name of the AuthorizedViewSet to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get AuthorizedViewSet", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.authorizedViewSets.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the AuthorizedViewSet to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContactcenterinsightsV1AuthorizedViewSet"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List AuthorizedViewSets", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.authorizedViewSets.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. The filter expression to filter authorized view sets listed in the response.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. The order by expression to order authorized view sets listed in the response.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of view sets to return in the response. If the value is zero, the service will select a default size. A call might return fewer objects than requested. A non-empty `next_page_token` in the response indicates that more data is available.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The value returned by the last `ListAuthorizedViewSetsResponse`. This value indicates that this is a continuation of a prior `ListAuthorizedViewSets` call and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the AuthorizedViewSets.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/authorizedViewSets", "response": {"$ref": "GoogleCloudContactcenterinsightsV1ListAuthorizedViewSetsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an AuthorizedViewSet.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}", "httpMethod": "PATCH", "id": "contactcenterinsights.projects.locations.authorizedViewSets.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the AuthorizedViewSet. Format: projects/{project}/locations/{location}/authorizedViewSets/{authorized_view_set}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. The list of fields to be updated. All possible fields can be updated by passing `*`, or a subset of the following updateable fields can be provided: * `display_name`", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudContactcenterinsightsV1AuthorizedViewSet"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1AuthorizedViewSet"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"authorizedViews": {"methods": {"create": {"description": "Create AuthorizedView", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.create", "parameterOrder": ["parent"], "parameters": {"authorizedViewId": {"description": "Optional. A unique ID for the new AuthorizedView. This ID will become the final component of the AuthorizedView's resource name. If no ID is specified, a server-generated ID will be used. This value should be 4-64 characters and must match the regular expression `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`. See go/aip/122#resource-id-segments", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the AuthorizedView.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/authorizedViews", "request": {"$ref": "GoogleCloudContactcenterinsightsV1AuthorizedView"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1AuthorizedView"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an AuthorizedView.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews/{authorizedViewsId}", "httpMethod": "DELETE", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the AuthorizedView to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+/authorizedViews/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get AuthorizedView", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews/{authorizedViewsId}", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the AuthorizedView to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+/authorizedViews/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContactcenterinsightsV1AuthorizedView"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List AuthorizedViewSets", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. The filter expression to filter authorized views listed in the response.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. The order by expression to order authorized views listed in the response.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of view to return in the response. If the value is zero, the service will select a default size. A call might return fewer objects than requested. A non-empty `next_page_token` in the response indicates that more data is available.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The value returned by the last `ListAuthorizedViewsResponse`. This value indicates that this is a continuation of a prior `ListAuthorizedViews` call and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the AuthorizedViews. If the parent is set to `-`, all AuthorizedViews under the location will be returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/authorizedViews", "response": {"$ref": "GoogleCloudContactcenterinsightsV1ListAuthorizedViewsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an AuthorizedView.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews/{authorizedViewsId}", "httpMethod": "PATCH", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the AuthorizedView. Format: projects/{project}/locations/{location}/authorizedViewSets/{authorized_view_set}/authorizedViews/{authorized_view}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+/authorizedViews/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. The list of fields to be updated. All possible fields can be updated by passing `*`, or a subset of the following updateable fields can be provided: * `conversation_filter` * `display_name`", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudContactcenterinsightsV1AuthorizedView"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1AuthorizedView"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "queryMetrics": {"description": "Query metrics.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews/{authorizedViewsId}:queryMetrics", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.queryMetrics", "parameterOrder": ["location"], "parameters": {"location": {"description": "Required. The location of the data. \"projects/{project}/locations/{location}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+/authorizedViews/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+location}:queryMetrics", "request": {"$ref": "GoogleCloudContactcenterinsightsV1QueryMetricsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "search": {"description": "SearchAuthorizedViewSets", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews:search", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.search", "parameterOrder": ["parent"], "parameters": {"orderBy": {"description": "Optional. The order by expression to order authorized views listed in the response.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of view to return in the response. If the value is zero, the service will select a default size. A call might return fewer objects than requested. A non-empty `next_page_token` in the response indicates that more data is available.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The value returned by the last `ListAuthorizedViewsResponse`. This value indicates that this is a continuation of a prior `ListAuthorizedViews` call and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the AuthorizedViews. If the parent is set to `-`, all AuthorizedViews under the location will be returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+$", "required": true, "type": "string"}, "query": {"description": "Optional. The query expression to search authorized views.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/authorizedViews:search", "response": {"$ref": "GoogleCloudContactcenterinsightsV1SearchAuthorizedViewsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"conversations": {"methods": {"bulkAnalyze": {"description": "Analyzes multiple conversations in a single request.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews/{authorizedViewsId}/conversations:bulkAnalyze", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.conversations.bulkAnalyze", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource to create analyses in.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+/authorizedViews/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/conversations:bulkAnalyze", "request": {"$ref": "GoogleCloudContactcenterinsightsV1BulkAnalyzeConversationsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "calculateStats": {"description": "Gets conversation statistics.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews/{authorizedViewsId}/conversations:calculateStats", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.conversations.calculateStats", "parameterOrder": ["location"], "parameters": {"filter": {"description": "A filter to reduce results to a specific subset. This field is useful for getting statistics about conversations with specific properties.", "location": "query", "type": "string"}, "location": {"description": "Required. The location of the conversations.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+/authorizedViews/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+location}/conversations:calculateStats", "response": {"$ref": "GoogleCloudContactcenterinsightsV1CalculateStatsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a conversation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews/{authorizedViewsId}/conversations/{conversationsId}", "httpMethod": "DELETE", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.conversations.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "If set to true, all of this conversation's analyses will also be deleted. Otherwise, the request will only succeed if the conversation has no analyses.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The name of the conversation to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+/authorizedViews/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a conversation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews/{authorizedViewsId}/conversations/{conversationsId}", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.conversations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the conversation to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+/authorizedViews/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}, "view": {"description": "The level of details of the conversation. De<PERSON>ult is `FULL`.", "enum": ["CONVERSATION_VIEW_UNSPECIFIED", "FULL", "BASIC"], "enumDescriptions": ["The conversation view is not specified. * Defaults to `FULL` in `GetConversationRequest`. * Defaults to `BASIC` in `ListConversationsRequest`.", "Populates all fields in the conversation.", "Populates all fields in the conversation except the transcript."], "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContactcenterinsightsV1Conversation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists conversations.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews/{authorizedViewsId}/conversations", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.conversations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter to reduce results to a specific subset. Useful for querying conversations with specific properties.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. The attribute by which to order conversations in the response. If empty, conversations will be ordered by descending creation time. Supported values are one of the following: * create_time * customer_satisfaction_rating * duration * latest_analysis * start_time * turn_count The default sort order is ascending. To specify order, append `asc` or `desc` (`create_time desc`). For more details, see [Google AIPs Ordering](https://google.aip.dev/132#ordering).", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of conversations to return in the response. A valid page size ranges from 0 to 100,000 inclusive. If the page size is zero or unspecified, a default page size of 100 will be chosen. Note that a call might return fewer results than the requested page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListConversationsResponse`. This value indicates that this is a continuation of a prior `ListConversations` call and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the conversation.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+/authorizedViews/[^/]+$", "required": true, "type": "string"}, "view": {"description": "The level of details of the conversation. <PERSON><PERSON><PERSON> is `BASIC`.", "enum": ["CONVERSATION_VIEW_UNSPECIFIED", "FULL", "BASIC"], "enumDescriptions": ["The conversation view is not specified. * Defaults to `FULL` in `GetConversationRequest`. * Defaults to `BASIC` in `ListConversationsRequest`.", "Populates all fields in the conversation.", "Populates all fields in the conversation except the transcript."], "location": "query", "type": "string"}}, "path": "v1/{+parent}/conversations", "response": {"$ref": "GoogleCloudContactcenterinsightsV1ListConversationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"analyses": {"methods": {"create": {"description": "Creates an analysis. The long running operation is done when the analysis has completed.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews/{authorizedViewsId}/conversations/{conversationsId}/analyses", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.conversations.analyses.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the analysis.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+/authorizedViews/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/analyses", "request": {"$ref": "GoogleCloudContactcenterinsightsV1Analysis"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an analysis.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews/{authorizedViewsId}/conversations/{conversationsId}/analyses/{analysesId}", "httpMethod": "DELETE", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.conversations.analyses.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the analysis to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+/authorizedViews/[^/]+/conversations/[^/]+/analyses/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an analysis.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews/{authorizedViewsId}/conversations/{conversationsId}/analyses/{analysesId}", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.conversations.analyses.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the analysis to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+/authorizedViews/[^/]+/conversations/[^/]+/analyses/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContactcenterinsightsV1Analysis"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists analyses.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews/{authorizedViewsId}/conversations/{conversationsId}/analyses", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.conversations.analyses.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter to reduce results to a specific subset. Useful for querying conversations with specific properties.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of analyses to return in the response. If this value is zero, the service will select a default size. A call might return fewer objects than requested. A non-empty `next_page_token` in the response indicates that more data is available.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListAnalysesResponse`; indicates that this is a continuation of a prior `ListAnalyses` call and the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the analyses.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+/authorizedViews/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/analyses", "response": {"$ref": "GoogleCloudContactcenterinsightsV1ListAnalysesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "feedbackLabels": {"methods": {"create": {"description": "Create feedback label.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews/{authorizedViewsId}/conversations/{conversationsId}/feedbackLabels", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.conversations.feedbackLabels.create", "parameterOrder": ["parent"], "parameters": {"feedbackLabelId": {"description": "Optional. The ID of the feedback label to create. If one is not specified it will be generated by the server.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the feedback label.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+/authorizedViews/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/feedbackLabels", "request": {"$ref": "GoogleCloudContactcenterinsightsV1FeedbackLabel"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1FeedbackLabel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete feedback label.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews/{authorizedViewsId}/conversations/{conversationsId}/feedbackLabels/{feedbackLabelsId}", "httpMethod": "DELETE", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.conversations.feedbackLabels.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the feedback label to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+/authorizedViews/[^/]+/conversations/[^/]+/feedbackLabels/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get feedback label.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews/{authorizedViewsId}/conversations/{conversationsId}/feedbackLabels/{feedbackLabelsId}", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.conversations.feedbackLabels.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the feedback label to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+/authorizedViews/[^/]+/conversations/[^/]+/feedbackLabels/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContactcenterinsightsV1FeedbackLabel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List feedback labels.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews/{authorizedViewsId}/conversations/{conversationsId}/feedbackLabels", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.conversations.feedbackLabels.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter to reduce results to a specific subset. Supports disjunctions (OR) and conjunctions (AND). Automatically sorts by conversation ID. To sort by all feedback labels in a project see ListAllFeedbackLabels. Supported fields: * `issue_model_id` * `qa_question_id` * `qa_scorecard_id` * `min_create_time` * `max_create_time` * `min_update_time` * `max_update_time` * `feedback_label_type`: QUALITY_AI, TOPIC_MODELING", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of feedback labels to return in the response. A valid page size ranges from 0 to 100,000 inclusive. If the page size is zero or unspecified, a default page size of 100 will be chosen. Note that a call might return fewer results than the requested page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The value returned by the last `ListFeedbackLabelsResponse`. This value indicates that this is a continuation of a prior `ListFeedbackLabels` call and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the feedback labels.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+/authorizedViews/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/feedbackLabels", "response": {"$ref": "GoogleCloudContactcenterinsightsV1ListFeedbackLabelsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update feedback label.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews/{authorizedViewsId}/conversations/{conversationsId}/feedbackLabels/{feedbackLabelsId}", "httpMethod": "PATCH", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.conversations.feedbackLabels.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Resource name of the FeedbackLabel. Format: projects/{project}/locations/{location}/conversations/{conversation}/feedbackLabels/{feedback_label}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+/authorizedViews/[^/]+/conversations/[^/]+/feedbackLabels/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudContactcenterinsightsV1FeedbackLabel"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1FeedbackLabel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews/{authorizedViewsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+/authorizedViews/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews/{authorizedViewsId}/operations/{operationsId}", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+/authorizedViews/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/authorizedViewSets/{authorizedViewSetsId}/authorizedViews/{authorizedViewsId}/operations", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.authorizedViewSets.authorizedViews.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/authorizedViewSets/[^/]+/authorizedViews/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}, "conversations": {"methods": {"bulkAnalyze": {"description": "Analyzes multiple conversations in a single request.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversations:bulkAnalyze", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.conversations.bulkAnalyze", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource to create analyses in.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/conversations:bulkAnalyze", "request": {"$ref": "GoogleCloudContactcenterinsightsV1BulkAnalyzeConversationsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "bulkDelete": {"description": "Deletes multiple conversations in a single request.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversations:bulkDelete", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.conversations.bulkDelete", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource to delete conversations from. Format: projects/{project}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/conversations:bulkDelete", "request": {"$ref": "GoogleCloudContactcenterinsightsV1BulkDeleteConversationsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "calculateStats": {"description": "Gets conversation statistics.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversations:calculateStats", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.conversations.calculateStats", "parameterOrder": ["location"], "parameters": {"filter": {"description": "A filter to reduce results to a specific subset. This field is useful for getting statistics about conversations with specific properties.", "location": "query", "type": "string"}, "location": {"description": "Required. The location of the conversations.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+location}/conversations:calculateStats", "response": {"$ref": "GoogleCloudContactcenterinsightsV1CalculateStatsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a conversation. Note that this method does not support audio transcription or redaction. Use `conversations.upload` instead.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversations", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.conversations.create", "parameterOrder": ["parent"], "parameters": {"conversationId": {"description": "A unique ID for the new conversation. This ID will become the final component of the conversation's resource name. If no ID is specified, a server-generated ID will be used. This value should be 4-64 characters and must match the regular expression `^[a-z0-9-]{4,64}$`. Valid characters are `a-z-`", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the conversation.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/conversations", "request": {"$ref": "GoogleCloudContactcenterinsightsV1Conversation"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1Conversation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a conversation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}", "httpMethod": "DELETE", "id": "contactcenterinsights.projects.locations.conversations.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "If set to true, all of this conversation's analyses will also be deleted. Otherwise, the request will only succeed if the conversation has no analyses.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The name of the conversation to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a conversation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.conversations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the conversation to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}, "view": {"description": "The level of details of the conversation. De<PERSON>ult is `FULL`.", "enum": ["CONVERSATION_VIEW_UNSPECIFIED", "FULL", "BASIC"], "enumDescriptions": ["The conversation view is not specified. * Defaults to `FULL` in `GetConversationRequest`. * Defaults to `BASIC` in `ListConversationsRequest`.", "Populates all fields in the conversation.", "Populates all fields in the conversation except the transcript."], "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContactcenterinsightsV1Conversation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "ingest": {"description": "Imports conversations and processes them according to the user's configuration.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversations:ingest", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.conversations.ingest", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource for new conversations.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/conversations:ingest", "request": {"$ref": "GoogleCloudContactcenterinsightsV1IngestConversationsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists conversations.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversations", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.conversations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter to reduce results to a specific subset. Useful for querying conversations with specific properties.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. The attribute by which to order conversations in the response. If empty, conversations will be ordered by descending creation time. Supported values are one of the following: * create_time * customer_satisfaction_rating * duration * latest_analysis * start_time * turn_count The default sort order is ascending. To specify order, append `asc` or `desc` (`create_time desc`). For more details, see [Google AIPs Ordering](https://google.aip.dev/132#ordering).", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of conversations to return in the response. A valid page size ranges from 0 to 100,000 inclusive. If the page size is zero or unspecified, a default page size of 100 will be chosen. Note that a call might return fewer results than the requested page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListConversationsResponse`. This value indicates that this is a continuation of a prior `ListConversations` call and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the conversation.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "view": {"description": "The level of details of the conversation. <PERSON><PERSON><PERSON> is `BASIC`.", "enum": ["CONVERSATION_VIEW_UNSPECIFIED", "FULL", "BASIC"], "enumDescriptions": ["The conversation view is not specified. * Defaults to `FULL` in `GetConversationRequest`. * Defaults to `BASIC` in `ListConversationsRequest`.", "Populates all fields in the conversation.", "Populates all fields in the conversation except the transcript."], "location": "query", "type": "string"}}, "path": "v1/{+parent}/conversations", "response": {"$ref": "GoogleCloudContactcenterinsightsV1ListConversationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a conversation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}", "httpMethod": "PATCH", "id": "contactcenterinsights.projects.locations.conversations.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. The resource name of the conversation. Format: projects/{project}/locations/{location}/conversations/{conversation}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated. All possible fields can be updated by passing `*`, or a subset of the following updateable fields can be provided: * `agent_id` * `language_code` * `labels` * `metadata` * `quality_metadata` * `call_metadata` * `start_time` * `expire_time` or `ttl` * `data_source.gcs_source.audio_uri` or `data_source.dialogflow_source.audio_uri`", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudContactcenterinsightsV1Conversation"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1Conversation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "upload": {"description": "Create a long-running conversation upload operation. This method differs from `CreateConversation` by allowing audio transcription and optional DLP redaction.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversations:upload", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.conversations.upload", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the conversation.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/conversations:upload", "request": {"$ref": "GoogleCloudContactcenterinsightsV1UploadConversationRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"analyses": {"methods": {"create": {"description": "Creates an analysis. The long running operation is done when the analysis has completed.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}/analyses", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.conversations.analyses.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the analysis.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/analyses", "request": {"$ref": "GoogleCloudContactcenterinsightsV1Analysis"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an analysis.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}/analyses/{analysesId}", "httpMethod": "DELETE", "id": "contactcenterinsights.projects.locations.conversations.analyses.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the analysis to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversations/[^/]+/analyses/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an analysis.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}/analyses/{analysesId}", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.conversations.analyses.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the analysis to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversations/[^/]+/analyses/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContactcenterinsightsV1Analysis"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists analyses.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}/analyses", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.conversations.analyses.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter to reduce results to a specific subset. Useful for querying conversations with specific properties.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of analyses to return in the response. If this value is zero, the service will select a default size. A call might return fewer objects than requested. A non-empty `next_page_token` in the response indicates that more data is available.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListAnalysesResponse`; indicates that this is a continuation of a prior `ListAnalyses` call and the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the analyses.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/analyses", "response": {"$ref": "GoogleCloudContactcenterinsightsV1ListAnalysesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "feedbackLabels": {"methods": {"create": {"description": "Create feedback label.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}/feedbackLabels", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.conversations.feedbackLabels.create", "parameterOrder": ["parent"], "parameters": {"feedbackLabelId": {"description": "Optional. The ID of the feedback label to create. If one is not specified it will be generated by the server.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the feedback label.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/feedbackLabels", "request": {"$ref": "GoogleCloudContactcenterinsightsV1FeedbackLabel"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1FeedbackLabel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete feedback label.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}/feedbackLabels/{feedbackLabelsId}", "httpMethod": "DELETE", "id": "contactcenterinsights.projects.locations.conversations.feedbackLabels.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the feedback label to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversations/[^/]+/feedbackLabels/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get feedback label.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}/feedbackLabels/{feedbackLabelsId}", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.conversations.feedbackLabels.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the feedback label to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversations/[^/]+/feedbackLabels/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContactcenterinsightsV1FeedbackLabel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List feedback labels.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}/feedbackLabels", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.conversations.feedbackLabels.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter to reduce results to a specific subset. Supports disjunctions (OR) and conjunctions (AND). Automatically sorts by conversation ID. To sort by all feedback labels in a project see ListAllFeedbackLabels. Supported fields: * `issue_model_id` * `qa_question_id` * `qa_scorecard_id` * `min_create_time` * `max_create_time` * `min_update_time` * `max_update_time` * `feedback_label_type`: QUALITY_AI, TOPIC_MODELING", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of feedback labels to return in the response. A valid page size ranges from 0 to 100,000 inclusive. If the page size is zero or unspecified, a default page size of 100 will be chosen. Note that a call might return fewer results than the requested page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The value returned by the last `ListFeedbackLabelsResponse`. This value indicates that this is a continuation of a prior `ListFeedbackLabels` call and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the feedback labels.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/feedbackLabels", "response": {"$ref": "GoogleCloudContactcenterinsightsV1ListFeedbackLabelsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update feedback label.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversations/{conversationsId}/feedbackLabels/{feedbackLabelsId}", "httpMethod": "PATCH", "id": "contactcenterinsights.projects.locations.conversations.feedbackLabels.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Resource name of the FeedbackLabel. Format: projects/{project}/locations/{location}/conversations/{conversation}/feedbackLabels/{feedback_label}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversations/[^/]+/feedbackLabels/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudContactcenterinsightsV1FeedbackLabel"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1FeedbackLabel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "datasets": {"methods": {"bulkDeleteFeedbackLabels": {"description": "Delete feedback labels in bulk using a filter.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}:bulkDeleteFeedbackLabels", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.datasets.bulkDeleteFeedbackLabels", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource for new feedback labels.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/datasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}:bulkDeleteFeedbackLabels", "request": {"$ref": "GoogleCloudContactcenterinsightsV1BulkDeleteFeedbackLabelsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "bulkDownloadFeedbackLabels": {"description": "Download feedback labels in bulk from an external source. Currently supports exporting Quality AI example conversations with transcripts and question bodies.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}:bulkDownloadFeedbackLabels", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.datasets.bulkDownloadFeedbackLabels", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource for new feedback labels.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/datasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}:bulkDownloadFeedbackLabels", "request": {"$ref": "GoogleCloudContactcenterinsightsV1BulkDownloadFeedbackLabelsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "bulkUploadFeedbackLabels": {"description": "Upload feedback labels from an external source in bulk. Currently supports labeling Quality AI example conversations.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}:bulkUploadFeedbackLabels", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.datasets.bulkUploadFeedbackLabels", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource for new feedback labels.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/datasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}:bulkUploadFeedbackLabels", "request": {"$ref": "GoogleCloudContactcenterinsightsV1BulkUploadFeedbackLabelsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "listAllFeedbackLabels": {"description": "List all feedback labels by project number.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}:listAllFeedbackLabels", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.datasets.listAllFeedbackLabels", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter to reduce results to a specific subset in the entire project. Supports disjunctions (OR) and conjunctions (AND). Supported fields: * `issue_model_id` * `qa_question_id` * `min_create_time` * `max_create_time` * `min_update_time` * `max_update_time` * `feedback_label_type`: QUALITY_AI, TOPIC_MODELING", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of feedback labels to return in the response. A valid page size ranges from 0 to 100,000 inclusive. If the page size is zero or unspecified, a default page size of 100 will be chosen. Note that a call might return fewer results than the requested page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The value returned by the last `ListAllFeedbackLabelsResponse`. This value indicates that this is a continuation of a prior `ListAllFeedbackLabels` call and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of all feedback labels per project.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/datasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}:listAllFeedbackLabels", "response": {"$ref": "GoogleCloudContactcenterinsightsV1ListAllFeedbackLabelsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"conversations": {"methods": {"bulkAnalyze": {"description": "Analyzes multiple conversations in a single request.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/conversations:bulkAnalyze", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.datasets.conversations.bulkAnalyze", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource to create analyses in.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/datasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/conversations:bulkAnalyze", "request": {"$ref": "GoogleCloudContactcenterinsightsV1BulkAnalyzeConversationsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "bulkDelete": {"description": "Deletes multiple conversations in a single request.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/conversations:bulkDelete", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.datasets.conversations.bulkDelete", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource to delete conversations from. Format: projects/{project}/locations/{location}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/datasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/conversations:bulkDelete", "request": {"$ref": "GoogleCloudContactcenterinsightsV1BulkDeleteConversationsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "calculateStats": {"description": "Gets conversation statistics.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/conversations:calculateStats", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.datasets.conversations.calculateStats", "parameterOrder": ["location"], "parameters": {"location": {"description": "Required. The location of the conversations.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/datasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+location}/conversations:calculateStats", "request": {"$ref": "GoogleCloudContactcenterinsightsV1CalculateStatsRequest"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1CalculateStatsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a conversation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/conversations/{conversationsId}", "httpMethod": "DELETE", "id": "contactcenterinsights.projects.locations.datasets.conversations.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "If set to true, all of this conversation's analyses will also be deleted. Otherwise, the request will only succeed if the conversation has no analyses.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The name of the conversation to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/datasets/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a conversation.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/conversations/{conversationsId}", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.datasets.conversations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the conversation to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/datasets/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}, "view": {"description": "The level of details of the conversation. De<PERSON>ult is `FULL`.", "enum": ["CONVERSATION_VIEW_UNSPECIFIED", "FULL", "BASIC"], "enumDescriptions": ["The conversation view is not specified. * Defaults to `FULL` in `GetConversationRequest`. * Defaults to `BASIC` in `ListConversationsRequest`.", "Populates all fields in the conversation.", "Populates all fields in the conversation except the transcript."], "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContactcenterinsightsV1Conversation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "ingest": {"description": "Imports conversations and processes them according to the user's configuration.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/conversations:ingest", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.datasets.conversations.ingest", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource for new conversations.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/datasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/conversations:ingest", "request": {"$ref": "GoogleCloudContactcenterinsightsV1IngestConversationsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists conversations.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/conversations", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.datasets.conversations.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter to reduce results to a specific subset. Useful for querying conversations with specific properties.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. The attribute by which to order conversations in the response. If empty, conversations will be ordered by descending creation time. Supported values are one of the following: * create_time * customer_satisfaction_rating * duration * latest_analysis * start_time * turn_count The default sort order is ascending. To specify order, append `asc` or `desc` (`create_time desc`). For more details, see [Google AIPs Ordering](https://google.aip.dev/132#ordering).", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of conversations to return in the response. A valid page size ranges from 0 to 100,000 inclusive. If the page size is zero or unspecified, a default page size of 100 will be chosen. Note that a call might return fewer results than the requested page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListConversationsResponse`. This value indicates that this is a continuation of a prior `ListConversations` call and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the conversation.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/datasets/[^/]+$", "required": true, "type": "string"}, "view": {"description": "The level of details of the conversation. <PERSON><PERSON><PERSON> is `BASIC`.", "enum": ["CONVERSATION_VIEW_UNSPECIFIED", "FULL", "BASIC"], "enumDescriptions": ["The conversation view is not specified. * Defaults to `FULL` in `GetConversationRequest`. * Defaults to `BASIC` in `ListConversationsRequest`.", "Populates all fields in the conversation.", "Populates all fields in the conversation except the transcript."], "location": "query", "type": "string"}}, "path": "v1/{+parent}/conversations", "response": {"$ref": "GoogleCloudContactcenterinsightsV1ListConversationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"analyses": {"methods": {"create": {"description": "Creates an analysis. The long running operation is done when the analysis has completed.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/conversations/{conversationsId}/analyses", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.datasets.conversations.analyses.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the analysis.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/datasets/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/analyses", "request": {"$ref": "GoogleCloudContactcenterinsightsV1Analysis"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an analysis.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/conversations/{conversationsId}/analyses/{analysesId}", "httpMethod": "DELETE", "id": "contactcenterinsights.projects.locations.datasets.conversations.analyses.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the analysis to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/datasets/[^/]+/conversations/[^/]+/analyses/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an analysis.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/conversations/{conversationsId}/analyses/{analysesId}", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.datasets.conversations.analyses.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the analysis to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/datasets/[^/]+/conversations/[^/]+/analyses/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContactcenterinsightsV1Analysis"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists analyses.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/conversations/{conversationsId}/analyses", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.datasets.conversations.analyses.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter to reduce results to a specific subset. Useful for querying conversations with specific properties.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of analyses to return in the response. If this value is zero, the service will select a default size. A call might return fewer objects than requested. A non-empty `next_page_token` in the response indicates that more data is available.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListAnalysesResponse`; indicates that this is a continuation of a prior `ListAnalyses` call and the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the analyses.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/datasets/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/analyses", "response": {"$ref": "GoogleCloudContactcenterinsightsV1ListAnalysesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "feedbackLabels": {"methods": {"create": {"description": "Create feedback label.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/conversations/{conversationsId}/feedbackLabels", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.datasets.conversations.feedbackLabels.create", "parameterOrder": ["parent"], "parameters": {"feedbackLabelId": {"description": "Optional. The ID of the feedback label to create. If one is not specified it will be generated by the server.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the feedback label.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/datasets/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/feedbackLabels", "request": {"$ref": "GoogleCloudContactcenterinsightsV1FeedbackLabel"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1FeedbackLabel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Delete feedback label.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/conversations/{conversationsId}/feedbackLabels/{feedbackLabelsId}", "httpMethod": "DELETE", "id": "contactcenterinsights.projects.locations.datasets.conversations.feedbackLabels.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the feedback label to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/datasets/[^/]+/conversations/[^/]+/feedbackLabels/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Get feedback label.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/conversations/{conversationsId}/feedbackLabels/{feedbackLabelsId}", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.datasets.conversations.feedbackLabels.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the feedback label to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/datasets/[^/]+/conversations/[^/]+/feedbackLabels/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContactcenterinsightsV1FeedbackLabel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "List feedback labels.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/conversations/{conversationsId}/feedbackLabels", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.datasets.conversations.feedbackLabels.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter to reduce results to a specific subset. Supports disjunctions (OR) and conjunctions (AND). Automatically sorts by conversation ID. To sort by all feedback labels in a project see ListAllFeedbackLabels. Supported fields: * `issue_model_id` * `qa_question_id` * `qa_scorecard_id` * `min_create_time` * `max_create_time` * `min_update_time` * `max_update_time` * `feedback_label_type`: QUALITY_AI, TOPIC_MODELING", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of feedback labels to return in the response. A valid page size ranges from 0 to 100,000 inclusive. If the page size is zero or unspecified, a default page size of 100 will be chosen. Note that a call might return fewer results than the requested page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The value returned by the last `ListFeedbackLabelsResponse`. This value indicates that this is a continuation of a prior `ListFeedbackLabels` call and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the feedback labels.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/datasets/[^/]+/conversations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/feedbackLabels", "response": {"$ref": "GoogleCloudContactcenterinsightsV1ListFeedbackLabelsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update feedback label.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/conversations/{conversationsId}/feedbackLabels/{feedbackLabelsId}", "httpMethod": "PATCH", "id": "contactcenterinsights.projects.locations.datasets.conversations.feedbackLabels.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. Resource name of the FeedbackLabel. Format: projects/{project}/locations/{location}/conversations/{conversation}/feedbackLabels/{feedback_label}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/datasets/[^/]+/conversations/[^/]+/feedbackLabels/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudContactcenterinsightsV1FeedbackLabel"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1FeedbackLabel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "insightsdata": {"methods": {"export": {"description": "Export insights data to a destination defined in the request body.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/insightsdata:export", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.datasets.insightsdata.export", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource to export data from.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/datasets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/insightsdata:export", "request": {"$ref": "GoogleCloudContactcenterinsightsV1ExportInsightsDataRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "encryptionSpec": {"methods": {"initialize": {"description": "Initializes a location-level encryption key specification. An error will result if the location has resources already created before the initialization. After the encryption specification is initialized at a location, it is immutable and all newly created resources under the location will be encrypted with the existing specification.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/encryptionSpec:initialize", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.encryptionSpec.initialize", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. The resource name of the encryption key specification resource. Format: projects/{project}/locations/{location}/encryptionSpec", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/encryptionSpec$", "required": true, "type": "string"}}, "path": "v1/{+name}:initialize", "request": {"$ref": "GoogleCloudContactcenterinsightsV1InitializeEncryptionSpecRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "insightsdata": {"methods": {"export": {"description": "Export insights data to a destination defined in the request body.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/insightsdata:export", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.insightsdata.export", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource to export data from.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/insightsdata:export", "request": {"$ref": "GoogleCloudContactcenterinsightsV1ExportInsightsDataRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "issueModels": {"methods": {"calculateIssueModelStats": {"description": "Gets an issue model's statistics.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/issueModels/{issueModelsId}:calculateIssueModelStats", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.issueModels.calculateIssueModelStats", "parameterOrder": ["issueModel"], "parameters": {"issueModel": {"description": "Required. The resource name of the issue model to query against.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/issueModels/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+issueModel}:calculateIssueModelStats", "response": {"$ref": "GoogleCloudContactcenterinsightsV1CalculateIssueModelStatsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates an issue model.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/issueModels", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.issueModels.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the issue model.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/issueModels", "request": {"$ref": "GoogleCloudContactcenterinsightsV1IssueModel"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an issue model.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/issueModels/{issueModelsId}", "httpMethod": "DELETE", "id": "contactcenterinsights.projects.locations.issueModels.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the issue model to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/issueModels/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "deploy": {"description": "Deploys an issue model. Returns an error if a model is already deployed. An issue model can only be used in analysis after it has been deployed.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/issueModels/{issueModelsId}:deploy", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.issueModels.deploy", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The issue model to deploy.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/issueModels/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:deploy", "request": {"$ref": "GoogleCloudContactcenterinsightsV1DeployIssueModelRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "export": {"description": "Exports an issue model to the provided destination.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/issueModels/{issueModelsId}:export", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.issueModels.export", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The issue model to export.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/issueModels/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:export", "request": {"$ref": "GoogleCloudContactcenterinsightsV1ExportIssueModelRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an issue model.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/issueModels/{issueModelsId}", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.issueModels.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the issue model to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/issueModels/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContactcenterinsightsV1IssueModel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "import": {"description": "Imports an issue model from a Cloud Storage bucket.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/issueModels:import", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.issueModels.import", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the issue model.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/issueModels:import", "request": {"$ref": "GoogleCloudContactcenterinsightsV1ImportIssueModelRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists issue models.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/issueModels", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.issueModels.list", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the issue model.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/issueModels", "response": {"$ref": "GoogleCloudContactcenterinsightsV1ListIssueModelsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an issue model.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/issueModels/{issueModelsId}", "httpMethod": "PATCH", "id": "contactcenterinsights.projects.locations.issueModels.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. The resource name of the issue model. Format: projects/{project}/locations/{location}/issueModels/{issue_model}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/issueModels/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudContactcenterinsightsV1IssueModel"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1IssueModel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "undeploy": {"description": "Undeploys an issue model. An issue model can not be used in analysis after it has been undeployed.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/issueModels/{issueModelsId}:undeploy", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.issueModels.undeploy", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The issue model to undeploy.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/issueModels/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:undeploy", "request": {"$ref": "GoogleCloudContactcenterinsightsV1UndeployIssueModelRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"issues": {"methods": {"create": {"description": "Creates an issue.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/issueModels/{issueModelsId}/issues", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.issueModels.issues.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the issue.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/issueModels/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/issues", "request": {"$ref": "GoogleCloudContactcenterinsightsV1Issue"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes an issue.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/issueModels/{issueModelsId}/issues/{issuesId}", "httpMethod": "DELETE", "id": "contactcenterinsights.projects.locations.issueModels.issues.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the issue to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/issueModels/[^/]+/issues/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets an issue.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/issueModels/{issueModelsId}/issues/{issuesId}", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.issueModels.issues.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the issue to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/issueModels/[^/]+/issues/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContactcenterinsightsV1Issue"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists issues.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/issueModels/{issueModelsId}/issues", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.issueModels.issues.list", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the issue.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/issueModels/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/issues", "response": {"$ref": "GoogleCloudContactcenterinsightsV1ListIssuesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an issue.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/issueModels/{issueModelsId}/issues/{issuesId}", "httpMethod": "PATCH", "id": "contactcenterinsights.projects.locations.issueModels.issues.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. The resource name of the issue. Format: projects/{project}/locations/{location}/issueModels/{issue_model}/issues/{issue}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/issueModels/[^/]+/issues/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudContactcenterinsightsV1Issue"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1Issue"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "phraseMatchers": {"methods": {"create": {"description": "Creates a phrase matcher.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/phraseMatchers", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.phraseMatchers.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the phrase matcher. Required. The location to create a phrase matcher for. Format: `projects//locations/` or `projects//locations/`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/phraseMatchers", "request": {"$ref": "GoogleCloudContactcenterinsightsV1PhraseMatcher"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1PhraseMatcher"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a phrase matcher.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/phraseMatchers/{phraseMatchersId}", "httpMethod": "DELETE", "id": "contactcenterinsights.projects.locations.phraseMatchers.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the phrase matcher to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/phraseMatchers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a phrase matcher.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/phraseMatchers/{phraseMatchersId}", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.phraseMatchers.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the phrase matcher to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/phraseMatchers/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContactcenterinsightsV1PhraseMatcher"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists phrase matchers.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/phraseMatchers", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.phraseMatchers.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter to reduce results to a specific subset. Useful for querying phrase matchers with specific properties.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of phrase matchers to return in the response. If this value is zero, the service will select a default size. A call might return fewer objects than requested. A non-empty `next_page_token` in the response indicates that more data is available.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListPhraseMatchersResponse`. This value indicates that this is a continuation of a prior `ListPhraseMatchers` call and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the phrase matcher.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/phraseMatchers", "response": {"$ref": "GoogleCloudContactcenterinsightsV1ListPhraseMatchersResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a phrase matcher.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/phraseMatchers/{phraseMatchersId}", "httpMethod": "PATCH", "id": "contactcenterinsights.projects.locations.phraseMatchers.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the phrase matcher. Format: projects/{project}/locations/{location}/phraseMatchers/{phrase_matcher}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/phraseMatchers/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudContactcenterinsightsV1PhraseMatcher"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1PhraseMatcher"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "qaScorecards": {"methods": {"create": {"description": "Create a QaScorecard.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/qaScorecards", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.qaScorecards.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the QaScorecard.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "qaScorecardId": {"description": "Optional. A unique ID for the new QaScorecard. This ID will become the final component of the QaScorecard's resource name. If no ID is specified, a server-generated ID will be used. This value should be 4-64 characters and must match the regular expression `^[a-z0-9-]{4,64}$`. Valid characters are `a-z-`.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/qaScorecards", "request": {"$ref": "GoogleCloudContactcenterinsightsV1QaScorecard"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1QaScorecard"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a QaScorecard.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/qaScorecards/{qaScorecardsId}", "httpMethod": "DELETE", "id": "contactcenterinsights.projects.locations.qaScorecards.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "Optional. If set to true, all of this QaScorecard's child resources will also be deleted. Otherwise, the request will only succeed if it has none.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The name of the QaScorecard to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/qaScorecards/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a QaScorecard.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/qaScorecards/{qaScorecardsId}", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.qaScorecards.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the QaScorecard to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/qaScorecards/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContactcenterinsightsV1QaScorecard"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists QaScorecards.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/qaScorecards", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.qaScorecards.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of scorecards to return in the response. If the value is zero, the service will select a default size. A call might return fewer objects than requested. A non-empty `next_page_token` in the response indicates that more data is available.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The value returned by the last `ListQaScorecardsResponse`. This value indicates that this is a continuation of a prior `ListQaScorecards` call and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the scorecards.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/qaScorecards", "response": {"$ref": "GoogleCloudContactcenterinsightsV1ListQaScorecardsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a QaScorecard.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/qaScorecards/{qaScorecardsId}", "httpMethod": "PATCH", "id": "contactcenterinsights.projects.locations.qaScorecards.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The scorecard name. Format: projects/{project}/locations/{location}/qaScorecards/{qa_scorecard}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/qaScorecards/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. All possible fields can be updated by passing `*`, or a subset of the following updateable fields can be provided: * `description` * `display_name`", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudContactcenterinsightsV1QaScorecard"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1QaScorecard"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"revisions": {"methods": {"create": {"description": "Creates a QaScorecardRevision.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/qaScorecards/{qaScorecardsId}/revisions", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.qaScorecards.revisions.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the QaScorecardRevision.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/qaScorecards/[^/]+$", "required": true, "type": "string"}, "qaScorecardRevisionId": {"description": "Optional. A unique ID for the new QaScorecardRevision. This ID will become the final component of the QaScorecardRevision's resource name. If no ID is specified, a server-generated ID will be used. This value should be 4-64 characters and must match the regular expression `^[a-z0-9-]{4,64}$`. Valid characters are `a-z-`.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/revisions", "request": {"$ref": "GoogleCloudContactcenterinsightsV1QaScorecardRevision"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1QaScorecardRevision"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a QaScorecardRevision.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/qaScorecards/{qaScorecardsId}/revisions/{revisionsId}", "httpMethod": "DELETE", "id": "contactcenterinsights.projects.locations.qaScorecards.revisions.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "Optional. If set to true, all of this QaScorecardRevision's child resources will also be deleted. Otherwise, the request will only succeed if it has none.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The name of the QaScorecardRevision to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/qaScorecards/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "deploy": {"description": "Deploy a QaScorecardRevision.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/qaScorecards/{qaScorecardsId}/revisions/{revisionsId}:deploy", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.qaScorecards.revisions.deploy", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the QaScorecardRevision to deploy.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/qaScorecards/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:deploy", "request": {"$ref": "GoogleCloudContactcenterinsightsV1DeployQaScorecardRevisionRequest"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1QaScorecardRevision"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a QaScorecardRevision.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/qaScorecards/{qaScorecardsId}/revisions/{revisionsId}", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.qaScorecards.revisions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the QaScorecardRevision to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/qaScorecards/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContactcenterinsightsV1QaScorecardRevision"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all revisions under the parent QaScorecard.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/qaScorecards/{qaScorecardsId}/revisions", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.qaScorecards.revisions.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter to reduce results to a specific subset. Useful for querying scorecard revisions with specific properties.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of scorecard revisions to return in the response. If the value is zero, the service will select a default size. A call might return fewer objects than requested. A non-empty `next_page_token` in the response indicates that more data is available.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The value returned by the last `ListQaScorecardRevisionsResponse`. This value indicates that this is a continuation of a prior `ListQaScorecardRevisions` call and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the scorecard revisions. To list all revisions of all scorecards, substitute the QaScorecard ID with a '-' character.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/qaScorecards/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/revisions", "response": {"$ref": "GoogleCloudContactcenterinsightsV1ListQaScorecardRevisionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "tuneQaScorecardRevision": {"description": "Fine tune one or more QaModels.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/qaScorecards/{qaScorecardsId}/revisions/{revisionsId}:tuneQaScorecardRevision", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.qaScorecards.revisions.tuneQaScorecardRevision", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource for new fine tuning job instance.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/qaScorecards/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}:tuneQaScorecardRevision", "request": {"$ref": "GoogleCloudContactcenterinsightsV1TuneQaScorecardRevisionRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "undeploy": {"description": "Undeploy a QaScorecardRevision.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/qaScorecards/{qaScorecardsId}/revisions/{revisionsId}:undeploy", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.qaScorecards.revisions.undeploy", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the QaScorecardRevision to undeploy.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/qaScorecards/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:undeploy", "request": {"$ref": "GoogleCloudContactcenterinsightsV1UndeployQaScorecardRevisionRequest"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1QaScorecardRevision"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"qaQuestions": {"methods": {"create": {"description": "Create a QaQuestion.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/qaScorecards/{qaScorecardsId}/revisions/{revisionsId}/qaQuestions", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.qaScorecards.revisions.qaQuestions.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the QaQuestion.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/qaScorecards/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}, "qaQuestionId": {"description": "Optional. A unique ID for the new question. This ID will become the final component of the question's resource name. If no ID is specified, a server-generated ID will be used. This value should be 4-64 characters and must match the regular expression `^[a-z0-9-]{4,64}$`. Valid characters are `a-z-`.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/qaQuestions", "request": {"$ref": "GoogleCloudContactcenterinsightsV1QaQuestion"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1QaQuestion"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a QaQuestion.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/qaScorecards/{qaScorecardsId}/revisions/{revisionsId}/qaQuestions/{qaQuestionsId}", "httpMethod": "DELETE", "id": "contactcenterinsights.projects.locations.qaScorecards.revisions.qaQuestions.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the QaQuestion to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/qaScorecards/[^/]+/revisions/[^/]+/qaQuestions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a QaQuestion.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/qaScorecards/{qaScorecardsId}/revisions/{revisionsId}/qaQuestions/{qaQuestionsId}", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.qaScorecards.revisions.qaQuestions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the QaQuestion to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/qaScorecards/[^/]+/revisions/[^/]+/qaQuestions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContactcenterinsightsV1QaQuestion"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists QaQuestions.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/qaScorecards/{qaScorecardsId}/revisions/{revisionsId}/qaQuestions", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.qaScorecards.revisions.qaQuestions.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of questions to return in the response. If the value is zero, the service will select a default size. A call might return fewer objects than requested. A non-empty `next_page_token` in the response indicates that more data is available.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The value returned by the last `ListQaQuestionsResponse`. This value indicates that this is a continuation of a prior `ListQaQuestions` call and that the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the questions.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/qaScorecards/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/qaQuestions", "response": {"$ref": "GoogleCloudContactcenterinsightsV1ListQaQuestionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a QaQuestion.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/qaScorecards/{qaScorecardsId}/revisions/{revisionsId}/qaQuestions/{qaQuestionsId}", "httpMethod": "PATCH", "id": "contactcenterinsights.projects.locations.qaScorecards.revisions.qaQuestions.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the question. Format: projects/{project}/locations/{location}/qaScorecards/{qa_scorecard}/revisions/{revision}/qaQuestions/{qa_question}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/qaScorecards/[^/]+/revisions/[^/]+/qaQuestions/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of fields to be updated. All possible fields can be updated by passing `*`, or a subset of the following updateable fields can be provided: * `abbreviation` * `answer_choices` * `answer_instructions` * `order` * `question_body` * `tags`", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudContactcenterinsightsV1QaQuestion"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1QaQuestion"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}, "views": {"methods": {"create": {"description": "Creates a view.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/views", "httpMethod": "POST", "id": "contactcenterinsights.projects.locations.views.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource of the view. Required. The location to create a view for. Format: `projects//locations/` or `projects//locations/`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/views", "request": {"$ref": "GoogleCloudContactcenterinsightsV1View"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1View"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a view.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/views/{viewsId}", "httpMethod": "DELETE", "id": "contactcenterinsights.projects.locations.views.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the view to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/views/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a view.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/views/{viewsId}", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.views.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the view to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/views/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudContactcenterinsightsV1View"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists views.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/views", "httpMethod": "GET", "id": "contactcenterinsights.projects.locations.views.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of views to return in the response. If this value is zero, the service will select a default size. A call may return fewer objects than requested. A non-empty `next_page_token` in the response indicates that more data is available.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The value returned by the last `ListViewsResponse`; indicates that this is a continuation of a prior `ListViews` call and the system should return the next page of data.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource of the views.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/views", "response": {"$ref": "GoogleCloudContactcenterinsightsV1ListViewsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a view.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/views/{viewsId}", "httpMethod": "PATCH", "id": "contactcenterinsights.projects.locations.views.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. The resource name of the view. Format: projects/{project}/locations/{location}/views/{view}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/views/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudContactcenterinsightsV1View"}, "response": {"$ref": "GoogleCloudContactcenterinsightsV1View"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250512", "rootUrl": "https://contactcenterinsights.googleapis.com/", "schemas": {"GoogleCloudContactcenterinsightsV1Analysis": {"description": "The analysis resource.", "id": "GoogleCloudContactcenterinsightsV1Analysis", "properties": {"analysisResult": {"$ref": "GoogleCloudContactcenterinsightsV1AnalysisResult", "description": "Output only. The result of the analysis, which is populated when the analysis finishes.", "readOnly": true}, "annotatorSelector": {"$ref": "GoogleCloudContactcenterinsightsV1AnnotatorSelector", "description": "To select the annotators to run and the phrase matchers to use (if any). If not specified, all annotators will be run."}, "createTime": {"description": "Output only. The time at which the analysis was created, which occurs when the long-running operation completes.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Immutable. The resource name of the analysis. Format: projects/{project}/locations/{location}/conversations/{conversation}/analyses/{analysis}", "type": "string"}, "requestTime": {"description": "Output only. The time at which the analysis was requested.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1AnalysisResult": {"description": "The result of an analysis.", "id": "GoogleCloudContactcenterinsightsV1AnalysisResult", "properties": {"callAnalysisMetadata": {"$ref": "GoogleCloudContactcenterinsightsV1AnalysisResultCallAnalysisMetadata", "description": "Call-specific metadata created by the analysis."}, "endTime": {"description": "The time at which the analysis ended.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1AnalysisResultCallAnalysisMetadata": {"description": "Call-specific metadata created during analysis.", "id": "GoogleCloudContactcenterinsightsV1AnalysisResultCallAnalysisMetadata", "properties": {"annotations": {"description": "A list of call annotations that apply to this call.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1CallAnnotation"}, "type": "array"}, "entities": {"additionalProperties": {"$ref": "GoogleCloudContactcenterinsightsV1Entity"}, "description": "All the entities in the call.", "type": "object"}, "intents": {"additionalProperties": {"$ref": "GoogleCloudContactcenterinsightsV1Intent"}, "description": "All the matched intents in the call.", "type": "object"}, "issueModelResult": {"$ref": "GoogleCloudContactcenterinsightsV1IssueModelResult", "description": "Overall conversation-level issue modeling result."}, "phraseMatchers": {"additionalProperties": {"$ref": "GoogleCloudContactcenterinsightsV1PhraseMatchData"}, "description": "All the matched phrase matchers in the call.", "type": "object"}, "qaScorecardResults": {"description": "Results of scoring QaScorecards.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1QaScorecardResult"}, "type": "array"}, "sentiments": {"description": "Overall conversation-level sentiment for each channel of the call.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1ConversationLevelSentiment"}, "type": "array"}, "silence": {"$ref": "GoogleCloudContactcenterinsightsV1ConversationLevelSilence", "description": "Overall conversation-level silence during the call."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1AnalysisRule": {"description": "The CCAI Insights project wide analysis rule. This rule will be applied to all conversations that match the filter defined in the rule. For a conversation matches the filter, the annotators specified in the rule will be run. If a conversation matches multiple rules, a union of all the annotators will be run. One project can have multiple analysis rules.", "id": "GoogleCloudContactcenterinsightsV1AnalysisRule", "properties": {"active": {"description": "If true, apply this rule to conversations. Otherwise, this rule is inactive and saved as a draft.", "type": "boolean"}, "analysisPercentage": {"description": "Percentage of conversations that we should apply this analysis setting automatically, between [0, 1]. For example, 0.1 means 10%. Conversations are sampled in a determenestic way. The original runtime_percentage & upload percentage will be replaced by defining filters on the conversation.", "format": "double", "type": "number"}, "annotatorSelector": {"$ref": "GoogleCloudContactcenterinsightsV1AnnotatorSelector", "description": "Selector of annotators to run and the phrase matchers to use for conversations that matches the conversation_filter. If not specified, NO annotators will be run."}, "conversationFilter": {"description": "Filter for the conversations that should apply this analysis rule. An empty filter means this analysis rule applies to all conversations. Refer to https://cloud.google.com/contact-center/insights/docs/filtering for details.", "type": "string"}, "createTime": {"description": "Output only. The time at which this analysis rule was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Display Name of the analysis rule.", "type": "string"}, "name": {"description": "Identifier. The resource name of the analysis rule. Format: projects/{project}/locations/{location}/analysisRules/{analysis_rule}", "type": "string"}, "updateTime": {"description": "Output only. The most recent time at which this analysis rule was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1AnnotationBoundary": {"description": "A point in a conversation that marks the start or the end of an annotation.", "id": "GoogleCloudContactcenterinsightsV1AnnotationBoundary", "properties": {"transcriptIndex": {"description": "The index in the sequence of transcribed pieces of the conversation where the boundary is located. This index starts at zero.", "format": "int32", "type": "integer"}, "wordIndex": {"description": "The word index of this boundary with respect to the first word in the transcript piece. This index starts at zero.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1AnnotatorSelector": {"description": "Selector of all available annotators and phrase matchers to run.", "id": "GoogleCloudContactcenterinsightsV1AnnotatorSelector", "properties": {"issueModels": {"description": "The issue model to run. If not provided, the most recently deployed topic model will be used. The provided issue model will only be used for inference if the issue model is deployed and if run_issue_model_annotator is set to true. If more than one issue model is provided, only the first provided issue model will be used for inference.", "items": {"type": "string"}, "type": "array"}, "phraseMatchers": {"description": "The list of phrase matchers to run. If not provided, all active phrase matchers will be used. If inactive phrase matchers are provided, they will not be used. Phrase matchers will be run only if run_phrase_matcher_annotator is set to true. Format: projects/{project}/locations/{location}/phraseMatchers/{phrase_matcher}", "items": {"type": "string"}, "type": "array"}, "qaConfig": {"$ref": "GoogleCloudContactcenterinsightsV1AnnotatorSelectorQaConfig", "description": "Configuration for the QA annotator."}, "runEntityAnnotator": {"description": "Whether to run the entity annotator.", "type": "boolean"}, "runIntentAnnotator": {"description": "Whether to run the intent annotator.", "type": "boolean"}, "runInterruptionAnnotator": {"description": "Whether to run the interruption annotator.", "type": "boolean"}, "runIssueModelAnnotator": {"description": "Whether to run the issue model annotator. A model should have already been deployed for this to take effect.", "type": "boolean"}, "runPhraseMatcherAnnotator": {"description": "Whether to run the active phrase matcher annotator(s).", "type": "boolean"}, "runQaAnnotator": {"description": "Whether to run the QA annotator.", "type": "boolean"}, "runSentimentAnnotator": {"description": "Whether to run the sentiment annotator.", "type": "boolean"}, "runSilenceAnnotator": {"description": "Whether to run the silence annotator.", "type": "boolean"}, "runSummarizationAnnotator": {"description": "Whether to run the summarization annotator.", "type": "boolean"}, "summarizationConfig": {"$ref": "GoogleCloudContactcenterinsightsV1AnnotatorSelectorSummarizationConfig", "description": "Configuration for the summarization annotator."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1AnnotatorSelectorQaConfig": {"description": "Configuration for the QA feature.", "id": "GoogleCloudContactcenterinsightsV1AnnotatorSelectorQaConfig", "properties": {"scorecardList": {"$ref": "GoogleCloudContactcenterinsightsV1AnnotatorSelectorQaConfigScorecardList", "description": "A manual list of scorecards to score."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1AnnotatorSelectorQaConfigScorecardList": {"description": "Container for a list of scorecards.", "id": "GoogleCloudContactcenterinsightsV1AnnotatorSelectorQaConfigScorecardList", "properties": {"qaScorecardRevisions": {"description": "List of QaScorecardRevisions.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1AnnotatorSelectorSummarizationConfig": {"description": "Configuration for summarization.", "id": "GoogleCloudContactcenterinsightsV1AnnotatorSelectorSummarizationConfig", "properties": {"conversationProfile": {"description": "Resource name of the Dialogflow conversation profile. Format: projects/{project}/locations/{location}/conversationProfiles/{conversation_profile}", "type": "string"}, "generator": {"description": "The resource name of the existing created generator. Format: projects//locations//generators/", "type": "string"}, "summarizationModel": {"description": "Default summarization model to be used.", "enum": ["SUMMARIZATION_MODEL_UNSPECIFIED", "BASELINE_MODEL", "BASELINE_MODEL_V2_0"], "enumDescriptions": ["Unspecified summarization model.", "The CCAI baseline model.", "The CCAI baseline model, V2.0."], "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1AnswerFeedback": {"description": "The feedback that the customer has about a certain answer in the conversation.", "id": "GoogleCloudContactcenterinsightsV1AnswerFeedback", "properties": {"clicked": {"description": "Indicates whether an answer or item was clicked by the human agent.", "type": "boolean"}, "correctnessLevel": {"description": "The correctness level of an answer.", "enum": ["CORRECTNESS_LEVEL_UNSPECIFIED", "NOT_CORRECT", "PARTIALLY_CORRECT", "FULLY_CORRECT"], "enumDescriptions": ["Correctness level unspecified.", "Answer is totally wrong.", "Answer is partially correct.", "Answer is fully correct."], "type": "string"}, "displayed": {"description": "Indicates whether an answer or item was displayed to the human agent in the agent desktop UI.", "type": "boolean"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ArticleSuggestionData": {"description": "Agent Assist Article Suggestion data.", "id": "GoogleCloudContactcenterinsightsV1ArticleSuggestionData", "properties": {"confidenceScore": {"description": "The system's confidence score that this article is a good match for this conversation, ranging from 0.0 (completely uncertain) to 1.0 (completely certain).", "format": "float", "type": "number"}, "metadata": {"additionalProperties": {"type": "string"}, "description": "Map that contains metadata about the Article Suggestion and the document that it originates from.", "type": "object"}, "queryRecord": {"description": "The name of the answer record. Format: projects/{project}/locations/{location}/answerRecords/{answer_record}", "type": "string"}, "source": {"description": "The knowledge document that this answer was extracted from. Format: projects/{project}/knowledgeBases/{knowledge_base}/documents/{document}", "type": "string"}, "title": {"description": "Article title.", "type": "string"}, "uri": {"description": "Article URI.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1AuthorizedView": {"description": "An AuthorizedView represents a view of accessible Insights resources (for example, Conversation and Scorecard). Who have read access to the AuthorizedView resource will have access to these Insight resources as well.", "id": "GoogleCloudContactcenterinsightsV1AuthorizedView", "properties": {"conversationFilter": {"description": "A filter to reduce conversation results to a specific subset. The AuthorizedView's assigned permission (read/write) could be applied to the subset of conversations. If conversation_filter is empty, there is no restriction on the conversations that the AuthorizedView can access. Having *authorizedViews.get* access to the AuthorizedView means having the same read/write access to the Conversations (as well as metadata/annotations liked to the conversation) that this AuthorizedView has.", "type": "string"}, "createTime": {"description": "Output only. The time at which the authorized view was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Display Name. Limit 64 characters.", "type": "string"}, "name": {"description": "Identifier. The resource name of the AuthorizedView. Format: projects/{project}/locations/{location}/authorizedViewSets/{authorized_view_set}/authorizedViews/{authorized_view}", "type": "string"}, "updateTime": {"description": "Output only. The most recent time at which the authorized view was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1AuthorizedViewSet": {"description": "An AuthorizedViewSet contains a set of AuthorizedView resources.", "id": "GoogleCloudContactcenterinsightsV1AuthorizedViewSet", "properties": {"createTime": {"description": "Output only. Create time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Display Name. Limit 64 characters.", "type": "string"}, "name": {"description": "Identifier. The resource name of the AuthorizedViewSet. Format: projects/{project}/locations/{location}/authorizedViewSets/{authorized_view_set}", "type": "string"}, "updateTime": {"description": "Output only. Update time.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1BulkAnalyzeConversationsMetadata": {"description": "The metadata for a bulk analyze conversations operation.", "id": "GoogleCloudContactcenterinsightsV1BulkAnalyzeConversationsMetadata", "properties": {"completedAnalysesCount": {"description": "The number of requested analyses that have completed successfully so far.", "format": "int32", "type": "integer"}, "createTime": {"description": "The time the operation was created.", "format": "google-datetime", "type": "string"}, "endTime": {"description": "The time the operation finished running.", "format": "google-datetime", "type": "string"}, "failedAnalysesCount": {"description": "The number of requested analyses that have failed so far.", "format": "int32", "type": "integer"}, "partialErrors": {"description": "Output only. Partial errors during bulk analyze operation that might cause the operation output to be incomplete.", "items": {"$ref": "GoogleRpcStatus"}, "readOnly": true, "type": "array"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1BulkAnalyzeConversationsRequest", "description": "The original request for bulk analyze."}, "totalRequestedAnalysesCount": {"description": "Total number of analyses requested. Computed by the number of conversations returned by `filter` multiplied by `analysis_percentage` in the request.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1BulkAnalyzeConversationsRequest": {"description": "The request to analyze conversations in bulk.", "id": "GoogleCloudContactcenterinsightsV1BulkAnalyzeConversationsRequest", "properties": {"analysisPercentage": {"description": "Required. Percentage of selected conversation to analyze, between [0, 100].", "format": "float", "type": "number"}, "annotatorSelector": {"$ref": "GoogleCloudContactcenterinsightsV1AnnotatorSelector", "description": "To select the annotators to run and the phrase matchers to use (if any). If not specified, all annotators will be run."}, "filter": {"description": "Required. Filter used to select the subset of conversations to analyze.", "type": "string"}, "parent": {"description": "Required. The parent resource to create analyses in.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1BulkAnalyzeConversationsResponse": {"description": "The response for a bulk analyze conversations operation.", "id": "GoogleCloudContactcenterinsightsV1BulkAnalyzeConversationsResponse", "properties": {"failedAnalysisCount": {"description": "Count of failed analyses.", "format": "int32", "type": "integer"}, "successfulAnalysisCount": {"description": "Count of successful analyses.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1BulkDeleteConversationsMetadata": {"description": "The metadata for a bulk delete conversations operation.", "id": "GoogleCloudContactcenterinsightsV1BulkDeleteConversationsMetadata", "properties": {"createTime": {"description": "The time the operation was created.", "format": "google-datetime", "type": "string"}, "endTime": {"description": "The time the operation finished running.", "format": "google-datetime", "type": "string"}, "partialErrors": {"description": "Partial errors during bulk delete conversations operation that might cause the operation output to be incomplete.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1BulkDeleteConversationsRequest", "description": "The original request for bulk delete."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1BulkDeleteConversationsRequest": {"description": "The request to delete conversations in bulk.", "id": "GoogleCloudContactcenterinsightsV1BulkDeleteConversationsRequest", "properties": {"filter": {"description": "Filter used to select the subset of conversations to delete.", "type": "string"}, "force": {"description": "If set to true, all of this conversation's analyses will also be deleted. Otherwise, the request will only succeed if the conversation has no analyses.", "type": "boolean"}, "maxDeleteCount": {"description": "Maximum number of conversations to delete.", "format": "int32", "type": "integer"}, "parent": {"description": "Required. The parent resource to delete conversations from. Format: projects/{project}/locations/{location}", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1BulkDeleteConversationsResponse": {"description": "The response for a bulk delete conversations operation.", "id": "GoogleCloudContactcenterinsightsV1BulkDeleteConversationsResponse", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1BulkDeleteFeedbackLabelsMetadata": {"description": "Metadata for the BulkDeleteFeedbackLabels endpoint.", "id": "GoogleCloudContactcenterinsightsV1BulkDeleteFeedbackLabelsMetadata", "properties": {"partialErrors": {"description": "Partial errors during deletion operation that might cause the operation output to be incomplete.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1BulkDeleteFeedbackLabelsRequest", "description": "Output only. The original request for delete.", "readOnly": true}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1BulkDeleteFeedbackLabelsRequest": {"description": "Request for the BulkDeleteFeedbackLabels endpoint.", "id": "GoogleCloudContactcenterinsightsV1BulkDeleteFeedbackLabelsRequest", "properties": {"filter": {"description": "Optional. A filter to reduce results to a specific subset. Supports disjunctions (OR) and conjunctions (AND). Supported fields: * `issue_model_id` * `qa_question_id` * `qa_scorecard_id` * `min_create_time` * `max_create_time` * `min_update_time` * `max_update_time` * `feedback_label_type`: QUALITY_AI, TOPIC_MODELING", "type": "string"}, "parent": {"description": "Required. The parent resource for new feedback labels.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1BulkDeleteFeedbackLabelsResponse": {"description": "Response for the BulkDeleteFeedbackLabels endpoint.", "id": "GoogleCloudContactcenterinsightsV1BulkDeleteFeedbackLabelsResponse", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1BulkDownloadFeedbackLabelsMetadata": {"description": "Metadata for the BulkDownloadFeedbackLabel endpoint.", "id": "GoogleCloudContactcenterinsightsV1BulkDownloadFeedbackLabelsMetadata", "properties": {"createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "downloadStats": {"$ref": "GoogleCloudContactcenterinsightsV1BulkDownloadFeedbackLabelsMetadataDownloadStats", "description": "Output only. Statistics for BulkDownloadFeedbackLabels operation.", "readOnly": true}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "partialErrors": {"description": "Partial errors during ingest operation that might cause the operation output to be incomplete.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1BulkDownloadFeedbackLabelsRequest", "description": "Output only. The original request for download.", "readOnly": true}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1BulkDownloadFeedbackLabelsMetadataDownloadStats": {"description": "Statistics for BulkDownloadFeedbackLabels operation.", "id": "GoogleCloudContactcenterinsightsV1BulkDownloadFeedbackLabelsMetadataDownloadStats", "properties": {"fileNames": {"description": "Output only. Full name of the files written to Cloud storage.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "processedObjectCount": {"description": "The number of objects processed during the download operation.", "format": "int32", "type": "integer"}, "successfulDownloadCount": {"description": "The number of new feedback labels downloaded during this operation. Different from \"processed\" because some labels might not be downloaded because an error.", "format": "int32", "type": "integer"}, "totalFilesWritten": {"description": "Total number of files written to the provided Cloud Storage bucket.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1BulkDownloadFeedbackLabelsRequest": {"description": "Request for the BulkDownloadFeedbackLabel endpoint.", "id": "GoogleCloudContactcenterinsightsV1BulkDownloadFeedbackLabelsRequest", "properties": {"conversationFilter": {"description": "Optional. Filter parent conversations to download feedback labels for. When specified, the feedback labels will be downloaded for the conversations that match the filter. If `template_qa_scorecard_id` is set, all the conversations that match the filter will be paired with the questions under the scorecard for labeling.", "type": "string"}, "feedbackLabelType": {"description": "Optional. The type of feedback labels that will be downloaded.", "enum": ["FEEDBACK_LABEL_TYPE_UNSPECIFIED", "QUALITY_AI", "TOPIC_MODELING", "AGENT_ASSIST_SUMMARY"], "enumDescriptions": ["Unspecified format", "Downloaded file will contain all Quality AI labels from the latest scorecard revision.", "Downloaded file will contain only Topic Modeling labels.", "Agent Assist Summarization labels."], "type": "string"}, "filter": {"description": "Optional. A filter to reduce results to a specific subset. Supports disjunctions (OR) and conjunctions (AND). Supported fields: * `issue_model_id` * `qa_question_id` * `qa_scorecard_id` * `min_create_time` * `max_create_time` * `min_update_time` * `max_update_time` * `feedback_label_type`: QUALITY_AI, TOPIC_MODELING", "type": "string"}, "gcsDestination": {"$ref": "GoogleCloudContactcenterinsightsV1BulkDownloadFeedbackLabelsRequestGcsDestination", "description": "A cloud storage bucket destination."}, "maxDownloadCount": {"description": "Optional. Limits the maximum number of feedback labels that will be downloaded. The first `N` feedback labels will be downloaded.", "format": "int32", "type": "integer"}, "parent": {"description": "Required. The parent resource for new feedback labels.", "type": "string"}, "sheetsDestination": {"$ref": "GoogleCloudContactcenterinsightsV1BulkDownloadFeedbackLabelsRequestSheetsDestination", "description": "A sheets document destination."}, "templateQaScorecardId": {"description": "Optional. If set, a template for labeling conversations and scorecard questions will be created from the conversation_filter and the questions under the scorecard(s). The feedback label `filter` will be ignored.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1BulkDownloadFeedbackLabelsRequestGcsDestination": {"description": "Google Cloud Storage Object details to write the feedback labels to.", "id": "GoogleCloudContactcenterinsightsV1BulkDownloadFeedbackLabelsRequestGcsDestination", "properties": {"addWhitespace": {"description": "Optional. Add whitespace to the JSON file. Makes easier to read, but increases file size. Only applicable for JSON format.", "type": "boolean"}, "alwaysPrintEmptyFields": {"description": "Optional. Always print fields with no presence. This is useful for printing fields that are not set, like implicit 0 value or empty lists/maps. Only applicable for JSON format.", "type": "boolean"}, "format": {"description": "Required. File format in which the labels will be exported.", "enum": ["FORMAT_UNSPECIFIED", "CSV", "JSON"], "enumDescriptions": ["Unspecified format.", "CSV format. 1,000 labels are stored per CSV file by default.", "JSON format. 1 label stored per JSON file by default."], "type": "string"}, "objectUri": {"description": "Required. The Google Cloud Storage URI to write the feedback labels to. The file name will be used as a prefix for the files written to the bucket if the output needs to be split across multiple files, otherwise it will be used as is. The file extension will be appended to the file name based on the format selected. E.g. `gs://bucket_name/object_uri_prefix`", "type": "string"}, "recordsPerFileCount": {"description": "Optional. The number of records per file. Applicable for either format.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1BulkDownloadFeedbackLabelsRequestSheetsDestination": {"description": "Google Sheets document details to write the feedback labels to.", "id": "GoogleCloudContactcenterinsightsV1BulkDownloadFeedbackLabelsRequestSheetsDestination", "properties": {"sheetTitle": {"description": "Optional. The title of the new sheet to write the feedback labels to.", "type": "string"}, "spreadsheetUri": {"description": "Required. The Google Sheets document to write the feedback labels to. Retrieved from Google Sheets URI. E.g. `https://docs.google.com/spreadsheets/d/1234567890` The spreadsheet must be shared with the Insights P4SA. The spreadsheet ID written to will be returned as `file_names` in the BulkDownloadFeedbackLabelsMetadata.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1BulkDownloadFeedbackLabelsResponse": {"description": "Response for the BulkDownloadFeedbackLabel endpoint.", "id": "GoogleCloudContactcenterinsightsV1BulkDownloadFeedbackLabelsResponse", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1BulkUploadFeedbackLabelsRequest": {"description": "The request for bulk uploading feedback labels.", "id": "GoogleCloudContactcenterinsightsV1BulkUploadFeedbackLabelsRequest", "properties": {"gcsSource": {"$ref": "GoogleCloudContactcenterinsightsV1BulkUploadFeedbackLabelsRequestGcsSource", "description": "A cloud storage bucket source."}, "sheetsSource": {"$ref": "GoogleCloudContactcenterinsightsV1BulkUploadFeedbackLabelsRequestSheetsSource", "description": "A sheets document source."}, "validateOnly": {"description": "Optional. If set, upload will not happen and the labels will be validated. If not set, then default behavior will be to upload the labels after validation is complete.", "type": "boolean"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1BulkUploadFeedbackLabelsRequestGcsSource": {"description": "Google Cloud Storage Object details to get the feedback label file from.", "id": "GoogleCloudContactcenterinsightsV1BulkUploadFeedbackLabelsRequestGcsSource", "properties": {"format": {"description": "Required. File format which will be ingested.", "enum": ["FORMAT_UNSPECIFIED", "CSV", "JSON"], "enumDescriptions": ["Unspecified format.", "CSV format.", "JSON format."], "type": "string"}, "objectUri": {"description": "Required. The Google Cloud Storage URI of the file to import. Format: `gs://bucket_name/object_name`", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1BulkUploadFeedbackLabelsRequestSheetsSource": {"description": "Google Sheets document details to get the feedback label file from.", "id": "GoogleCloudContactcenterinsightsV1BulkUploadFeedbackLabelsRequestSheetsSource", "properties": {"spreadsheetUri": {"description": "Required. The Google Sheets document to write the feedback labels to. Retrieved from Google Sheets URI. E.g. `https://docs.google.com/spreadsheets/d/1234567890` The spreadsheet must be shared with the Insights P4SA.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1CalculateIssueModelStatsResponse": {"description": "Response of querying an issue model's statistics.", "id": "GoogleCloudContactcenterinsightsV1CalculateIssueModelStatsResponse", "properties": {"currentStats": {"$ref": "GoogleCloudContactcenterinsightsV1IssueModelLabelStats", "description": "The latest label statistics for the queried issue model. Includes results on both training data and data labeled after deployment."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1CalculateStatsRequest": {"description": "The request for calculating conversation statistics.", "id": "GoogleCloudContactcenterinsightsV1CalculateStatsRequest", "properties": {"filter": {"description": "A filter to reduce results to a specific subset. This field is useful for getting statistics about conversations with specific properties.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1CalculateStatsResponse": {"description": "The response for calculating conversation statistics.", "id": "GoogleCloudContactcenterinsightsV1CalculateStatsResponse", "properties": {"averageDuration": {"description": "The average duration of all conversations. The average is calculated using only conversations that have a time duration.", "format": "google-duration", "type": "string"}, "averageTurnCount": {"description": "The average number of turns per conversation.", "format": "int32", "type": "integer"}, "conversationCount": {"description": "The total number of conversations.", "format": "int32", "type": "integer"}, "conversationCountTimeSeries": {"$ref": "GoogleCloudContactcenterinsightsV1CalculateStatsResponseTimeSeries", "description": "A time series representing the count of conversations created over time that match that requested filter criteria."}, "customHighlighterMatches": {"additionalProperties": {"format": "int32", "type": "integer"}, "description": "A map associating each custom highlighter resource name with its respective number of matches in the set of conversations.", "type": "object"}, "issueMatches": {"additionalProperties": {"format": "int32", "type": "integer"}, "deprecated": true, "description": "A map associating each issue resource name with its respective number of matches in the set of conversations. Key has the format: `projects//locations//issueModels//issues/` Deprecated, use `issue_matches_stats` field instead.", "type": "object"}, "issueMatchesStats": {"additionalProperties": {"$ref": "GoogleCloudContactcenterinsightsV1IssueModelLabelStatsIssueStats"}, "description": "A map associating each issue resource name with its respective number of matches in the set of conversations. Key has the format: `projects//locations//issueModels//issues/`", "type": "object"}, "smartHighlighterMatches": {"additionalProperties": {"format": "int32", "type": "integer"}, "description": "A map associating each smart highlighter display name with its respective number of matches in the set of conversations.", "type": "object"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1CalculateStatsResponseTimeSeries": {"description": "A time series representing conversations over time.", "id": "GoogleCloudContactcenterinsightsV1CalculateStatsResponseTimeSeries", "properties": {"intervalDuration": {"description": "The duration of each interval.", "format": "google-duration", "type": "string"}, "points": {"description": "An ordered list of intervals from earliest to latest, where each interval represents the number of conversations that transpired during the time window.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1CalculateStatsResponseTimeSeriesInterval"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1CalculateStatsResponseTimeSeriesInterval": {"description": "A single interval in a time series.", "id": "GoogleCloudContactcenterinsightsV1CalculateStatsResponseTimeSeriesInterval", "properties": {"conversationCount": {"description": "The number of conversations created in this interval.", "format": "int32", "type": "integer"}, "startTime": {"description": "The start time of this interval.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1CallAnnotation": {"description": "A piece of metadata that applies to a window of a call.", "id": "GoogleCloudContactcenterinsightsV1CallAnnotation", "properties": {"annotationEndBoundary": {"$ref": "GoogleCloudContactcenterinsightsV1AnnotationBoundary", "description": "The boundary in the conversation where the annotation ends, inclusive."}, "annotationStartBoundary": {"$ref": "GoogleCloudContactcenterinsightsV1AnnotationBoundary", "description": "The boundary in the conversation where the annotation starts, inclusive."}, "channelTag": {"description": "The channel of the audio where the annotation occurs. For single-channel audio, this field is not populated.", "format": "int32", "type": "integer"}, "entityMentionData": {"$ref": "GoogleCloudContactcenterinsightsV1EntityMentionData", "description": "Data specifying an entity mention."}, "holdData": {"$ref": "GoogleCloudContactcenterinsightsV1HoldData", "description": "Data specifying a hold."}, "intentMatchData": {"$ref": "GoogleCloudContactcenterinsightsV1IntentMatchData", "description": "Data specifying an intent match."}, "interruptionData": {"$ref": "GoogleCloudContactcenterinsightsV1InterruptionData", "description": "Data specifying an interruption."}, "issueMatchData": {"$ref": "GoogleCloudContactcenterinsightsV1IssueMatchData", "description": "Data specifying an issue match."}, "phraseMatchData": {"$ref": "GoogleCloudContactcenterinsightsV1PhraseMatchData", "description": "Data specifying a phrase match."}, "sentimentData": {"$ref": "GoogleCloudContactcenterinsightsV1SentimentData", "description": "Data specifying sentiment."}, "silenceData": {"$ref": "GoogleCloudContactcenterinsightsV1SilenceData", "description": "Data specifying silence."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1Conversation": {"description": "The conversation resource.", "id": "GoogleCloudContactcenterinsightsV1Conversation", "properties": {"agentId": {"description": "An opaque, user-specified string representing the human agent who handled the conversation.", "type": "string"}, "callMetadata": {"$ref": "GoogleCloudContactcenterinsightsV1ConversationCallMetadata", "description": "Call-specific metadata."}, "createTime": {"description": "Output only. The time at which the conversation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dataSource": {"$ref": "GoogleCloudContactcenterinsightsV1ConversationDataSource", "description": "The source of the audio and transcription for the conversation."}, "dialogflowIntents": {"additionalProperties": {"$ref": "GoogleCloudContactcenterinsightsV1DialogflowIntent"}, "description": "Output only. All the matched Dialogflow intents in the call. The key corresponds to a Dialogflow intent, format: projects/{project}/agent/{agent}/intents/{intent}", "readOnly": true, "type": "object"}, "duration": {"description": "Output only. The duration of the conversation.", "format": "google-duration", "readOnly": true, "type": "string"}, "expireTime": {"description": "The time at which this conversation should expire. After this time, the conversation data and any associated analyses will be deleted.", "format": "google-datetime", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "A map for the user to specify any custom fields. A maximum of 100 labels per conversation is allowed, with a maximum of 256 characters per entry.", "type": "object"}, "languageCode": {"description": "A user-specified language code for the conversation.", "type": "string"}, "latestAnalysis": {"$ref": "GoogleCloudContactcenterinsightsV1Analysis", "description": "Output only. The conversation's latest analysis, if one exists.", "readOnly": true}, "latestSummary": {"$ref": "GoogleCloudContactcenterinsightsV1ConversationSummarizationSuggestionData", "description": "Output only. Latest summary of the conversation.", "readOnly": true}, "medium": {"description": "Immutable. The conversation medium, if unspecified will default to PHONE_CALL.", "enum": ["MEDIUM_UNSPECIFIED", "PHONE_CALL", "CHAT"], "enumDescriptions": ["Default value, if unspecified will default to PHONE_CALL.", "The format for conversations that took place over the phone.", "The format for conversations that took place over chat."], "type": "string"}, "metadataJson": {"description": "Input only. JSON metadata encoded as a string. This field is primarily used by Insights integrations with various telephony systems and must be in one of Insight's supported formats.", "type": "string"}, "name": {"description": "Immutable. The resource name of the conversation. Format: projects/{project}/locations/{location}/conversations/{conversation}", "type": "string"}, "obfuscatedUserId": {"description": "Obfuscated user ID which the customer sent to us.", "type": "string"}, "qualityMetadata": {"$ref": "GoogleCloudContactcenterinsightsV1ConversationQualityMetadata", "description": "Conversation metadata related to quality management."}, "runtimeAnnotations": {"description": "Output only. The annotations that were generated during the customer and agent interaction.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1RuntimeAnnotation"}, "readOnly": true, "type": "array"}, "startTime": {"description": "The time at which the conversation started.", "format": "google-datetime", "type": "string"}, "transcript": {"$ref": "GoogleCloudContactcenterinsightsV1ConversationTranscript", "description": "Output only. The conversation transcript.", "readOnly": true}, "ttl": {"description": "Input only. The TTL for this resource. If specified, then this TTL will be used to calculate the expire time.", "format": "google-duration", "type": "string"}, "turnCount": {"description": "Output only. The number of turns in the conversation.", "format": "int32", "readOnly": true, "type": "integer"}, "updateTime": {"description": "Output only. The most recent time at which the conversation was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ConversationCallMetadata": {"description": "Call-specific metadata.", "id": "GoogleCloudContactcenterinsightsV1ConversationCallMetadata", "properties": {"agentChannel": {"description": "The audio channel that contains the agent.", "format": "int32", "type": "integer"}, "customerChannel": {"description": "The audio channel that contains the customer.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ConversationDataSource": {"description": "The conversation source, which is a combination of transcript and audio.", "id": "GoogleCloudContactcenterinsightsV1ConversationDataSource", "properties": {"dialogflowSource": {"$ref": "GoogleCloudContactcenterinsightsV1DialogflowSource", "description": "The source when the conversation comes from Dialogflow."}, "gcsSource": {"$ref": "GoogleCloudContactcenterinsightsV1GcsSource", "description": "A Cloud Storage location specification for the audio and transcript."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ConversationLevelSentiment": {"description": "One channel of conversation-level sentiment data.", "id": "GoogleCloudContactcenterinsightsV1ConversationLevelSentiment", "properties": {"channelTag": {"description": "The channel of the audio that the data applies to.", "format": "int32", "type": "integer"}, "sentimentData": {"$ref": "GoogleCloudContactcenterinsightsV1SentimentData", "description": "Data specifying sentiment."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ConversationLevelSilence": {"description": "Conversation-level silence data.", "id": "GoogleCloudContactcenterinsightsV1ConversationLevelSilence", "properties": {"silenceDuration": {"description": "Amount of time calculated to be in silence.", "format": "google-duration", "type": "string"}, "silencePercentage": {"description": "Percentage of the total conversation spent in silence.", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ConversationParticipant": {"description": "The call participant speaking for a given utterance.", "id": "GoogleCloudContactcenterinsightsV1ConversationParticipant", "properties": {"dialogflowParticipant": {"deprecated": true, "description": "Deprecated. Use `dialogflow_participant_name` instead. The name of the Dialogflow participant. Format: projects/{project}/locations/{location}/conversations/{conversation}/participants/{participant}", "type": "string"}, "dialogflowParticipantName": {"description": "The name of the participant provided by Dialogflow. Format: projects/{project}/locations/{location}/conversations/{conversation}/participants/{participant}", "type": "string"}, "obfuscatedExternalUserId": {"description": "Obfuscated user ID from Dialogflow.", "type": "string"}, "role": {"description": "The role of the participant.", "enum": ["ROLE_UNSPECIFIED", "HUMAN_AGENT", "AUTOMATED_AGENT", "END_USER", "ANY_AGENT"], "enumDescriptions": ["<PERSON><PERSON><PERSON><PERSON>'s role is not set.", "Participant is a human agent.", "Participant is an automated agent.", "<PERSON><PERSON><PERSON><PERSON> is an end user who conversed with the contact center.", "Participant is either a human or automated agent."], "type": "string"}, "userId": {"description": "A user-specified ID representing the participant.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ConversationQualityMetadata": {"description": "Conversation metadata related to quality management.", "id": "GoogleCloudContactcenterinsightsV1ConversationQualityMetadata", "properties": {"agentInfo": {"description": "Information about agents involved in the call.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1ConversationQualityMetadataAgentInfo"}, "type": "array"}, "customerSatisfactionRating": {"description": "An arbitrary integer value indicating the customer's satisfaction rating.", "format": "int32", "type": "integer"}, "menuPath": {"description": "An arbitrary string value specifying the menu path the customer took.", "type": "string"}, "waitDuration": {"description": "The amount of time the customer waited to connect with an agent.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ConversationQualityMetadataAgentInfo": {"description": "Information about an agent involved in the conversation.", "id": "GoogleCloudContactcenterinsightsV1ConversationQualityMetadataAgentInfo", "properties": {"agentId": {"description": "A user-specified string representing the agent.", "type": "string"}, "agentType": {"description": "The agent type, e.g. HUMAN_AGENT.", "enum": ["ROLE_UNSPECIFIED", "HUMAN_AGENT", "AUTOMATED_AGENT", "END_USER", "ANY_AGENT"], "enumDescriptions": ["<PERSON><PERSON><PERSON><PERSON>'s role is not set.", "Participant is a human agent.", "Participant is an automated agent.", "<PERSON><PERSON><PERSON><PERSON> is an end user who conversed with the contact center.", "Participant is either a human or automated agent."], "type": "string"}, "displayName": {"description": "The agent's name.", "type": "string"}, "dispositionCode": {"description": "A user-provided string indicating the outcome of the agent's segment of the call.", "type": "string"}, "location": {"description": "The agent's location.", "type": "string"}, "team": {"deprecated": true, "description": "A user-specified string representing the agent's team. Deprecated in favor of the `teams` field.", "type": "string"}, "teams": {"description": "User-specified strings representing the agent's teams.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ConversationSummarizationSuggestionData": {"description": "Conversation summarization suggestion data.", "id": "GoogleCloudContactcenterinsightsV1ConversationSummarizationSuggestionData", "properties": {"answerRecord": {"description": "The name of the answer record. Format: projects/{project}/locations/{location}/answerRecords/{answer_record}", "type": "string"}, "confidence": {"description": "The confidence score of the summarization.", "format": "float", "type": "number"}, "conversationModel": {"description": "The name of the model that generates this summary. Format: projects/{project}/locations/{location}/conversationModels/{conversation_model}", "type": "string"}, "metadata": {"additionalProperties": {"type": "string"}, "description": "A map that contains metadata about the summarization and the document from which it originates.", "type": "object"}, "text": {"description": "The summarization content that is concatenated into one string.", "type": "string"}, "textSections": {"additionalProperties": {"type": "string"}, "description": "The summarization content that is divided into sections. The key is the section's name and the value is the section's content. There is no specific format for the key or value.", "type": "object"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ConversationTranscript": {"description": "A message representing the transcript of a conversation.", "id": "GoogleCloudContactcenterinsightsV1ConversationTranscript", "properties": {"transcriptSegments": {"description": "A list of sequential transcript segments that comprise the conversation.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1ConversationTranscriptTranscriptSegment"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ConversationTranscriptTranscriptSegment": {"description": "A segment of a full transcript.", "id": "GoogleCloudContactcenterinsightsV1ConversationTranscriptTranscriptSegment", "properties": {"channelTag": {"description": "For conversations derived from multi-channel audio, this is the channel number corresponding to the audio from that channel. For audioChannelCount = N, its output values can range from '1' to 'N'. A channel tag of 0 indicates that the audio is mono.", "format": "int32", "type": "integer"}, "confidence": {"description": "A confidence estimate between 0.0 and 1.0 of the fidelity of this segment. A default value of 0.0 indicates that the value is unset.", "format": "float", "type": "number"}, "dialogflowSegmentMetadata": {"$ref": "GoogleCloudContactcenterinsightsV1ConversationTranscriptTranscriptSegmentDialogflowSegmentMetadata", "description": "CCAI metadata relating to the current transcript segment."}, "languageCode": {"description": "The language code of this segment as a [BCP-47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt) language tag. Example: \"en-US\".", "type": "string"}, "messageTime": {"description": "The time that the message occurred, if provided.", "format": "google-datetime", "type": "string"}, "segmentParticipant": {"$ref": "GoogleCloudContactcenterinsightsV1ConversationParticipant", "description": "The participant of this segment."}, "sentiment": {"$ref": "GoogleCloudContactcenterinsightsV1SentimentData", "description": "The sentiment for this transcript segment."}, "text": {"description": "The text of this segment.", "type": "string"}, "words": {"description": "A list of the word-specific information for each word in the segment.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1ConversationTranscriptTranscriptSegmentWordInfo"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ConversationTranscriptTranscriptSegmentDialogflowSegmentMetadata": {"description": "Metadata from Dialogflow relating to the current transcript segment.", "id": "GoogleCloudContactcenterinsightsV1ConversationTranscriptTranscriptSegmentDialogflowSegmentMetadata", "properties": {"smartReplyAllowlistCovered": {"description": "Whether the transcript segment was covered under the configured smart reply allowlist in Agent Assist.", "type": "boolean"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ConversationTranscriptTranscriptSegmentWordInfo": {"description": "Word-level info for words in a transcript.", "id": "GoogleCloudContactcenterinsightsV1ConversationTranscriptTranscriptSegmentWordInfo", "properties": {"confidence": {"description": "A confidence estimate between 0.0 and 1.0 of the fidelity of this word. A default value of 0.0 indicates that the value is unset.", "format": "float", "type": "number"}, "endOffset": {"description": "Time offset of the end of this word relative to the beginning of the total conversation.", "format": "google-duration", "type": "string"}, "startOffset": {"description": "Time offset of the start of this word relative to the beginning of the total conversation.", "format": "google-duration", "type": "string"}, "word": {"description": "The word itself. Includes punctuation marks that surround the word.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1CreateAnalysisOperationMetadata": {"description": "Metadata for a create analysis operation.", "id": "GoogleCloudContactcenterinsightsV1CreateAnalysisOperationMetadata", "properties": {"annotatorSelector": {"$ref": "GoogleCloudContactcenterinsightsV1AnnotatorSelector", "description": "Output only. The annotator selector used for the analysis (if any).", "readOnly": true}, "conversation": {"description": "Output only. The Conversation that this Analysis Operation belongs to.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1CreateIssueMetadata": {"description": "Met<PERSON><PERSON> for creating an issue.", "id": "GoogleCloudContactcenterinsightsV1CreateIssueMetadata", "properties": {"createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1CreateIssueRequest", "description": "The original request for creation."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1CreateIssueModelMetadata": {"description": "<PERSON><PERSON><PERSON> for creating an issue model.", "id": "GoogleCloudContactcenterinsightsV1CreateIssueModelMetadata", "properties": {"createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1CreateIssueModelRequest", "description": "The original request for creation."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1CreateIssueModelRequest": {"description": "The request to create an issue model.", "id": "GoogleCloudContactcenterinsightsV1CreateIssueModelRequest", "properties": {"issueModel": {"$ref": "GoogleCloudContactcenterinsightsV1IssueModel", "description": "Required. The issue model to create."}, "parent": {"description": "Required. The parent resource of the issue model.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1CreateIssueRequest": {"description": "The request to create an issue.", "id": "GoogleCloudContactcenterinsightsV1CreateIssueRequest", "properties": {"issue": {"$ref": "GoogleCloudContactcenterinsightsV1Issue", "description": "Required. The values for the new issue."}, "parent": {"description": "Required. The parent resource of the issue.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1Dataset": {"description": "Dataset resource represents a collection of conversations that may be bounded (Static Dataset, e.g. golden dataset for training), or unbounded (Dynamic Dataset, e.g. live traffic, or agent training traffic)", "id": "GoogleCloudContactcenterinsightsV1Dataset", "properties": {"createTime": {"description": "Output only. Dataset create time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Dataset description.", "type": "string"}, "displayName": {"description": "Display name for the dataaset", "type": "string"}, "name": {"description": "Immutable. Identifier. Resource name of the dataset. Format: projects/{project}/locations/{location}/datasets/{dataset}", "type": "string"}, "ttl": {"description": "Optional. Option TTL for the dataset.", "format": "google-duration", "type": "string"}, "type": {"description": "Dataset usage type.", "enum": ["TYPE_UNSPECIFIED", "EVAL", "LIVE"], "enumDescriptions": ["Default value for unspecified.", "For evals only.", "Dataset with new conversations coming in regularly (Insights legacy conversations and AI trainer)"], "type": "string"}, "updateTime": {"description": "Output only. Dataset update time.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1DeleteIssueModelMetadata": {"description": "Metadata for deleting an issue model.", "id": "GoogleCloudContactcenterinsightsV1DeleteIssueModelMetadata", "properties": {"createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1DeleteIssueModelRequest", "description": "The original request for deletion."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1DeleteIssueModelRequest": {"description": "The request to delete an issue model.", "id": "GoogleCloudContactcenterinsightsV1DeleteIssueModelRequest", "properties": {"name": {"description": "Required. The name of the issue model to delete.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1DeployIssueModelMetadata": {"description": "Metadata for deploying an issue model.", "id": "GoogleCloudContactcenterinsightsV1DeployIssueModelMetadata", "properties": {"createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1DeployIssueModelRequest", "description": "The original request for deployment."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1DeployIssueModelRequest": {"description": "The request to deploy an issue model.", "id": "GoogleCloudContactcenterinsightsV1DeployIssueModelRequest", "properties": {"name": {"description": "Required. The issue model to deploy.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1DeployIssueModelResponse": {"description": "The response to deploy an issue model.", "id": "GoogleCloudContactcenterinsightsV1DeployIssueModelResponse", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1DeployQaScorecardRevisionRequest": {"description": "The request to deploy a QaScorecardRevision", "id": "GoogleCloudContactcenterinsightsV1DeployQaScorecardRevisionRequest", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1DialogflowIntent": {"description": "The data for a Dialogflow intent. Represents a detected intent in the conversation, e.g. MAKES_PROMISE.", "id": "GoogleCloudContactcenterinsightsV1DialogflowIntent", "properties": {"displayName": {"description": "The human-readable name of the intent.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1DialogflowInteractionData": {"description": "Dialogflow interaction data.", "id": "GoogleCloudContactcenterinsightsV1DialogflowInteractionData", "properties": {"confidence": {"description": "The confidence of the match ranging from 0.0 (completely uncertain) to 1.0 (completely certain).", "format": "float", "type": "number"}, "dialogflowIntentId": {"description": "The Dialogflow intent resource path. Format: projects/{project}/agent/{agent}/intents/{intent}", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1DialogflowSource": {"description": "A Dialogflow source of conversation data.", "id": "GoogleCloudContactcenterinsightsV1DialogflowSource", "properties": {"audioUri": {"description": "Cloud Storage URI that points to a file that contains the conversation audio.", "type": "string"}, "dialogflowConversation": {"description": "Output only. The name of the Dialogflow conversation that this conversation resource is derived from. Format: projects/{project}/locations/{location}/conversations/{conversation}", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1Dimension": {"description": "A dimension determines the grouping key for the query. In SQL terms, these would be part of both the \"SELECT\" and \"GROUP BY\" clauses.", "id": "GoogleCloudContactcenterinsightsV1Dimension", "properties": {"agentDimensionMetadata": {"$ref": "GoogleCloudContactcenterinsightsV1DimensionAgentDimensionMetadata", "description": "Output only. <PERSON><PERSON><PERSON> about the agent dimension.", "readOnly": true}, "dimensionKey": {"description": "The key of the dimension.", "enum": ["DIMENSION_KEY_UNSPECIFIED", "ISSUE", "ISSUE_NAME", "AGENT", "AGENT_TEAM", "QA_QUESTION_ID", "QA_QUESTION_ANSWER_VALUE", "CONVERSATION_PROFILE_ID", "MEDIUM"], "enumDescriptions": ["The key of the dimension is unspecified.", "The dimension is keyed by issues.", "The dimension is keyed by issue names.", "The dimension is keyed by agents.", "The dimension is keyed by agent teams.", "The dimension is keyed by QaQuestionIds. Note that: We only group by the QuestionId and not the revision-id of the scorecard this question is a part of. This allows for showing stats for the same question across different scorecard revisions.", "The dimension is keyed by QaQuestionIds-Answer value pairs. Note that: We only group by the QuestionId and not the revision-id of the scorecard this question is a part of. This allows for showing distribution of answers per question across different scorecard revisions.", "The dimension is keyed by the conversation profile ID.", "The dimension is keyed by the conversation medium."], "type": "string"}, "issueDimensionMetadata": {"$ref": "GoogleCloudContactcenterinsightsV1DimensionIssueDimensionMetadata", "description": "Output only. <PERSON><PERSON><PERSON> about the issue dimension.", "readOnly": true}, "qaQuestionAnswerDimensionMetadata": {"$ref": "GoogleCloudContactcenterinsightsV1DimensionQaQuestionAnswerDimensionMetadata", "description": "Output only. <PERSON><PERSON><PERSON> about the QA question-answer dimension.", "readOnly": true}, "qaQuestionDimensionMetadata": {"$ref": "GoogleCloudContactcenterinsightsV1DimensionQaQuestionDimensionMetadata", "description": "Output only. <PERSON><PERSON><PERSON> about the QA question dimension.", "readOnly": true}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1DimensionAgentDimensionMetadata": {"description": "<PERSON><PERSON><PERSON> about the agent dimension.", "id": "GoogleCloudContactcenterinsightsV1DimensionAgentDimensionMetadata", "properties": {"agentDisplayName": {"description": "Optional. The agent's name", "type": "string"}, "agentId": {"description": "Optional. A user-specified string representing the agent.", "type": "string"}, "agentTeam": {"description": "Optional. A user-specified string representing the agent's team.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1DimensionIssueDimensionMetadata": {"description": "<PERSON><PERSON><PERSON> about the issue dimension.", "id": "GoogleCloudContactcenterinsightsV1DimensionIssueDimensionMetadata", "properties": {"issueDisplayName": {"description": "The issue display name.", "type": "string"}, "issueId": {"description": "The issue ID.", "type": "string"}, "issueModelId": {"description": "The parent issue model ID.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1DimensionQaQuestionAnswerDimensionMetadata": {"description": "Metadata about the QA question-answer dimension. This is useful for showing the answer distribution for questions for a given scorecard.", "id": "GoogleCloudContactcenterinsightsV1DimensionQaQuestionAnswerDimensionMetadata", "properties": {"answerValue": {"description": "Optional. The full body of the question.", "type": "string"}, "qaQuestionId": {"description": "Optional. The QA question ID.", "type": "string"}, "qaScorecardId": {"description": "Optional. The QA scorecard ID.", "type": "string"}, "questionBody": {"description": "Optional. The full body of the question.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1DimensionQaQuestionDimensionMetadata": {"description": "<PERSON><PERSON><PERSON> about the QA question dimension.", "id": "GoogleCloudContactcenterinsightsV1DimensionQaQuestionDimensionMetadata", "properties": {"qaQuestionId": {"description": "Optional. The QA question ID.", "type": "string"}, "qaScorecardId": {"description": "Optional. The QA scorecard ID.", "type": "string"}, "questionBody": {"description": "Optional. The full body of the question.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1EncryptionSpec": {"description": "A customer-managed encryption key specification that can be applied to all created resources (e.g. `Conversation`).", "id": "GoogleCloudContactcenterinsightsV1EncryptionSpec", "properties": {"kmsKey": {"description": "Required. The name of customer-managed encryption key that is used to secure a resource and its sub-resources. If empty, the resource is secured by our default encryption key. Only the key in the same location as this resource is allowed to be used for encryption. Format: `projects/{project}/locations/{location}/keyRings/{keyRing}/cryptoKeys/{key}`", "type": "string"}, "name": {"description": "Immutable. The resource name of the encryption key specification resource. Format: projects/{project}/locations/{location}/encryptionSpec", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1Entity": {"description": "The data for an entity annotation. Represents a phrase in the conversation that is a known entity, such as a person, an organization, or location.", "id": "GoogleCloudContactcenterinsightsV1Entity", "properties": {"displayName": {"description": "The representative name for the entity.", "type": "string"}, "metadata": {"additionalProperties": {"type": "string"}, "description": "Metadata associated with the entity. For most entity types, the metadata is a Wikipedia URL (`wikipedia_url`) and Knowledge Graph MID (`mid`), if they are available. For the metadata associated with other entity types, see the Type table below.", "type": "object"}, "salience": {"description": "The salience score associated with the entity in the [0, 1.0] range. The salience score for an entity provides information about the importance or centrality of that entity to the entire document text. Scores closer to 0 are less salient, while scores closer to 1.0 are highly salient.", "format": "float", "type": "number"}, "sentiment": {"$ref": "GoogleCloudContactcenterinsightsV1SentimentData", "description": "The aggregate sentiment expressed for this entity in the conversation."}, "type": {"description": "The entity type.", "enum": ["TYPE_UNSPECIFIED", "PERSON", "LOCATION", "ORGANIZATION", "EVENT", "WORK_OF_ART", "CONSUMER_GOOD", "OTHER", "PHONE_NUMBER", "ADDRESS", "DATE", "NUMBER", "PRICE"], "enumDescriptions": ["Unspecified.", "Person.", "Location.", "Organization.", "Event.", "Artwork.", "Consumer product.", "Other types of entities.", "Phone number. The metadata lists the phone number (formatted according to local convention), plus whichever additional elements appear in the text: * `number` - The actual number, broken down into sections according to local convention. * `national_prefix` - Country code, if detected. * `area_code` - Region or area code, if detected. * `extension` - Phone extension (to be dialed after connection), if detected.", "Address. The metadata identifies the street number and locality plus whichever additional elements appear in the text: * `street_number` - Street number. * `locality` - City or town. * `street_name` - Street/route name, if detected. * `postal_code` - Postal code, if detected. * `country` - Country, if detected. * `broad_region` - Administrative area, such as the state, if detected. * `narrow_region` - Smaller administrative area, such as county, if detected. * `sublocality` - Used in Asian addresses to demark a district within a city, if detected.", "Date. The metadata identifies the components of the date: * `year` - Four digit year, if detected. * `month` - Two digit month number, if detected. * `day` - Two digit day number, if detected.", "Number. The metadata is the number itself.", "Price. The metadata identifies the `value` and `currency`."], "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1EntityMentionData": {"description": "The data for an entity mention annotation. This represents a mention of an `Entity` in the conversation.", "id": "GoogleCloudContactcenterinsightsV1EntityMentionData", "properties": {"entityUniqueId": {"description": "The key of this entity in conversation entities. Can be used to retrieve the exact `Entity` this mention is attached to.", "type": "string"}, "sentiment": {"$ref": "GoogleCloudContactcenterinsightsV1SentimentData", "description": "Sentiment expressed for this mention of the entity."}, "type": {"description": "The type of the entity mention.", "enum": ["MENTION_TYPE_UNSPECIFIED", "PROPER", "COMMON"], "enumDescriptions": ["Unspecified.", "Proper noun.", "Common noun (or noun compound)."], "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ExactMatchConfig": {"description": "Exact match configuration.", "id": "GoogleCloudContactcenterinsightsV1ExactMatchConfig", "properties": {"caseSensitive": {"description": "Whether to consider case sensitivity when performing an exact match.", "type": "boolean"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ExportInsightsDataMetadata": {"description": "Metadata for an export insights operation.", "id": "GoogleCloudContactcenterinsightsV1ExportInsightsDataMetadata", "properties": {"completedExportCount": {"description": "The number of conversations that were exported successfully.", "format": "int32", "type": "integer"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "failedExportCount": {"description": "The number of conversations that failed to be exported.", "format": "int32", "type": "integer"}, "partialErrors": {"description": "Partial errors during export operation that might cause the operation output to be incomplete.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1ExportInsightsDataRequest", "description": "The original request for export."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ExportInsightsDataRequest": {"description": "The request to export insights.", "id": "GoogleCloudContactcenterinsightsV1ExportInsightsDataRequest", "properties": {"bigQueryDestination": {"$ref": "GoogleCloudContactcenterinsightsV1ExportInsightsDataRequestBigQueryDestination", "description": "Specified if sink is a BigQuery table."}, "exportSchemaVersion": {"description": "Optional. Version of the export schema.", "enum": ["EXPORT_SCHEMA_VERSION_UNSPECIFIED", "EXPORT_V1", "EXPORT_V2", "EXPORT_V3", "EXPORT_V4", "EXPORT_V5", "EXPORT_V6", "EXPORT_V7", "EXPORT_VERSION_LATEST_AVAILABLE"], "enumDescriptions": ["Unspecified. Defaults to EXPORT_V3.", "Export schema version 1.", "Export schema version 2.", "Export schema version 3.", "Export schema version 4.", "Export schema version 5.", "Export schema version 6.", "Export schema version 7.", "Export schema version latest available."], "type": "string"}, "filter": {"description": "A filter to reduce results to a specific subset. Useful for exporting conversations with specific properties.", "type": "string"}, "kmsKey": {"description": "A fully qualified KMS key name for BigQuery tables protected by CMEK. Format: projects/{project}/locations/{location}/keyRings/{keyring}/cryptoKeys/{key}/cryptoKeyVersions/{version}", "type": "string"}, "parent": {"description": "Required. The parent resource to export data from.", "type": "string"}, "writeDisposition": {"description": "Options for what to do if the destination table already exists.", "enum": ["WRITE_DISPOSITION_UNSPECIFIED", "WRITE_TRUNCATE", "WRITE_APPEND"], "enumDescriptions": ["Write disposition is not specified. Defaults to WRITE_TRUNCATE.", "If the table already exists, BigQuery will overwrite the table data and use the schema from the load.", "If the table already exists, BigQuery will append data to the table."], "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ExportInsightsDataRequestBigQueryDestination": {"description": "A BigQuery Table Reference.", "id": "GoogleCloudContactcenterinsightsV1ExportInsightsDataRequestBigQueryDestination", "properties": {"dataset": {"description": "Required. The name of the BigQuery dataset that the snapshot result should be exported to. If this dataset does not exist, the export call returns an INVALID_ARGUMENT error.", "type": "string"}, "projectId": {"description": "A project ID or number. If specified, then export will attempt to write data to this project instead of the resource project. Otherwise, the resource project will be used.", "type": "string"}, "table": {"description": "The BigQuery table name to which the insights data should be written. If this table does not exist, the export call returns an INVALID_ARGUMENT error.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ExportInsightsDataResponse": {"description": "Response for an export insights operation.", "id": "GoogleCloudContactcenterinsightsV1ExportInsightsDataResponse", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ExportIssueModelMetadata": {"description": "Metadata used for export issue model.", "id": "GoogleCloudContactcenterinsightsV1ExportIssueModelMetadata", "properties": {"createTime": {"description": "The time the operation was created.", "format": "google-datetime", "type": "string"}, "endTime": {"description": "The time the operation finished running.", "format": "google-datetime", "type": "string"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1ExportIssueModelRequest", "description": "The original export request."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ExportIssueModelRequest": {"description": "Request to export an issue model.", "id": "GoogleCloudContactcenterinsightsV1ExportIssueModelRequest", "properties": {"gcsDestination": {"$ref": "GoogleCloudContactcenterinsightsV1ExportIssueModelRequestGcsDestination", "description": "Google Cloud Storage URI to export the issue model to."}, "name": {"description": "Required. The issue model to export.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ExportIssueModelRequestGcsDestination": {"description": "Google Cloud Storage Object URI to save the issue model to.", "id": "GoogleCloudContactcenterinsightsV1ExportIssueModelRequestGcsDestination", "properties": {"objectUri": {"description": "Required. Format: `gs:///`", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ExportIssueModelResponse": {"description": "Response from export issue model", "id": "GoogleCloudContactcenterinsightsV1ExportIssueModelResponse", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1FaqAnswerData": {"description": "Agent Assist frequently-asked-question answer data.", "id": "GoogleCloudContactcenterinsightsV1FaqAnswerData", "properties": {"answer": {"description": "The piece of text from the `source` knowledge base document.", "type": "string"}, "confidenceScore": {"description": "The system's confidence score that this answer is a good match for this conversation, ranging from 0.0 (completely uncertain) to 1.0 (completely certain).", "format": "float", "type": "number"}, "metadata": {"additionalProperties": {"type": "string"}, "description": "Map that contains metadata about the FAQ answer and the document that it originates from.", "type": "object"}, "queryRecord": {"description": "The name of the answer record. Format: projects/{project}/locations/{location}/answerRecords/{answer_record}", "type": "string"}, "question": {"description": "The corresponding FAQ question.", "type": "string"}, "source": {"description": "The knowledge document that this answer was extracted from. Format: projects/{project}/knowledgeBases/{knowledge_base}/documents/{document}.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1FeedbackLabel": {"description": "Represents a conversation, resource, and label provided by the user. Can take the form of a string label or a QaAnswer label. QaAnswer labels are used for Quality AI example conversations. String labels are used for Topic Modeling. AgentAssistSummary labels are used for Agent Assist Summarization.", "id": "GoogleCloudContactcenterinsightsV1FeedbackLabel", "properties": {"createTime": {"description": "Output only. Create time of the label.", "format": "google-datetime", "readOnly": true, "type": "string"}, "label": {"description": "String label used for Topic Modeling.", "type": "string"}, "labeledResource": {"description": "Name of the resource to be labeled. Supported resources are: * `projects/{project}/locations/{location}/qaScorecards/{scorecard}/revisions/{revision}/qaQuestions/{question}` * `projects/{project}/locations/{location}/issueModels/{issue_model}` * `projects/{project}/locations/{location}/generators/{generator_id}`", "type": "string"}, "name": {"description": "Immutable. Resource name of the FeedbackLabel. Format: projects/{project}/locations/{location}/conversations/{conversation}/feedbackLabels/{feedback_label}", "type": "string"}, "qaAnswerLabel": {"$ref": "GoogleCloudContactcenterinsightsV1QaAnswerAnswerValue", "description": "QaAnswer label used for Quality AI example conversations."}, "updateTime": {"description": "Output only. Update time of the label.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1GcsSource": {"description": "A Cloud Storage source of conversation data.", "id": "GoogleCloudContactcenterinsightsV1GcsSource", "properties": {"audioUri": {"description": "Cloud Storage URI that points to a file that contains the conversation audio.", "type": "string"}, "transcriptUri": {"description": "Immutable. Cloud Storage URI that points to a file that contains the conversation transcript.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1HoldData": {"description": "The data for a hold annotation.", "id": "GoogleCloudContactcenterinsightsV1HoldData", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ImportIssueModelMetadata": {"description": "Metadata used for import issue model.", "id": "GoogleCloudContactcenterinsightsV1ImportIssueModelMetadata", "properties": {"createTime": {"description": "The time the operation was created.", "format": "google-datetime", "type": "string"}, "endTime": {"description": "The time the operation finished running.", "format": "google-datetime", "type": "string"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1ImportIssueModelRequest", "description": "The original import request."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ImportIssueModelRequest": {"description": "Request to import an issue model.", "id": "GoogleCloudContactcenterinsightsV1ImportIssueModelRequest", "properties": {"createNewModel": {"description": "Optional. If set to true, will create an issue model from the imported file with randomly generated IDs for the issue model and corresponding issues. Otherwise, replaces an existing model with the same ID as the file.", "type": "boolean"}, "gcsSource": {"$ref": "GoogleCloudContactcenterinsightsV1ImportIssueModelRequestGcsSource", "description": "Google Cloud Storage source message."}, "parent": {"description": "Required. The parent resource of the issue model.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ImportIssueModelRequestGcsSource": {"description": "Google Cloud Storage Object URI to get the issue model file from.", "id": "GoogleCloudContactcenterinsightsV1ImportIssueModelRequestGcsSource", "properties": {"objectUri": {"description": "Required. Format: `gs:///`", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ImportIssueModelResponse": {"description": "Response from import issue model", "id": "GoogleCloudContactcenterinsightsV1ImportIssueModelResponse", "properties": {"issueModel": {"$ref": "GoogleCloudContactcenterinsightsV1IssueModel", "description": "The issue model that was imported."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1IngestConversationsMetadata": {"description": "The metadata for an IngestConversations operation.", "id": "GoogleCloudContactcenterinsightsV1IngestConversationsMetadata", "properties": {"createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "ingestConversationsStats": {"$ref": "GoogleCloudContactcenterinsightsV1IngestConversationsMetadataIngestConversationsStats", "description": "Output only. Statistics for IngestConversations operation.", "readOnly": true}, "partialErrors": {"description": "Output only. Partial errors during ingest operation that might cause the operation output to be incomplete.", "items": {"$ref": "GoogleRpcStatus"}, "readOnly": true, "type": "array"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1IngestConversationsRequest", "description": "Output only. The original request for ingest.", "readOnly": true}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1IngestConversationsMetadataIngestConversationsStats": {"description": "Statistics for IngestConversations operation.", "id": "GoogleCloudContactcenterinsightsV1IngestConversationsMetadataIngestConversationsStats", "properties": {"duplicatesSkippedCount": {"description": "Output only. The number of objects skipped because another conversation with the same transcript uri had already been ingested.", "format": "int32", "readOnly": true, "type": "integer"}, "failedIngestCount": {"description": "Output only. The number of objects which were unable to be ingested due to errors. The errors are populated in the partial_errors field.", "format": "int32", "readOnly": true, "type": "integer"}, "processedObjectCount": {"description": "Output only. The number of objects processed during the ingest operation.", "format": "int32", "readOnly": true, "type": "integer"}, "successfulIngestCount": {"description": "Output only. The number of new conversations added during this ingest operation.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1IngestConversationsRequest": {"description": "The request to ingest conversations.", "id": "GoogleCloudContactcenterinsightsV1IngestConversationsRequest", "properties": {"conversationConfig": {"$ref": "GoogleCloudContactcenterinsightsV1IngestConversationsRequestConversationConfig", "description": "Configuration that applies to all conversations."}, "gcsSource": {"$ref": "GoogleCloudContactcenterinsightsV1IngestConversationsRequestGcsSource", "description": "A cloud storage bucket source. Note that any previously ingested objects from the source will be skipped to avoid duplication."}, "parent": {"description": "Required. The parent resource for new conversations.", "type": "string"}, "redactionConfig": {"$ref": "GoogleCloudContactcenterinsightsV1RedactionConfig", "description": "Optional. DLP settings for transcript redaction. Optional, will default to the config specified in Settings."}, "sampleSize": {"description": "Optional. If set, this fields indicates the number of objects to ingest from the Cloud Storage bucket. If empty, the entire bucket will be ingested. Unless they are first deleted, conversations produced through sampling won't be ingested by subsequent ingest requests.", "format": "int32", "type": "integer"}, "speechConfig": {"$ref": "GoogleCloudContactcenterinsightsV1SpeechConfig", "description": "Optional. Default Speech-to-Text configuration. Optional, will default to the config specified in Settings."}, "transcriptObjectConfig": {"$ref": "GoogleCloudContactcenterinsightsV1IngestConversationsRequestTranscriptObjectConfig", "description": "Configuration for when `source` contains conversation transcripts."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1IngestConversationsRequestConversationConfig": {"description": "Configuration that applies to all conversations.", "id": "GoogleCloudContactcenterinsightsV1IngestConversationsRequestConversationConfig", "properties": {"agentChannel": {"description": "Optional. Indicates which of the channels, 1 or 2, contains the agent. Note that this must be set for conversations to be properly displayed and analyzed.", "format": "int32", "type": "integer"}, "agentId": {"description": "Optional. An opaque, user-specified string representing a human agent who handled all conversations in the import. Note that this will be overridden if per-conversation metadata is provided through the `metadata_bucket_uri`.", "type": "string"}, "customerChannel": {"description": "Optional. Indicates which of the channels, 1 or 2, contains the agent. Note that this must be set for conversations to be properly displayed and analyzed.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1IngestConversationsRequestGcsSource": {"description": "Configuration for Cloud Storage bucket sources.", "id": "GoogleCloudContactcenterinsightsV1IngestConversationsRequestGcsSource", "properties": {"bucketObjectType": {"description": "Optional. Specifies the type of the objects in `bucket_uri`.", "enum": ["BUCKET_OBJECT_TYPE_UNSPECIFIED", "TRANSCRIPT", "AUDIO"], "enumDescriptions": ["The object type is unspecified and will default to `TRANSCRIPT`.", "The object is a transcript.", "The object is an audio file."], "type": "string"}, "bucketUri": {"description": "Required. The Cloud Storage bucket containing source objects.", "type": "string"}, "customMetadataKeys": {"description": "Optional. Custom keys to extract as conversation labels from metadata files in `metadata_bucket_uri`. Keys not included in this field will be ignored. Note that there is a limit of 100 labels per conversation.", "items": {"type": "string"}, "type": "array"}, "metadataBucketUri": {"description": "Optional. The Cloud Storage path to the conversation metadata. Note that: [1] Metadata files are expected to be in JSON format. [2] Metadata and source files (transcripts or audio) must be in separate buckets. [3] A source file and its corresponding metadata file must share the same name to be properly ingested, E.g. `gs://bucket/audio/conversation1.mp3` and `gs://bucket/metadata/conversation1.json`.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1IngestConversationsRequestTranscriptObjectConfig": {"description": "Configuration for processing transcript objects.", "id": "GoogleCloudContactcenterinsightsV1IngestConversationsRequestTranscriptObjectConfig", "properties": {"medium": {"description": "Required. The medium transcript objects represent.", "enum": ["MEDIUM_UNSPECIFIED", "PHONE_CALL", "CHAT"], "enumDescriptions": ["Default value, if unspecified will default to PHONE_CALL.", "The format for conversations that took place over the phone.", "The format for conversations that took place over chat."], "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1IngestConversationsResponse": {"description": "The response to an IngestConversations operation.", "id": "GoogleCloudContactcenterinsightsV1IngestConversationsResponse", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1InitializeEncryptionSpecMetadata": {"description": "Metadata for initializing a location-level encryption specification.", "id": "GoogleCloudContactcenterinsightsV1InitializeEncryptionSpecMetadata", "properties": {"createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "partialErrors": {"description": "Partial errors during initializing operation that might cause the operation output to be incomplete.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1InitializeEncryptionSpecRequest", "description": "Output only. The original request for initialization.", "readOnly": true}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1InitializeEncryptionSpecRequest": {"description": "The request to initialize a location-level encryption specification.", "id": "GoogleCloudContactcenterinsightsV1InitializeEncryptionSpecRequest", "properties": {"encryptionSpec": {"$ref": "GoogleCloudContactcenterinsightsV1EncryptionSpec", "description": "Required. The encryption spec used for CMEK encryption. It is required that the kms key is in the same region as the endpoint. The same key will be used for all provisioned resources, if encryption is available. If the `kms_key_name` field is left empty, no encryption will be enforced."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1InitializeEncryptionSpecResponse": {"description": "The response to initialize a location-level encryption specification.", "id": "GoogleCloudContactcenterinsightsV1InitializeEncryptionSpecResponse", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1Intent": {"description": "The data for an intent. Represents a detected intent in the conversation, for example MAKES_PROMISE.", "id": "GoogleCloudContactcenterinsightsV1Intent", "properties": {"displayName": {"description": "The human-readable name of the intent.", "type": "string"}, "id": {"description": "The unique identifier of the intent.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1IntentMatchData": {"description": "The data for an intent match. Represents an intent match for a text segment in the conversation. A text segment can be part of a sentence, a complete sentence, or an utterance with multiple sentences.", "id": "GoogleCloudContactcenterinsightsV1IntentMatchData", "properties": {"intentUniqueId": {"description": "The id of the matched intent. Can be used to retrieve the corresponding intent information.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1InterruptionData": {"description": "The data for an interruption annotation.", "id": "GoogleCloudContactcenterinsightsV1InterruptionData", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1Issue": {"description": "The issue resource.", "id": "GoogleCloudContactcenterinsightsV1Issue", "properties": {"createTime": {"description": "Output only. The time at which this issue was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayDescription": {"description": "Representative description of the issue.", "type": "string"}, "displayName": {"description": "The representative name for the issue.", "type": "string"}, "name": {"description": "Immutable. The resource name of the issue. Format: projects/{project}/locations/{location}/issueModels/{issue_model}/issues/{issue}", "type": "string"}, "sampleUtterances": {"description": "Output only. Resource names of the sample representative utterances that match to this issue.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "updateTime": {"description": "Output only. The most recent time that this issue was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1IssueAssignment": {"description": "Information about the issue.", "id": "GoogleCloudContactcenterinsightsV1IssueAssignment", "properties": {"displayName": {"description": "Immutable. Display name of the assigned issue. This field is set at time of analysis and immutable since then.", "type": "string"}, "issue": {"description": "Resource name of the assigned issue.", "type": "string"}, "score": {"description": "Score indicating the likelihood of the issue assignment. currently bounded on [0,1].", "format": "double", "type": "number"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1IssueMatchData": {"description": "The data for an issue match annotation.", "id": "GoogleCloudContactcenterinsightsV1IssueMatchData", "properties": {"issueAssignment": {"$ref": "GoogleCloudContactcenterinsightsV1IssueAssignment", "description": "Information about the issue's assignment."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1IssueModel": {"description": "The issue model resource.", "id": "GoogleCloudContactcenterinsightsV1IssueModel", "properties": {"createTime": {"description": "Output only. The time at which this issue model was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "The representative name for the issue model.", "type": "string"}, "inputDataConfig": {"$ref": "GoogleCloudContactcenterinsightsV1IssueModelInputDataConfig", "description": "Configs for the input data that used to create the issue model."}, "issueCount": {"description": "Output only. Number of issues in this issue model.", "format": "int64", "readOnly": true, "type": "string"}, "languageCode": {"description": "Language of the model.", "type": "string"}, "modelType": {"description": "Type of the model.", "enum": ["MODEL_TYPE_UNSPECIFIED", "TYPE_V1", "TYPE_V2"], "enumDescriptions": ["Unspecified model type.", "Type V1.", "Type V2."], "type": "string"}, "name": {"description": "Immutable. The resource name of the issue model. Format: projects/{project}/locations/{location}/issueModels/{issue_model}", "type": "string"}, "state": {"description": "Output only. State of the model.", "enum": ["STATE_UNSPECIFIED", "UNDEPLOYED", "DEPLOYING", "DEPLOYED", "UNDEPLOYING", "DELETING"], "enumDescriptions": ["Unspecified.", "Model is not deployed but is ready to deploy.", "Model is being deployed.", "Model is deployed and is ready to be used. A model can only be used in analysis if it's in this state.", "Model is being undeployed.", "Model is being deleted."], "readOnly": true, "type": "string"}, "trainingStats": {"$ref": "GoogleCloudContactcenterinsightsV1IssueModelLabelStats", "description": "Output only. Immutable. The issue model's label statistics on its training data.", "readOnly": true}, "updateTime": {"description": "Output only. The most recent time at which the issue model was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1IssueModelInputDataConfig": {"description": "Configs for the input data used to create the issue model.", "id": "GoogleCloudContactcenterinsightsV1IssueModelInputDataConfig", "properties": {"filter": {"description": "A filter to reduce the conversations used for training the model to a specific subset. Refer to https://cloud.google.com/contact-center/insights/docs/filtering for details.", "type": "string"}, "medium": {"deprecated": true, "description": "Medium of conversations used in training data. This field is being deprecated. To specify the medium to be used in training a new issue model, set the `medium` field on `filter`.", "enum": ["MEDIUM_UNSPECIFIED", "PHONE_CALL", "CHAT"], "enumDescriptions": ["Default value, if unspecified will default to PHONE_CALL.", "The format for conversations that took place over the phone.", "The format for conversations that took place over chat."], "type": "string"}, "trainingConversationsCount": {"description": "Output only. Number of conversations used in training. Output only.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1IssueModelLabelStats": {"description": "Aggregated statistics about an issue model.", "id": "GoogleCloudContactcenterinsightsV1IssueModelLabelStats", "properties": {"analyzedConversationsCount": {"description": "Number of conversations the issue model has analyzed at this point in time.", "format": "int64", "type": "string"}, "issueStats": {"additionalProperties": {"$ref": "GoogleCloudContactcenterinsightsV1IssueModelLabelStatsIssueStats"}, "description": "Statistics on each issue. Key is the issue's resource name.", "type": "object"}, "unclassifiedConversationsCount": {"description": "Number of analyzed conversations for which no issue was applicable at this point in time.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1IssueModelLabelStatsIssueStats": {"description": "Aggregated statistics about an issue.", "id": "GoogleCloudContactcenterinsightsV1IssueModelLabelStatsIssueStats", "properties": {"displayName": {"description": "Display name of the issue.", "type": "string"}, "issue": {"description": "Issue resource. Format: projects/{project}/locations/{location}/issueModels/{issue_model}/issues/{issue}", "type": "string"}, "labeledConversationsCount": {"description": "Number of conversations attached to the issue at this point in time.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1IssueModelResult": {"description": "Issue Modeling result on a conversation.", "id": "GoogleCloudContactcenterinsightsV1IssueModelResult", "properties": {"issueModel": {"description": "Issue model that generates the result. Format: projects/{project}/locations/{location}/issueModels/{issue_model}", "type": "string"}, "issues": {"description": "All the matched issues.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1IssueAssignment"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ListAllFeedbackLabelsResponse": {"description": "The response for listing all feedback labels.", "id": "GoogleCloudContactcenterinsightsV1ListAllFeedbackLabelsResponse", "properties": {"feedbackLabels": {"description": "The feedback labels that match the request.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1FeedbackLabel"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ListAnalysesResponse": {"description": "The response to list analyses.", "id": "GoogleCloudContactcenterinsightsV1ListAnalysesResponse", "properties": {"analyses": {"description": "The analyses that match the request.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1Analysis"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ListAnalysisRulesResponse": {"description": "The response of listing views.", "id": "GoogleCloudContactcenterinsightsV1ListAnalysisRulesResponse", "properties": {"analysisRules": {"description": "The analysis_rule that match the request.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1AnalysisRule"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ListAuthorizedViewSetsResponse": {"description": "The response from a ListAuthorizedViewSet request.", "id": "GoogleCloudContactcenterinsightsV1ListAuthorizedViewSetsResponse", "properties": {"authorizedViewSets": {"description": "The AuthorizedViewSets under the parent.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1AuthorizedViewSet"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ListAuthorizedViewsResponse": {"description": "The response from a ListAuthorizedViews request.", "id": "GoogleCloudContactcenterinsightsV1ListAuthorizedViewsResponse", "properties": {"authorizedViews": {"description": "The AuthorizedViews under the parent.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1AuthorizedView"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ListConversationsResponse": {"description": "The response of listing conversations.", "id": "GoogleCloudContactcenterinsightsV1ListConversationsResponse", "properties": {"conversations": {"description": "The conversations that match the request.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1Conversation"}, "type": "array"}, "nextPageToken": {"description": "A token which can be sent as `page_token` to retrieve the next page. If this field is set, it means there is another page available. If it is not set, it means no other pages are available.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ListFeedbackLabelsResponse": {"description": "The response for listing feedback labels.", "id": "GoogleCloudContactcenterinsightsV1ListFeedbackLabelsResponse", "properties": {"feedbackLabels": {"description": "The feedback labels that match the request.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1FeedbackLabel"}, "type": "array"}, "nextPageToken": {"description": "The next page token.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ListIssueModelsResponse": {"description": "The response of listing issue models.", "id": "GoogleCloudContactcenterinsightsV1ListIssueModelsResponse", "properties": {"issueModels": {"description": "The issue models that match the request.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1IssueModel"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ListIssuesResponse": {"description": "The response of listing issues.", "id": "GoogleCloudContactcenterinsightsV1ListIssuesResponse", "properties": {"issues": {"description": "The issues that match the request.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1Issue"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ListPhraseMatchersResponse": {"description": "The response of listing phrase matchers.", "id": "GoogleCloudContactcenterinsightsV1ListPhraseMatchersResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "phraseMatchers": {"description": "The phrase matchers that match the request.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1PhraseMatcher"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ListQaQuestionsResponse": {"description": "The response from a ListQaQuestions request.", "id": "GoogleCloudContactcenterinsightsV1ListQaQuestionsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "qaQuestions": {"description": "The QaQuestions under the parent.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1QaQuestion"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ListQaScorecardRevisionsResponse": {"description": "The response from a ListQaScorecardRevisions request.", "id": "GoogleCloudContactcenterinsightsV1ListQaScorecardRevisionsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "qaScorecardRevisions": {"description": "The QaScorecards under the parent.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1QaScorecardRevision"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ListQaScorecardsResponse": {"description": "The response from a ListQaScorecards request.", "id": "GoogleCloudContactcenterinsightsV1ListQaScorecardsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "qaScorecards": {"description": "The QaScorecards under the parent.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1QaScorecard"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1ListViewsResponse": {"description": "The response of listing views.", "id": "GoogleCloudContactcenterinsightsV1ListViewsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "views": {"description": "The views that match the request.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1View"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1PhraseMatchData": {"description": "The data for a matched phrase matcher. Represents information identifying a phrase matcher for a given match.", "id": "GoogleCloudContactcenterinsightsV1PhraseMatchData", "properties": {"displayName": {"description": "The human-readable name of the phrase matcher.", "type": "string"}, "phraseMatcher": {"description": "The unique identifier (the resource name) of the phrase matcher.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1PhraseMatchRule": {"description": "The data for a phrase match rule.", "id": "GoogleCloudContactcenterinsightsV1PhraseMatchRule", "properties": {"config": {"$ref": "GoogleCloudContactcenterinsightsV1PhraseMatchRuleConfig", "description": "Provides additional information about the rule that specifies how to apply the rule."}, "negated": {"description": "Specifies whether the phrase must be missing from the transcript segment or present in the transcript segment.", "type": "boolean"}, "query": {"description": "Required. The phrase to be matched.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1PhraseMatchRuleConfig": {"description": "Configuration information of a phrase match rule.", "id": "GoogleCloudContactcenterinsightsV1PhraseMatchRuleConfig", "properties": {"exactMatchConfig": {"$ref": "GoogleCloudContactcenterinsightsV1ExactMatchConfig", "description": "The configuration for the exact match rule."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1PhraseMatchRuleGroup": {"description": "A message representing a rule in the phrase matcher.", "id": "GoogleCloudContactcenterinsightsV1PhraseMatchRuleGroup", "properties": {"phraseMatchRules": {"description": "A list of phrase match rules that are included in this group.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1PhraseMatchRule"}, "type": "array"}, "type": {"description": "Required. The type of this phrase match rule group.", "enum": ["PHRASE_MATCH_RULE_GROUP_TYPE_UNSPECIFIED", "ALL_OF", "ANY_OF"], "enumDescriptions": ["Unspecified.", "Must meet all phrase match rules or there is no match.", "If any of the phrase match rules are met, there is a match."], "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1PhraseMatcher": {"description": "The phrase matcher resource.", "id": "GoogleCloudContactcenterinsightsV1PhraseMatcher", "properties": {"activationUpdateTime": {"description": "Output only. The most recent time at which the activation status was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "active": {"description": "Applies the phrase matcher only when it is active.", "type": "boolean"}, "displayName": {"description": "The human-readable name of the phrase matcher.", "type": "string"}, "name": {"description": "The resource name of the phrase matcher. Format: projects/{project}/locations/{location}/phraseMatchers/{phrase_matcher}", "type": "string"}, "phraseMatchRuleGroups": {"description": "A list of phase match rule groups that are included in this matcher.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1PhraseMatchRuleGroup"}, "type": "array"}, "revisionCreateTime": {"description": "Output only. The timestamp of when the revision was created. It is also the create time when a new matcher is added.", "format": "google-datetime", "readOnly": true, "type": "string"}, "revisionId": {"description": "Output only. Immutable. The revision ID of the phrase matcher. A new revision is committed whenever the matcher is changed, except when it is activated or deactivated. A server generated random ID will be used. Example: locations/global/phraseMatchers/my-first-matcher@1234567", "readOnly": true, "type": "string"}, "roleMatch": {"description": "The role whose utterances the phrase matcher should be matched against. If the role is ROLE_UNSPECIFIED it will be matched against any utterances in the transcript.", "enum": ["ROLE_UNSPECIFIED", "HUMAN_AGENT", "AUTOMATED_AGENT", "END_USER", "ANY_AGENT"], "enumDescriptions": ["<PERSON><PERSON><PERSON><PERSON>'s role is not set.", "Participant is a human agent.", "Participant is an automated agent.", "<PERSON><PERSON><PERSON><PERSON> is an end user who conversed with the contact center.", "Participant is either a human or automated agent."], "type": "string"}, "type": {"description": "Required. The type of this phrase matcher.", "enum": ["PHRASE_MATCHER_TYPE_UNSPECIFIED", "ALL_OF", "ANY_OF"], "enumDescriptions": ["Unspecified.", "Must meet all phrase match rule groups or there is no match.", "If any of the phrase match rule groups are met, there is a match."], "type": "string"}, "updateTime": {"description": "Output only. The most recent time at which the phrase matcher was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "versionTag": {"description": "The customized version tag to use for the phrase matcher. If not specified, it will default to `revision_id`.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QaAnswer": {"description": "An answer to a QaQuestion.", "id": "GoogleCloudContactcenterinsightsV1QaAnswer", "properties": {"answerSources": {"description": "List of all individual answers given to the question.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1QaAnswerAnswerSource"}, "type": "array"}, "answerValue": {"$ref": "GoogleCloudContactcenterinsightsV1QaAnswerAnswerValue", "description": "The main answer value, incorporating any manual edits if they exist."}, "conversation": {"description": "The conversation the answer applies to.", "type": "string"}, "qaQuestion": {"description": "The QaQuestion answered by this answer.", "type": "string"}, "questionBody": {"description": "Question text. E.g., \"Did the agent greet the customer?\"", "type": "string"}, "tags": {"description": "User-defined list of arbitrary tags. Matches the value from QaScorecard.ScorecardQuestion.tags. Used for grouping/organization and for weighting the score of each answer.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QaAnswerAnswerSource": {"description": "A question may have multiple answers from varying sources, one of which becomes the \"main\" answer above. AnswerSource represents each individual answer.", "id": "GoogleCloudContactcenterinsightsV1QaAnswerAnswerSource", "properties": {"answerValue": {"$ref": "GoogleCloudContactcenterinsightsV1QaAnswerAnswerValue", "description": "The answer value from this source."}, "sourceType": {"description": "What created the answer.", "enum": ["SOURCE_TYPE_UNSPECIFIED", "SYSTEM_GENERATED", "MANUAL_EDIT"], "enumDescriptions": ["Source type is unspecified.", "Answer was system-generated; created during an Insights analysis.", "Answer was created by a human via manual edit."], "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QaAnswerAnswerValue": {"description": "Message for holding the value of a QaAnswer. QaQuestion.AnswerChoice defines the possible answer values for a question.", "id": "GoogleCloudContactcenterinsightsV1QaAnswerAnswerValue", "properties": {"boolValue": {"description": "Boolean value.", "type": "boolean"}, "key": {"description": "A short string used as an identifier. Matches the value used in QaQuestion.AnswerChoice.key.", "type": "string"}, "naValue": {"description": "A value of \"Not Applicable (N/A)\". Should only ever be `true`.", "type": "boolean"}, "normalizedScore": {"description": "Output only. Normalized score of the questions. Calculated as score / potential_score.", "format": "double", "readOnly": true, "type": "number"}, "numValue": {"description": "Numerical value.", "format": "double", "type": "number"}, "potentialScore": {"description": "Output only. The maximum potential score of the question.", "format": "double", "readOnly": true, "type": "number"}, "score": {"description": "Output only. Numerical score of the answer.", "format": "double", "readOnly": true, "type": "number"}, "strValue": {"description": "String value.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QaQuestion": {"description": "A single question to be scored by the Insights QA feature.", "id": "GoogleCloudContactcenterinsightsV1QaQuestion", "properties": {"abbreviation": {"description": "Short, descriptive string, used in the UI where it's not practical to display the full question body. E.g., \"Greeting\".", "type": "string"}, "answerChoices": {"description": "A list of valid answers to the question, which the LLM must choose from.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1QaQuestionAnswerChoice"}, "type": "array"}, "answerInstructions": {"description": "Instructions describing how to determine the answer.", "type": "string"}, "createTime": {"description": "Output only. The time at which this question was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "metrics": {"$ref": "GoogleCloudContactcenterinsightsV1QaQuestionMetrics", "description": "Metrics of the underlying tuned LLM over a holdout/test set while fine tuning the underlying LLM for the given question. This field will only be populated if and only if the question is part of a scorecard revision that has been tuned."}, "name": {"description": "Identifier. The resource name of the question. Format: projects/{project}/locations/{location}/qaScorecards/{qa_scorecard}/revisions/{revision}/qaQuestions/{qa_question}", "type": "string"}, "order": {"description": "Defines the order of the question within its parent scorecard revision.", "format": "int32", "type": "integer"}, "questionBody": {"description": "Question text. E.g., \"Did the agent greet the customer?\"", "type": "string"}, "tags": {"description": "Questions are tagged for categorization and scoring. Tags can either be: - Default Tags: These are predefined categories. They are identified by their string value (e.g., \"BUSINESS\", \"COMPLIANCE\", and \"CUSTOMER\"). - Custom Tags: These are user-defined categories. They are identified by their full resource name (e.g., projects/{project}/locations/{location}/qaQuestionTags/{qa_question_tag}). Both default and custom tags are used to group questions and to influence the scoring of each question.", "items": {"type": "string"}, "type": "array"}, "tuningMetadata": {"$ref": "GoogleCloudContactcenterinsightsV1QaQuestionTuningMetadata", "description": "Metadata about the tuning operation for the question.This field will only be populated if and only if the question is part of a scorecard revision that has been tuned."}, "updateTime": {"description": "Output only. The most recent time at which the question was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QaQuestionAnswerChoice": {"description": "Message representing a possible answer to the question.", "id": "GoogleCloudContactcenterinsightsV1QaQuestionAnswerChoice", "properties": {"boolValue": {"description": "Boolean value.", "type": "boolean"}, "key": {"description": "A short string used as an identifier.", "type": "string"}, "naValue": {"description": "A value of \"Not Applicable (N/A)\". If provided, this field may only be set to `true`. If a question receives this answer, it will be excluded from any score calculations.", "type": "boolean"}, "numValue": {"description": "Numerical value.", "format": "double", "type": "number"}, "score": {"description": "Numerical score of the answer, used for generating the overall score of a QaScorecardResult. If the answer uses na_value, this field is unused.", "format": "double", "type": "number"}, "strValue": {"description": "String value.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QaQuestionMetrics": {"description": "A wrapper representing metrics calculated against a test-set on a LLM that was fine tuned for this question.", "id": "GoogleCloudContactcenterinsightsV1QaQuestionMetrics", "properties": {"accuracy": {"description": "Output only. Accuracy of the model. Measures the percentage of correct answers the model gave on the test set.", "format": "double", "readOnly": true, "type": "number"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QaQuestionTuningMetadata": {"description": "<PERSON><PERSON><PERSON> about the tuning operation for the question. Will only be set if a scorecard containing this question has been tuned.", "id": "GoogleCloudContactcenterinsightsV1QaQuestionTuningMetadata", "properties": {"datasetValidationWarnings": {"description": "A list of any applicable data validation warnings about the question's feedback labels.", "items": {"enum": ["DATASET_VALIDATION_WARNING_UNSPECIFIED", "TOO_MANY_INVALID_FEEDBACK_LABELS", "INSUFFICIENT_FEEDBACK_LABELS", "INSUFFICIENT_FEEDBACK_LABELS_PER_ANSWER", "ALL_FEEDBACK_LABELS_HAVE_THE_SAME_ANSWER"], "enumDescriptions": ["Unspecified data validation warning.", "A non-trivial percentage of the feedback labels are invalid.", "The quantity of valid feedback labels provided is less than the recommended minimum.", "One or more of the answers have less than the recommended minimum of feedback labels.", "All the labels in the dataset come from a single answer choice."], "type": "string"}, "type": "array"}, "totalValidLabelCount": {"description": "Total number of valid labels provided for the question at the time of tuining.", "format": "int64", "type": "string"}, "tuningError": {"description": "Error status of the tuning operation for the question. Will only be set if the tuning operation failed.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QaScorecard": {"description": "A QaScorecard represents a collection of questions to be scored during analysis.", "id": "GoogleCloudContactcenterinsightsV1QaScorecard", "properties": {"createTime": {"description": "Output only. The time at which this scorecard was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "A text description explaining the intent of the scorecard.", "type": "string"}, "displayName": {"description": "The user-specified display name of the scorecard.", "type": "string"}, "name": {"description": "Identifier. The scorecard name. Format: projects/{project}/locations/{location}/qaScorecards/{qa_scorecard}", "type": "string"}, "updateTime": {"description": "Output only. The most recent time at which the scorecard was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QaScorecardResult": {"description": "The results of scoring a single conversation against a QaScorecard. Contains a collection of QaAnswers and aggregate score.", "id": "GoogleCloudContactcenterinsightsV1QaScorecardResult", "properties": {"agentId": {"description": "ID of the agent that handled the conversation.", "type": "string"}, "conversation": {"description": "The conversation scored by this result.", "type": "string"}, "createTime": {"description": "Output only. The timestamp that the revision was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Identifier. The name of the scorecard result. Format: projects/{project}/locations/{location}/qaScorecardResults/{qa_scorecard_result}", "type": "string"}, "normalizedScore": {"description": "The normalized score, which is the score divided by the potential score. Any manual edits are included if they exist.", "format": "double", "type": "number"}, "potentialScore": {"description": "The maximum potential overall score of the scorecard. Any questions answered using `na_value` are excluded from this calculation.", "format": "double", "type": "number"}, "qaAnswers": {"description": "Set of QaAnswers represented in the result.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1QaAnswer"}, "type": "array"}, "qaScorecardRevision": {"description": "The QaScorecardRevision scored by this result.", "type": "string"}, "qaTagResults": {"description": "Collection of tags and their scores.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1QaScorecardResultQaTagResult"}, "type": "array"}, "score": {"description": "The overall numerical score of the result, incorporating any manual edits if they exist.", "format": "double", "type": "number"}, "scoreSources": {"description": "List of all individual score sets.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1QaScorecardResultScoreSource"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QaScorecardResultQaTagResult": {"description": "Tags and their corresponding results.", "id": "GoogleCloudContactcenterinsightsV1QaScorecardResultQaTagResult", "properties": {"normalizedScore": {"description": "The normalized score the tag applies to.", "format": "double", "type": "number"}, "potentialScore": {"description": "The potential score the tag applies to.", "format": "double", "type": "number"}, "score": {"description": "The score the tag applies to.", "format": "double", "type": "number"}, "tag": {"description": "The tag the score applies to.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QaScorecardResultScoreSource": {"description": "A scorecard result may have multiple sets of scores from varying sources, one of which becomes the \"main\" answer above. A ScoreSource represents each individual set of scores.", "id": "GoogleCloudContactcenterinsightsV1QaScorecardResultScoreSource", "properties": {"normalizedScore": {"description": "The normalized score, which is the score divided by the potential score.", "format": "double", "type": "number"}, "potentialScore": {"description": "The maximum potential overall score of the scorecard. Any questions answered using `na_value` are excluded from this calculation.", "format": "double", "type": "number"}, "qaTagResults": {"description": "Collection of tags and their scores.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1QaScorecardResultQaTagResult"}, "type": "array"}, "score": {"description": "The overall numerical score of the result.", "format": "double", "type": "number"}, "sourceType": {"description": "What created the score.", "enum": ["SOURCE_TYPE_UNSPECIFIED", "SYSTEM_GENERATED_ONLY", "INCLUDES_MANUAL_EDITS"], "enumDescriptions": ["Source type is unspecified.", "Score is derived only from system-generated answers.", "Score is derived from both system-generated answers, and includes any manual edits if they exist."], "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QaScorecardRevision": {"description": "A revision of a QaScorecard. Modifying published scorecard fields would invalidate existing scorecard results — the questions may have changed, or the score weighting will make existing scores impossible to understand. So changes must create a new revision, rather than modifying the existing resource.", "id": "GoogleCloudContactcenterinsightsV1QaScorecardRevision", "properties": {"alternateIds": {"description": "Output only. Alternative IDs for this revision of the scorecard, e.g., `latest`.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "createTime": {"description": "Output only. The timestamp that the revision was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Identifier. The name of the scorecard revision. Format: projects/{project}/locations/{location}/qaScorecards/{qa_scorecard}/revisions/{revision}", "type": "string"}, "snapshot": {"$ref": "GoogleCloudContactcenterinsightsV1QaScorecard", "description": "The snapshot of the scorecard at the time of this revision's creation."}, "state": {"description": "Output only. State of the scorecard revision, indicating whether it's ready to be used in analysis.", "enum": ["STATE_UNSPECIFIED", "EDITABLE", "TRAINING", "TRAINING_FAILED", "READY", "DELETING", "TRAINING_CANCELLED"], "enumDescriptions": ["Unspecified.", "The scorecard revision can be edited.", "Scorecard model training is in progress.", "Scorecard revision model training failed.", "The revision can be used in analysis.", "Scorecard is being deleted.", "Scorecard model training was explicitly cancelled by the user."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QueryMetricsMetadata": {"description": "The metadata from querying metrics.", "id": "GoogleCloudContactcenterinsightsV1QueryMetricsMetadata", "properties": {"resultIsTruncated": {"description": "Whether the result rows were truncated because the result row size is too large to materialize.", "type": "boolean"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QueryMetricsRequest": {"description": "The request for querying metrics.", "id": "GoogleCloudContactcenterinsightsV1QueryMetricsRequest", "properties": {"dimensions": {"description": "The dimensions that determine the grouping key for the query. Defaults to no dimension if this field is unspecified. If a dimension is specified, its key must also be specified. Each dimension's key must be unique. If a time granularity is also specified, metric values in the dimension will be bucketed by this granularity. Up to one dimension is supported for now.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1Dimension"}, "type": "array"}, "filter": {"description": "Required. Filter to select a subset of conversations to compute the metrics. Must specify a window of the conversation create time to compute the metrics. The returned metrics will be from the range [DATE(starting create time), DATE(ending create time)). ", "type": "string"}, "measureMask": {"description": "Measures to return. Defaults to all measures if this field is unspecified. A valid mask should traverse from the `measure` field from the response. For example, a path from a measure mask to get the conversation count is \"conversation_measure.count\".", "format": "google-fieldmask", "type": "string"}, "timeGranularity": {"description": "The time granularity of each data point in the time series. Defaults to NONE if this field is unspecified.", "enum": ["TIME_GRANULARITY_UNSPECIFIED", "NONE", "DAILY", "HOURLY", "PER_MINUTE", "PER_5_MINUTES", "MONTHLY"], "enumDescriptions": ["The time granularity is unspecified and will default to NONE.", "No time granularity. The response won't contain a time series. This is the default value if no time granularity is specified.", "Data points in the time series will aggregate at a daily granularity. 1 day means [midnight to midnight).", "Data points in the time series will aggregate at a daily granularity. 1 HOUR means [01:00 to 02:00).", "Data points in the time series will aggregate at a daily granularity. PER_MINUTE means [01:00 to 01:01).", "Data points in the time series will aggregate at a 1 minute granularity. PER_5_MINUTES means [01:00 to 01:05).", "Data points in the time series will aggregate at a monthly granularity. 1 MONTH means [01st of the month to 1st of the next month)."], "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QueryMetricsResponse": {"description": "The response for querying metrics.", "id": "GoogleCloudContactcenterinsightsV1QueryMetricsResponse", "properties": {"location": {"description": "Required. The location of the data. \"projects/{project}/locations/{location}\"", "type": "string"}, "macroAverageSlice": {"$ref": "GoogleCloudContactcenterinsightsV1QueryMetricsResponseSlice", "description": "The macro average slice contains aggregated averages across the selected dimension. i.e. if group_by agent is specified this field will contain the average across all agents. This field is only populated if the request specifies a Dimension."}, "slices": {"description": "A slice contains a total and (if the request specified a time granularity) a time series of metric values. Each slice contains a unique combination of the cardinality of dimensions from the request.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1QueryMetricsResponseSlice"}, "type": "array"}, "updateTime": {"description": "The metrics last update time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QueryMetricsResponseSlice": {"description": "A slice contains a total and (if the request specified a time granularity) a time series of metric values. Each slice contains a unique combination of the cardinality of dimensions from the request. For example, if the request specifies a single ISSUE dimension and it has a cardinality of 2 (i.e. the data used to compute the metrics has 2 issues in total), the response will have 2 slices: * Slice 1 -> dimensions=[Issue 1] * Slice 2 -> dimensions=[Issue 2]", "id": "GoogleCloudContactcenterinsightsV1QueryMetricsResponseSlice", "properties": {"dimensions": {"description": "A unique combination of dimensions that this slice represents.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1Dimension"}, "type": "array"}, "timeSeries": {"$ref": "GoogleCloudContactcenterinsightsV1QueryMetricsResponseSliceTimeSeries", "description": "A time series of metric values. This is only populated if the request specifies a time granularity other than NONE."}, "total": {"$ref": "GoogleCloudContactcenterinsightsV1QueryMetricsResponseSliceDataPoint", "description": "The total metric value. The interval of this data point is [starting create time, ending create time) from the request."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QueryMetricsResponseSliceDataPoint": {"description": "A data point contains the metric values mapped to an interval.", "id": "GoogleCloudContactcenterinsightsV1QueryMetricsResponseSliceDataPoint", "properties": {"conversationMeasure": {"$ref": "GoogleCloudContactcenterinsightsV1QueryMetricsResponseSliceDataPointConversationMeasure", "description": "The measure related to conversations."}, "interval": {"$ref": "GoogleTypeInterval", "description": "The interval that this data point represents. * If this is the total data point, the interval is [starting create time, ending create time) from the request. * If this a data point from the time series, the interval is [time, time + time granularity from the request)."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QueryMetricsResponseSliceDataPointConversationMeasure": {"description": "The measure related to conversations.", "id": "GoogleCloudContactcenterinsightsV1QueryMetricsResponseSliceDataPointConversationMeasure", "properties": {"averageAgentSentimentScore": {"description": "The average agent's sentiment score.", "format": "float", "type": "number"}, "averageClientSentimentScore": {"description": "The average client's sentiment score.", "format": "float", "type": "number"}, "averageCustomerSatisfactionRating": {"description": "The average customer satisfaction rating.", "format": "double", "type": "number"}, "averageDuration": {"description": "The average duration.", "format": "google-duration", "type": "string"}, "averageQaNormalizedScore": {"description": "Average QA normalized score. Will exclude 0's in average calculation.", "format": "double", "type": "number"}, "averageQaQuestionNormalizedScore": {"description": "Average QA normalized score averaged for questions averaged across all revisions of the parent scorecard. Will be only populated if the request specifies a dimension of QA_QUESTION_ID.", "format": "double", "type": "number"}, "averageSilencePercentage": {"description": "The average silence percentage.", "format": "float", "type": "number"}, "averageTurnCount": {"description": "The average turn count.", "format": "float", "type": "number"}, "conversationCount": {"description": "The conversation count.", "format": "int32", "type": "integer"}, "qaTagScores": {"description": "Average QA normalized score for all the tags.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1QueryMetricsResponseSliceDataPointConversationMeasureQaTagScore"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QueryMetricsResponseSliceDataPointConversationMeasureQaTagScore": {"description": "Average QA normalized score for the tag.", "id": "GoogleCloudContactcenterinsightsV1QueryMetricsResponseSliceDataPointConversationMeasureQaTagScore", "properties": {"averageTagNormalizedScore": {"description": "Average tag normalized score per tag.", "format": "double", "type": "number"}, "tag": {"description": "Tag name.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QueryMetricsResponseSliceTimeSeries": {"description": "A time series of metric values.", "id": "GoogleCloudContactcenterinsightsV1QueryMetricsResponseSliceTimeSeries", "properties": {"dataPoints": {"description": "The data points that make up the time series .", "items": {"$ref": "GoogleCloudContactcenterinsightsV1QueryMetricsResponseSliceDataPoint"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QueryPerformanceOverviewMetadata": {"description": "The metadata for querying performance overview.", "id": "GoogleCloudContactcenterinsightsV1QueryPerformanceOverviewMetadata", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1QueryPerformanceOverviewResponse": {"description": "The response for querying performance overview.", "id": "GoogleCloudContactcenterinsightsV1QueryPerformanceOverviewResponse", "properties": {"summaryText": {"description": "The summary text of the performance.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1RedactionConfig": {"description": "DLP resources used for redaction while ingesting conversations. DLP settings are applied to conversations ingested from the `UploadConversation` and `IngestConversations` endpoints, including conversation coming from CCAI Platform. They are not applied to conversations ingested from the `CreateConversation` endpoint or the Dialogflow / Agent Assist runtime integrations. When using Dialogflow / Agent Assist runtime integrations, redaction should be performed in Dialogflow / Agent Assist.", "id": "GoogleCloudContactcenterinsightsV1RedactionConfig", "properties": {"deidentifyTemplate": {"description": "The fully-qualified DLP deidentify template resource name. Format: `projects/{project}/deidentifyTemplates/{template}`", "type": "string"}, "inspectTemplate": {"description": "The fully-qualified DLP inspect template resource name. Format: `projects/{project}/locations/{location}/inspectTemplates/{template}`", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1RuntimeAnnotation": {"description": "An annotation that was generated during the customer and agent interaction.", "id": "GoogleCloudContactcenterinsightsV1RuntimeAnnotation", "properties": {"annotationId": {"description": "The unique identifier of the annotation. Format: projects/{project}/locations/{location}/conversationDatasets/{dataset}/conversationDataItems/{data_item}/conversationAnnotations/{annotation}", "type": "string"}, "answerFeedback": {"$ref": "GoogleCloudContactcenterinsightsV1AnswerFeedback", "description": "The feedback that the customer has about the answer in `data`."}, "articleSuggestion": {"$ref": "GoogleCloudContactcenterinsightsV1ArticleSuggestionData", "description": "Agent Assist Article Suggestion data."}, "conversationSummarizationSuggestion": {"$ref": "GoogleCloudContactcenterinsightsV1ConversationSummarizationSuggestionData", "description": "Conversation summarization suggestion data."}, "createTime": {"description": "The time at which this annotation was created.", "format": "google-datetime", "type": "string"}, "dialogflowInteraction": {"$ref": "GoogleCloudContactcenterinsightsV1DialogflowInteractionData", "description": "Dialogflow interaction data."}, "endBoundary": {"$ref": "GoogleCloudContactcenterinsightsV1AnnotationBoundary", "description": "The boundary in the conversation where the annotation ends, inclusive."}, "faqAnswer": {"$ref": "GoogleCloudContactcenterinsightsV1FaqAnswerData", "description": "Agent Assist FAQ answer data."}, "smartComposeSuggestion": {"$ref": "GoogleCloudContactcenterinsightsV1SmartComposeSuggestionData", "description": "Agent Assist Smart Compose suggestion data."}, "smartReply": {"$ref": "GoogleCloudContactcenterinsightsV1SmartReplyData", "description": "Agent Assist Smart Reply data."}, "startBoundary": {"$ref": "GoogleCloudContactcenterinsightsV1AnnotationBoundary", "description": "The boundary in the conversation where the annotation starts, inclusive."}, "userInput": {"$ref": "GoogleCloudContactcenterinsightsV1RuntimeAnnotationUserInput", "description": "Explicit input used for generating the answer"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1RuntimeAnnotationUserInput": {"description": "Explicit input used for generating the answer", "id": "GoogleCloudContactcenterinsightsV1RuntimeAnnotationUserInput", "properties": {"generatorName": {"description": "The resource name of associated generator. Format: `projects//locations//generators/`", "type": "string"}, "query": {"description": "Query text. Article Search uses this to store the input query used to generate the search results.", "type": "string"}, "querySource": {"description": "Query source for the answer.", "enum": ["QUERY_SOURCE_UNSPECIFIED", "AGENT_QUERY", "SUGGESTED_QUERY"], "enumDescriptions": ["Unknown query source.", "The query is from agents.", "The query is a query from previous suggestions, e.g. from a preceding SuggestKnowledgeAssist response."], "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1SampleConversationsMetadata": {"description": "The metadata for an SampleConversations operation.", "id": "GoogleCloudContactcenterinsightsV1SampleConversationsMetadata", "properties": {"createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "partialErrors": {"description": "Output only. Partial errors during sample conversations operation that might cause the operation output to be incomplete.", "items": {"$ref": "GoogleRpcStatus"}, "readOnly": true, "type": "array"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1SampleConversationsRequest", "description": "Output only. The original request for sample conversations to dataset.", "readOnly": true}, "sampleConversationsStats": {"$ref": "GoogleCloudContactcenterinsightsV1SampleConversationsMetadataSampleConversationsStats", "description": "Output only. Statistics for SampleConversations operation.", "readOnly": true}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1SampleConversationsMetadataSampleConversationsStats": {"description": "Statistics for SampleConversations operation.", "id": "GoogleCloudContactcenterinsightsV1SampleConversationsMetadataSampleConversationsStats", "properties": {"failedSampleCount": {"description": "Output only. The number of objects which were unable to be sampled due to errors. The errors are populated in the partial_errors field.", "format": "int32", "readOnly": true, "type": "integer"}, "successfulSampleCount": {"description": "Output only. The number of new conversations added during this sample operation.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1SampleConversationsRequest": {"description": "The request to sample conversations to a dataset.", "id": "GoogleCloudContactcenterinsightsV1SampleConversationsRequest", "properties": {"destinationDataset": {"$ref": "GoogleCloudContactcenterinsightsV1Dataset", "description": "The dataset resource to copy the sampled conversations to."}, "parent": {"description": "Required. The parent resource of the dataset.", "type": "string"}, "sampleRule": {"$ref": "GoogleCloudContactcenterinsightsV1SampleRule", "description": "Optional. The sample rule used for sampling conversations."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1SampleConversationsResponse": {"description": "The response to an SampleConversations operation.", "id": "GoogleCloudContactcenterinsightsV1SampleConversationsResponse", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1SampleRule": {"description": "Message for sampling conversations.", "id": "GoogleCloudContactcenterinsightsV1SampleRule", "properties": {"conversationFilter": {"description": "To specify the filter for the conversions that should apply this sample rule. An empty filter means this sample rule applies to all conversations.", "type": "string"}, "dimension": {"description": "Optional. Group by dimension to sample the conversation. If no dimension is provided, the sampling will be applied to the project level. Current supported dimensions is 'quality_metadata.agent_info.agent_id'.", "type": "string"}, "samplePercentage": {"description": "Percentage of conversations that we should sample based on the dimension between [0, 100].", "format": "double", "type": "number"}, "sampleRow": {"description": "Number of the conversations that we should sample based on the dimension.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1SearchAuthorizedViewsResponse": {"description": "The response from a ListAuthorizedViews request.", "id": "GoogleCloudContactcenterinsightsV1SearchAuthorizedViewsResponse", "properties": {"authorizedViews": {"description": "The AuthorizedViews under the parent.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1AuthorizedView"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1SentimentData": {"description": "The data for a sentiment annotation.", "id": "GoogleCloudContactcenterinsightsV1SentimentData", "properties": {"magnitude": {"description": "A non-negative number from 0 to infinity which represents the absolute magnitude of sentiment regardless of score.", "format": "float", "type": "number"}, "score": {"description": "The sentiment score between -1.0 (negative) and 1.0 (positive).", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1Settings": {"description": "The CCAI Insights project wide settings. Use these settings to configure the behavior of Insights. View these settings with [`getsettings`](https://cloud.google.com/contact-center/insights/docs/reference/rest/v1/projects.locations/getSettings) and change the settings with [`updateSettings`](https://cloud.google.com/contact-center/insights/docs/reference/rest/v1/projects.locations/updateSettings).", "id": "GoogleCloudContactcenterinsightsV1Settings", "properties": {"analysisConfig": {"$ref": "GoogleCloudContactcenterinsightsV1SettingsAnalysisConfig", "description": "Default analysis settings."}, "conversationTtl": {"description": "The default TTL for newly-created conversations. If a conversation has a specified expiration, that value will be used instead. Changing this value will not change the expiration of existing conversations. Conversations with no expire time persist until they are deleted.", "format": "google-duration", "type": "string"}, "createTime": {"description": "Output only. The time at which the settings was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "languageCode": {"description": "A language code to be applied to each transcript segment unless the segment already specifies a language code. Language code defaults to \"en-US\" if it is neither specified on the segment nor here.", "type": "string"}, "name": {"description": "Immutable. The resource name of the settings resource. Format: projects/{project}/locations/{location}/settings", "type": "string"}, "pubsubNotificationSettings": {"additionalProperties": {"type": "string"}, "description": "A map that maps a notification trigger to a Pub/Sub topic. Each time a specified trigger occurs, Insights will notify the corresponding Pub/Sub topic. Keys are notification triggers. Supported keys are: * \"all-triggers\": Notify each time any of the supported triggers occurs. * \"create-analysis\": Notify each time an analysis is created. * \"create-conversation\": Notify each time a conversation is created. * \"export-insights-data\": Notify each time an export is complete. * \"ingest-conversations\": Notify each time an IngestConversations LRO is complete. * \"update-conversation\": Notify each time a conversation is updated via UpdateConversation. * \"upload-conversation\": Notify when an UploadConversation LRO is complete. Values are Pub/Sub topics. The format of each Pub/Sub topic is: projects/{project}/topics/{topic}", "type": "object"}, "redactionConfig": {"$ref": "GoogleCloudContactcenterinsightsV1RedactionConfig", "description": "Default DLP redaction resources to be applied while ingesting conversations. This applies to conversations ingested from the `UploadConversation` and `IngestConversations` endpoints, including conversations coming from CCAI Platform."}, "speechConfig": {"$ref": "GoogleCloudContactcenterinsightsV1SpeechConfig", "description": "Optional. Default Speech-to-Text resources to use while ingesting audio files. Optional, CCAI Insights will create a default if not provided. This applies to conversations ingested from the `UploadConversation` and `IngestConversations` endpoints, including conversations coming from CCAI Platform."}, "updateTime": {"description": "Output only. The time at which the settings were last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1SettingsAnalysisConfig": {"description": "Default configuration when creating Analyses in Insights.", "id": "GoogleCloudContactcenterinsightsV1SettingsAnalysisConfig", "properties": {"annotatorSelector": {"$ref": "GoogleCloudContactcenterinsightsV1AnnotatorSelector", "description": "To select the annotators to run and the phrase matchers to use (if any). If not specified, all annotators will be run."}, "runtimeIntegrationAnalysisPercentage": {"description": "Percentage of conversations created using Dialogflow runtime integration to analyze automatically, between [0, 100].", "format": "double", "type": "number"}, "uploadConversationAnalysisPercentage": {"description": "Percentage of conversations created using the UploadConversation endpoint to analyze automatically, between [0, 100].", "format": "double", "type": "number"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1SilenceData": {"description": "The data for a silence annotation.", "id": "GoogleCloudContactcenterinsightsV1SilenceData", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1SmartComposeSuggestionData": {"description": "Agent Assist Smart Compose suggestion data.", "id": "GoogleCloudContactcenterinsightsV1SmartComposeSuggestionData", "properties": {"confidenceScore": {"description": "The system's confidence score that this suggestion is a good match for this conversation, ranging from 0.0 (completely uncertain) to 1.0 (completely certain).", "format": "double", "type": "number"}, "metadata": {"additionalProperties": {"type": "string"}, "description": "Map that contains metadata about the Smart Compose suggestion and the document from which it originates.", "type": "object"}, "queryRecord": {"description": "The name of the answer record. Format: projects/{project}/locations/{location}/answerRecords/{answer_record}", "type": "string"}, "suggestion": {"description": "The content of the suggestion.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1SmartReplyData": {"description": "Agent Assist Smart Reply data.", "id": "GoogleCloudContactcenterinsightsV1SmartReplyData", "properties": {"confidenceScore": {"description": "The system's confidence score that this reply is a good match for this conversation, ranging from 0.0 (completely uncertain) to 1.0 (completely certain).", "format": "double", "type": "number"}, "metadata": {"additionalProperties": {"type": "string"}, "description": "Map that contains metadata about the Smart Reply and the document from which it originates.", "type": "object"}, "queryRecord": {"description": "The name of the answer record. Format: projects/{project}/locations/{location}/answerRecords/{answer_record}", "type": "string"}, "reply": {"description": "The content of the reply.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1SpeechConfig": {"description": "Speech-to-Text configuration. Speech-to-Text settings are applied to conversations ingested from the `UploadConversation` and `IngestConversations` endpoints, including conversation coming from CCAI Platform. They are not applied to conversations ingested from the `CreateConversation` endpoint.", "id": "GoogleCloudContactcenterinsightsV1SpeechConfig", "properties": {"speechRecognizer": {"description": "The fully-qualified Speech Recognizer resource name. Format: `projects/{project_id}/locations/{location}/recognizer/{recognizer}`", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1TuneQaScorecardRevisionRequest": {"description": "Request for TuneQaScorecardRevision endpoint.", "id": "GoogleCloudContactcenterinsightsV1TuneQaScorecardRevisionRequest", "properties": {"filter": {"description": "Required. Filter for selecting the feedback labels that needs to be used for training. This filter can be used to limit the feedback labels used for tuning to a feedback labels created or updated for a specific time-window etc.", "type": "string"}, "validateOnly": {"description": "Optional. Run in validate only mode, no fine tuning will actually run. Data quality validations like training data distributions will run. Even when set to false, the data quality validations will still run but once the validations complete we will proceed with the fine tune, if applicable.", "type": "boolean"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1UndeployIssueModelMetadata": {"description": "Metadata for undeploying an issue model.", "id": "GoogleCloudContactcenterinsightsV1UndeployIssueModelMetadata", "properties": {"createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1UndeployIssueModelRequest", "description": "The original request for undeployment."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1UndeployIssueModelRequest": {"description": "The request to undeploy an issue model.", "id": "GoogleCloudContactcenterinsightsV1UndeployIssueModelRequest", "properties": {"name": {"description": "Required. The issue model to undeploy.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1UndeployIssueModelResponse": {"description": "The response to undeploy an issue model.", "id": "GoogleCloudContactcenterinsightsV1UndeployIssueModelResponse", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1UndeployQaScorecardRevisionRequest": {"description": "The request to undeploy a QaScorecardRevision", "id": "GoogleCloudContactcenterinsightsV1UndeployQaScorecardRevisionRequest", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1UploadConversationMetadata": {"description": "The metadata for an `UploadConversation` operation.", "id": "GoogleCloudContactcenterinsightsV1UploadConversationMetadata", "properties": {"analysisOperation": {"description": "Output only. The operation name for a successfully created analysis operation, if any.", "readOnly": true, "type": "string"}, "appliedRedactionConfig": {"$ref": "GoogleCloudContactcenterinsightsV1RedactionConfig", "description": "Output only. The redaction config applied to the uploaded conversation.", "readOnly": true}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1UploadConversationRequest", "description": "Output only. The original request.", "readOnly": true}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1UploadConversationRequest": {"description": "Request to upload a conversation.", "id": "GoogleCloudContactcenterinsightsV1UploadConversationRequest", "properties": {"conversation": {"$ref": "GoogleCloudContactcenterinsightsV1Conversation", "description": "Required. The conversation resource to create."}, "conversationId": {"description": "Optional. A unique ID for the new conversation. This ID will become the final component of the conversation's resource name. If no ID is specified, a server-generated ID will be used. This value should be 4-64 characters and must match the regular expression `^[a-z0-9-]{4,64}$`. Valid characters are `a-z-`", "type": "string"}, "parent": {"description": "Required. The parent resource of the conversation.", "type": "string"}, "redactionConfig": {"$ref": "GoogleCloudContactcenterinsightsV1RedactionConfig", "description": "Optional. DLP settings for transcript redaction. Will default to the config specified in Settings."}, "speechConfig": {"$ref": "GoogleCloudContactcenterinsightsV1SpeechConfig", "description": "Optional. Speech-to-Text configuration. Will default to the config specified in Settings."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1View": {"description": "The View resource.", "id": "GoogleCloudContactcenterinsightsV1View", "properties": {"createTime": {"description": "Output only. The time at which this view was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "The human-readable display name of the view.", "type": "string"}, "name": {"description": "Immutable. The resource name of the view. Format: projects/{project}/locations/{location}/views/{view}", "type": "string"}, "updateTime": {"description": "Output only. The most recent time at which the view was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "value": {"description": "A filter to reduce conversation results to a specific subset. Refer to https://cloud.google.com/contact-center/insights/docs/filtering for details.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1Analysis": {"description": "The analysis resource.", "id": "GoogleCloudContactcenterinsightsV1alpha1Analysis", "properties": {"analysisResult": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1AnalysisResult", "description": "Output only. The result of the analysis, which is populated when the analysis finishes.", "readOnly": true}, "annotatorSelector": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1AnnotatorSelector", "description": "To select the annotators to run and the phrase matchers to use (if any). If not specified, all annotators will be run."}, "createTime": {"description": "Output only. The time at which the analysis was created, which occurs when the long-running operation completes.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Immutable. The resource name of the analysis. Format: projects/{project}/locations/{location}/conversations/{conversation}/analyses/{analysis}", "type": "string"}, "requestTime": {"description": "Output only. The time at which the analysis was requested.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1AnalysisResult": {"description": "The result of an analysis.", "id": "GoogleCloudContactcenterinsightsV1alpha1AnalysisResult", "properties": {"callAnalysisMetadata": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1AnalysisResultCallAnalysisMetadata", "description": "Call-specific metadata created by the analysis."}, "endTime": {"description": "The time at which the analysis ended.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1AnalysisResultCallAnalysisMetadata": {"description": "Call-specific metadata created during analysis.", "id": "GoogleCloudContactcenterinsightsV1alpha1AnalysisResultCallAnalysisMetadata", "properties": {"annotations": {"description": "A list of call annotations that apply to this call.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1CallAnnotation"}, "type": "array"}, "entities": {"additionalProperties": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1Entity"}, "description": "All the entities in the call.", "type": "object"}, "intents": {"additionalProperties": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1Intent"}, "description": "All the matched intents in the call.", "type": "object"}, "issueModelResult": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1IssueModelResult", "description": "Overall conversation-level issue modeling result."}, "phraseMatchers": {"additionalProperties": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1PhraseMatchData"}, "description": "All the matched phrase matchers in the call.", "type": "object"}, "qaScorecardResults": {"description": "Results of scoring QaScorecards.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1QaScorecardResult"}, "type": "array"}, "sentiments": {"description": "Overall conversation-level sentiment for each channel of the call.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1ConversationLevelSentiment"}, "type": "array"}, "silence": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1ConversationLevelSilence", "description": "Overall conversation-level silence during the call."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1AnnotationBoundary": {"description": "A point in a conversation that marks the start or the end of an annotation.", "id": "GoogleCloudContactcenterinsightsV1alpha1AnnotationBoundary", "properties": {"transcriptIndex": {"description": "The index in the sequence of transcribed pieces of the conversation where the boundary is located. This index starts at zero.", "format": "int32", "type": "integer"}, "wordIndex": {"description": "The word index of this boundary with respect to the first word in the transcript piece. This index starts at zero.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1AnnotatorSelector": {"description": "Selector of all available annotators and phrase matchers to run.", "id": "GoogleCloudContactcenterinsightsV1alpha1AnnotatorSelector", "properties": {"issueModels": {"description": "The issue model to run. If not provided, the most recently deployed topic model will be used. The provided issue model will only be used for inference if the issue model is deployed and if run_issue_model_annotator is set to true. If more than one issue model is provided, only the first provided issue model will be used for inference.", "items": {"type": "string"}, "type": "array"}, "phraseMatchers": {"description": "The list of phrase matchers to run. If not provided, all active phrase matchers will be used. If inactive phrase matchers are provided, they will not be used. Phrase matchers will be run only if run_phrase_matcher_annotator is set to true. Format: projects/{project}/locations/{location}/phraseMatchers/{phrase_matcher}", "items": {"type": "string"}, "type": "array"}, "qaConfig": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1AnnotatorSelectorQaConfig", "description": "Configuration for the QA annotator."}, "runEntityAnnotator": {"description": "Whether to run the entity annotator.", "type": "boolean"}, "runIntentAnnotator": {"description": "Whether to run the intent annotator.", "type": "boolean"}, "runInterruptionAnnotator": {"description": "Whether to run the interruption annotator.", "type": "boolean"}, "runIssueModelAnnotator": {"description": "Whether to run the issue model annotator. A model should have already been deployed for this to take effect.", "type": "boolean"}, "runPhraseMatcherAnnotator": {"description": "Whether to run the active phrase matcher annotator(s).", "type": "boolean"}, "runQaAnnotator": {"description": "Whether to run the QA annotator.", "type": "boolean"}, "runSentimentAnnotator": {"description": "Whether to run the sentiment annotator.", "type": "boolean"}, "runSilenceAnnotator": {"description": "Whether to run the silence annotator.", "type": "boolean"}, "runSummarizationAnnotator": {"description": "Whether to run the summarization annotator.", "type": "boolean"}, "summarizationConfig": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1AnnotatorSelectorSummarizationConfig", "description": "Configuration for the summarization annotator."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1AnnotatorSelectorQaConfig": {"description": "Configuration for the QA feature.", "id": "GoogleCloudContactcenterinsightsV1alpha1AnnotatorSelectorQaConfig", "properties": {"scorecardList": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1AnnotatorSelectorQaConfigScorecardList", "description": "A manual list of scorecards to score."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1AnnotatorSelectorQaConfigScorecardList": {"description": "Container for a list of scorecards.", "id": "GoogleCloudContactcenterinsightsV1alpha1AnnotatorSelectorQaConfigScorecardList", "properties": {"qaScorecardRevisions": {"description": "List of QaScorecardRevisions.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1AnnotatorSelectorSummarizationConfig": {"description": "Configuration for summarization.", "id": "GoogleCloudContactcenterinsightsV1alpha1AnnotatorSelectorSummarizationConfig", "properties": {"conversationProfile": {"description": "Resource name of the Dialogflow conversation profile. Format: projects/{project}/locations/{location}/conversationProfiles/{conversation_profile}", "type": "string"}, "generator": {"description": "The resource name of the existing created generator. Format: projects//locations//generators/", "type": "string"}, "summarizationModel": {"description": "Default summarization model to be used.", "enum": ["SUMMARIZATION_MODEL_UNSPECIFIED", "BASELINE_MODEL", "BASELINE_MODEL_V2_0"], "enumDescriptions": ["Unspecified summarization model.", "The CCAI baseline model.", "The CCAI baseline model, V2.0."], "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1AnswerFeedback": {"description": "The feedback that the customer has about a certain answer in the conversation.", "id": "GoogleCloudContactcenterinsightsV1alpha1AnswerFeedback", "properties": {"clicked": {"description": "Indicates whether an answer or item was clicked by the human agent.", "type": "boolean"}, "correctnessLevel": {"description": "The correctness level of an answer.", "enum": ["CORRECTNESS_LEVEL_UNSPECIFIED", "NOT_CORRECT", "PARTIALLY_CORRECT", "FULLY_CORRECT"], "enumDescriptions": ["Correctness level unspecified.", "Answer is totally wrong.", "Answer is partially correct.", "Answer is fully correct."], "type": "string"}, "displayed": {"description": "Indicates whether an answer or item was displayed to the human agent in the agent desktop UI.", "type": "boolean"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ArticleSuggestionData": {"description": "Agent Assist Article Suggestion data.", "id": "GoogleCloudContactcenterinsightsV1alpha1ArticleSuggestionData", "properties": {"confidenceScore": {"description": "The system's confidence score that this article is a good match for this conversation, ranging from 0.0 (completely uncertain) to 1.0 (completely certain).", "format": "float", "type": "number"}, "metadata": {"additionalProperties": {"type": "string"}, "description": "Map that contains metadata about the Article Suggestion and the document that it originates from.", "type": "object"}, "queryRecord": {"description": "The name of the answer record. Format: projects/{project}/locations/{location}/answerRecords/{answer_record}", "type": "string"}, "source": {"description": "The knowledge document that this answer was extracted from. Format: projects/{project}/knowledgeBases/{knowledge_base}/documents/{document}", "type": "string"}, "title": {"description": "Article title.", "type": "string"}, "uri": {"description": "Article URI.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1BulkAnalyzeConversationsMetadata": {"description": "The metadata for a bulk analyze conversations operation.", "id": "GoogleCloudContactcenterinsightsV1alpha1BulkAnalyzeConversationsMetadata", "properties": {"completedAnalysesCount": {"description": "The number of requested analyses that have completed successfully so far.", "format": "int32", "type": "integer"}, "createTime": {"description": "The time the operation was created.", "format": "google-datetime", "type": "string"}, "endTime": {"description": "The time the operation finished running.", "format": "google-datetime", "type": "string"}, "failedAnalysesCount": {"description": "The number of requested analyses that have failed so far.", "format": "int32", "type": "integer"}, "partialErrors": {"description": "Output only. Partial errors during bulk analyze operation that might cause the operation output to be incomplete.", "items": {"$ref": "GoogleRpcStatus"}, "readOnly": true, "type": "array"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1BulkAnalyzeConversationsRequest", "description": "The original request for bulk analyze."}, "totalRequestedAnalysesCount": {"description": "Total number of analyses requested. Computed by the number of conversations returned by `filter` multiplied by `analysis_percentage` in the request.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1BulkAnalyzeConversationsRequest": {"description": "The request to analyze conversations in bulk.", "id": "GoogleCloudContactcenterinsightsV1alpha1BulkAnalyzeConversationsRequest", "properties": {"analysisPercentage": {"description": "Required. Percentage of selected conversation to analyze, between [0, 100].", "format": "float", "type": "number"}, "annotatorSelector": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1AnnotatorSelector", "description": "To select the annotators to run and the phrase matchers to use (if any). If not specified, all annotators will be run."}, "filter": {"description": "Required. Filter used to select the subset of conversations to analyze.", "type": "string"}, "parent": {"description": "Required. The parent resource to create analyses in.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1BulkAnalyzeConversationsResponse": {"description": "The response for a bulk analyze conversations operation.", "id": "GoogleCloudContactcenterinsightsV1alpha1BulkAnalyzeConversationsResponse", "properties": {"failedAnalysisCount": {"description": "Count of failed analyses.", "format": "int32", "type": "integer"}, "successfulAnalysisCount": {"description": "Count of successful analyses.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1BulkDeleteConversationsMetadata": {"description": "The metadata for a bulk delete conversations operation.", "id": "GoogleCloudContactcenterinsightsV1alpha1BulkDeleteConversationsMetadata", "properties": {"createTime": {"description": "The time the operation was created.", "format": "google-datetime", "type": "string"}, "endTime": {"description": "The time the operation finished running.", "format": "google-datetime", "type": "string"}, "partialErrors": {"description": "Partial errors during bulk delete conversations operation that might cause the operation output to be incomplete.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1BulkDeleteConversationsRequest", "description": "The original request for bulk delete."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1BulkDeleteConversationsRequest": {"description": "The request to delete conversations in bulk.", "id": "GoogleCloudContactcenterinsightsV1alpha1BulkDeleteConversationsRequest", "properties": {"filter": {"description": "Filter used to select the subset of conversations to delete.", "type": "string"}, "force": {"description": "If set to true, all of this conversation's analyses will also be deleted. Otherwise, the request will only succeed if the conversation has no analyses.", "type": "boolean"}, "maxDeleteCount": {"description": "Maximum number of conversations to delete.", "format": "int32", "type": "integer"}, "parent": {"description": "Required. The parent resource to delete conversations from. Format: projects/{project}/locations/{location}", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1BulkDeleteConversationsResponse": {"description": "The response for a bulk delete conversations operation.", "id": "GoogleCloudContactcenterinsightsV1alpha1BulkDeleteConversationsResponse", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1BulkDeleteFeedbackLabelsMetadata": {"description": "Metadata for the BulkDeleteFeedbackLabels endpoint.", "id": "GoogleCloudContactcenterinsightsV1alpha1BulkDeleteFeedbackLabelsMetadata", "properties": {"partialErrors": {"description": "Partial errors during deletion operation that might cause the operation output to be incomplete.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1BulkDeleteFeedbackLabelsRequest", "description": "Output only. The original request for delete.", "readOnly": true}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1BulkDeleteFeedbackLabelsRequest": {"description": "Request for the BulkDeleteFeedbackLabels endpoint.", "id": "GoogleCloudContactcenterinsightsV1alpha1BulkDeleteFeedbackLabelsRequest", "properties": {"filter": {"description": "Optional. A filter to reduce results to a specific subset. Supports disjunctions (OR) and conjunctions (AND). Supported fields: * `issue_model_id` * `qa_question_id` * `qa_scorecard_id` * `min_create_time` * `max_create_time` * `min_update_time` * `max_update_time` * `feedback_label_type`: QUALITY_AI, TOPIC_MODELING", "type": "string"}, "parent": {"description": "Required. The parent resource for new feedback labels.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1BulkDeleteFeedbackLabelsResponse": {"description": "Response for the BulkDeleteFeedbackLabels endpoint.", "id": "GoogleCloudContactcenterinsightsV1alpha1BulkDeleteFeedbackLabelsResponse", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1CallAnnotation": {"description": "A piece of metadata that applies to a window of a call.", "id": "GoogleCloudContactcenterinsightsV1alpha1CallAnnotation", "properties": {"annotationEndBoundary": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1AnnotationBoundary", "description": "The boundary in the conversation where the annotation ends, inclusive."}, "annotationStartBoundary": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1AnnotationBoundary", "description": "The boundary in the conversation where the annotation starts, inclusive."}, "channelTag": {"description": "The channel of the audio where the annotation occurs. For single-channel audio, this field is not populated.", "format": "int32", "type": "integer"}, "entityMentionData": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1EntityMentionData", "description": "Data specifying an entity mention."}, "holdData": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1HoldData", "description": "Data specifying a hold."}, "intentMatchData": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1IntentMatchData", "description": "Data specifying an intent match."}, "interruptionData": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1InterruptionData", "description": "Data specifying an interruption."}, "issueMatchData": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1IssueMatchData", "description": "Data specifying an issue match."}, "phraseMatchData": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1PhraseMatchData", "description": "Data specifying a phrase match."}, "sentimentData": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1SentimentData", "description": "Data specifying sentiment."}, "silenceData": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1SilenceData", "description": "Data specifying silence."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1Conversation": {"description": "The conversation resource.", "id": "GoogleCloudContactcenterinsightsV1alpha1Conversation", "properties": {"agentId": {"description": "An opaque, user-specified string representing the human agent who handled the conversation.", "type": "string"}, "callMetadata": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1ConversationCallMetadata", "description": "Call-specific metadata."}, "createTime": {"description": "Output only. The time at which the conversation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dataSource": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1ConversationDataSource", "description": "The source of the audio and transcription for the conversation."}, "dialogflowIntents": {"additionalProperties": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1DialogflowIntent"}, "description": "Output only. All the matched Dialogflow intents in the call. The key corresponds to a Dialogflow intent, format: projects/{project}/agent/{agent}/intents/{intent}", "readOnly": true, "type": "object"}, "duration": {"description": "Output only. The duration of the conversation.", "format": "google-duration", "readOnly": true, "type": "string"}, "expireTime": {"description": "The time at which this conversation should expire. After this time, the conversation data and any associated analyses will be deleted.", "format": "google-datetime", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "A map for the user to specify any custom fields. A maximum of 100 labels per conversation is allowed, with a maximum of 256 characters per entry.", "type": "object"}, "languageCode": {"description": "A user-specified language code for the conversation.", "type": "string"}, "latestAnalysis": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1Analysis", "description": "Output only. The conversation's latest analysis, if one exists.", "readOnly": true}, "latestSummary": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1ConversationSummarizationSuggestionData", "description": "Output only. Latest summary of the conversation.", "readOnly": true}, "medium": {"description": "Immutable. The conversation medium, if unspecified will default to PHONE_CALL.", "enum": ["MEDIUM_UNSPECIFIED", "PHONE_CALL", "CHAT"], "enumDescriptions": ["Default value, if unspecified will default to PHONE_CALL.", "The format for conversations that took place over the phone.", "The format for conversations that took place over chat."], "type": "string"}, "metadataJson": {"description": "Input only. JSON metadata encoded as a string. This field is primarily used by Insights integrations with various telephony systems and must be in one of Insight's supported formats.", "type": "string"}, "name": {"description": "Immutable. The resource name of the conversation. Format: projects/{project}/locations/{location}/conversations/{conversation}", "type": "string"}, "obfuscatedUserId": {"description": "Obfuscated user ID which the customer sent to us.", "type": "string"}, "qualityMetadata": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1ConversationQualityMetadata", "description": "Conversation metadata related to quality management."}, "runtimeAnnotations": {"description": "Output only. The annotations that were generated during the customer and agent interaction.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1RuntimeAnnotation"}, "readOnly": true, "type": "array"}, "startTime": {"description": "The time at which the conversation started.", "format": "google-datetime", "type": "string"}, "transcript": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1ConversationTranscript", "description": "Output only. The conversation transcript.", "readOnly": true}, "ttl": {"description": "Input only. The TTL for this resource. If specified, then this TTL will be used to calculate the expire time.", "format": "google-duration", "type": "string"}, "turnCount": {"description": "Output only. The number of turns in the conversation.", "format": "int32", "readOnly": true, "type": "integer"}, "updateTime": {"description": "Output only. The most recent time at which the conversation was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ConversationCallMetadata": {"description": "Call-specific metadata.", "id": "GoogleCloudContactcenterinsightsV1alpha1ConversationCallMetadata", "properties": {"agentChannel": {"description": "The audio channel that contains the agent.", "format": "int32", "type": "integer"}, "customerChannel": {"description": "The audio channel that contains the customer.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ConversationDataSource": {"description": "The conversation source, which is a combination of transcript and audio.", "id": "GoogleCloudContactcenterinsightsV1alpha1ConversationDataSource", "properties": {"dialogflowSource": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1DialogflowSource", "description": "The source when the conversation comes from Dialogflow."}, "gcsSource": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1GcsSource", "description": "A Cloud Storage location specification for the audio and transcript."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ConversationLevelSentiment": {"description": "One channel of conversation-level sentiment data.", "id": "GoogleCloudContactcenterinsightsV1alpha1ConversationLevelSentiment", "properties": {"channelTag": {"description": "The channel of the audio that the data applies to.", "format": "int32", "type": "integer"}, "sentimentData": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1SentimentData", "description": "Data specifying sentiment."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ConversationLevelSilence": {"description": "Conversation-level silence data.", "id": "GoogleCloudContactcenterinsightsV1alpha1ConversationLevelSilence", "properties": {"silenceDuration": {"description": "Amount of time calculated to be in silence.", "format": "google-duration", "type": "string"}, "silencePercentage": {"description": "Percentage of the total conversation spent in silence.", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ConversationParticipant": {"description": "The call participant speaking for a given utterance.", "id": "GoogleCloudContactcenterinsightsV1alpha1ConversationParticipant", "properties": {"dialogflowParticipant": {"deprecated": true, "description": "Deprecated. Use `dialogflow_participant_name` instead. The name of the Dialogflow participant. Format: projects/{project}/locations/{location}/conversations/{conversation}/participants/{participant}", "type": "string"}, "dialogflowParticipantName": {"description": "The name of the participant provided by Dialogflow. Format: projects/{project}/locations/{location}/conversations/{conversation}/participants/{participant}", "type": "string"}, "obfuscatedExternalUserId": {"description": "Obfuscated user ID from Dialogflow.", "type": "string"}, "role": {"description": "The role of the participant.", "enum": ["ROLE_UNSPECIFIED", "HUMAN_AGENT", "AUTOMATED_AGENT", "END_USER", "ANY_AGENT"], "enumDescriptions": ["<PERSON><PERSON><PERSON><PERSON>'s role is not set.", "Participant is a human agent.", "Participant is an automated agent.", "<PERSON><PERSON><PERSON><PERSON> is an end user who conversed with the contact center.", "Participant is either a human or automated agent."], "type": "string"}, "userId": {"description": "A user-specified ID representing the participant.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ConversationQualityMetadata": {"description": "Conversation metadata related to quality management.", "id": "GoogleCloudContactcenterinsightsV1alpha1ConversationQualityMetadata", "properties": {"agentInfo": {"description": "Information about agents involved in the call.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1ConversationQualityMetadataAgentInfo"}, "type": "array"}, "customerSatisfactionRating": {"description": "An arbitrary integer value indicating the customer's satisfaction rating.", "format": "int32", "type": "integer"}, "menuPath": {"description": "An arbitrary string value specifying the menu path the customer took.", "type": "string"}, "waitDuration": {"description": "The amount of time the customer waited to connect with an agent.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ConversationQualityMetadataAgentInfo": {"description": "Information about an agent involved in the conversation.", "id": "GoogleCloudContactcenterinsightsV1alpha1ConversationQualityMetadataAgentInfo", "properties": {"agentId": {"description": "A user-specified string representing the agent.", "type": "string"}, "agentType": {"description": "The agent type, e.g. HUMAN_AGENT.", "enum": ["ROLE_UNSPECIFIED", "HUMAN_AGENT", "AUTOMATED_AGENT", "END_USER", "ANY_AGENT"], "enumDescriptions": ["<PERSON><PERSON><PERSON><PERSON>'s role is not set.", "Participant is a human agent.", "Participant is an automated agent.", "<PERSON><PERSON><PERSON><PERSON> is an end user who conversed with the contact center.", "Participant is either a human or automated agent."], "type": "string"}, "displayName": {"description": "The agent's name.", "type": "string"}, "dispositionCode": {"description": "A user-provided string indicating the outcome of the agent's segment of the call.", "type": "string"}, "location": {"description": "The agent's location.", "type": "string"}, "team": {"deprecated": true, "description": "A user-specified string representing the agent's team. Deprecated in favor of the `teams` field.", "type": "string"}, "teams": {"description": "User-specified strings representing the agent's teams.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ConversationSummarizationSuggestionData": {"description": "Conversation summarization suggestion data.", "id": "GoogleCloudContactcenterinsightsV1alpha1ConversationSummarizationSuggestionData", "properties": {"answerRecord": {"description": "The name of the answer record. Format: projects/{project}/locations/{location}/answerRecords/{answer_record}", "type": "string"}, "confidence": {"description": "The confidence score of the summarization.", "format": "float", "type": "number"}, "conversationModel": {"description": "The name of the model that generates this summary. Format: projects/{project}/locations/{location}/conversationModels/{conversation_model}", "type": "string"}, "metadata": {"additionalProperties": {"type": "string"}, "description": "A map that contains metadata about the summarization and the document from which it originates.", "type": "object"}, "text": {"description": "The summarization content that is concatenated into one string.", "type": "string"}, "textSections": {"additionalProperties": {"type": "string"}, "description": "The summarization content that is divided into sections. The key is the section's name and the value is the section's content. There is no specific format for the key or value.", "type": "object"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ConversationTranscript": {"description": "A message representing the transcript of a conversation.", "id": "GoogleCloudContactcenterinsightsV1alpha1ConversationTranscript", "properties": {"transcriptSegments": {"description": "A list of sequential transcript segments that comprise the conversation.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1ConversationTranscriptTranscriptSegment"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ConversationTranscriptTranscriptSegment": {"description": "A segment of a full transcript.", "id": "GoogleCloudContactcenterinsightsV1alpha1ConversationTranscriptTranscriptSegment", "properties": {"channelTag": {"description": "For conversations derived from multi-channel audio, this is the channel number corresponding to the audio from that channel. For audioChannelCount = N, its output values can range from '1' to 'N'. A channel tag of 0 indicates that the audio is mono.", "format": "int32", "type": "integer"}, "confidence": {"description": "A confidence estimate between 0.0 and 1.0 of the fidelity of this segment. A default value of 0.0 indicates that the value is unset.", "format": "float", "type": "number"}, "dialogflowSegmentMetadata": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1ConversationTranscriptTranscriptSegmentDialogflowSegmentMetadata", "description": "CCAI metadata relating to the current transcript segment."}, "languageCode": {"description": "The language code of this segment as a [BCP-47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt) language tag. Example: \"en-US\".", "type": "string"}, "messageTime": {"description": "The time that the message occurred, if provided.", "format": "google-datetime", "type": "string"}, "segmentParticipant": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1ConversationParticipant", "description": "The participant of this segment."}, "sentiment": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1SentimentData", "description": "The sentiment for this transcript segment."}, "text": {"description": "The text of this segment.", "type": "string"}, "words": {"description": "A list of the word-specific information for each word in the segment.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1ConversationTranscriptTranscriptSegmentWordInfo"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ConversationTranscriptTranscriptSegmentDialogflowSegmentMetadata": {"description": "Metadata from Dialogflow relating to the current transcript segment.", "id": "GoogleCloudContactcenterinsightsV1alpha1ConversationTranscriptTranscriptSegmentDialogflowSegmentMetadata", "properties": {"smartReplyAllowlistCovered": {"description": "Whether the transcript segment was covered under the configured smart reply allowlist in Agent Assist.", "type": "boolean"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ConversationTranscriptTranscriptSegmentWordInfo": {"description": "Word-level info for words in a transcript.", "id": "GoogleCloudContactcenterinsightsV1alpha1ConversationTranscriptTranscriptSegmentWordInfo", "properties": {"confidence": {"description": "A confidence estimate between 0.0 and 1.0 of the fidelity of this word. A default value of 0.0 indicates that the value is unset.", "format": "float", "type": "number"}, "endOffset": {"description": "Time offset of the end of this word relative to the beginning of the total conversation.", "format": "google-duration", "type": "string"}, "startOffset": {"description": "Time offset of the start of this word relative to the beginning of the total conversation.", "format": "google-duration", "type": "string"}, "word": {"description": "The word itself. Includes punctuation marks that surround the word.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1CreateAnalysisOperationMetadata": {"description": "Metadata for a create analysis operation.", "id": "GoogleCloudContactcenterinsightsV1alpha1CreateAnalysisOperationMetadata", "properties": {"annotatorSelector": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1AnnotatorSelector", "description": "Output only. The annotator selector used for the analysis (if any).", "readOnly": true}, "conversation": {"description": "Output only. The Conversation that this Analysis Operation belongs to.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1CreateIssueMetadata": {"description": "Met<PERSON><PERSON> for creating an issue.", "id": "GoogleCloudContactcenterinsightsV1alpha1CreateIssueMetadata", "properties": {"createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1CreateIssueRequest", "description": "The original request for creation."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1CreateIssueModelMetadata": {"description": "<PERSON><PERSON><PERSON> for creating an issue model.", "id": "GoogleCloudContactcenterinsightsV1alpha1CreateIssueModelMetadata", "properties": {"createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1CreateIssueModelRequest", "description": "The original request for creation."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1CreateIssueModelRequest": {"description": "The request to create an issue model.", "id": "GoogleCloudContactcenterinsightsV1alpha1CreateIssueModelRequest", "properties": {"issueModel": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1IssueModel", "description": "Required. The issue model to create."}, "parent": {"description": "Required. The parent resource of the issue model.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1CreateIssueRequest": {"description": "The request to create an issue.", "id": "GoogleCloudContactcenterinsightsV1alpha1CreateIssueRequest", "properties": {"issue": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1Issue", "description": "Required. The values for the new issue."}, "parent": {"description": "Required. The parent resource of the issue.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1Dataset": {"description": "Dataset resource represents a collection of conversations that may be bounded (Static Dataset, e.g. golden dataset for training), or unbounded (Dynamic Dataset, e.g. live traffic, or agent training traffic)", "id": "GoogleCloudContactcenterinsightsV1alpha1Dataset", "properties": {"createTime": {"description": "Output only. Dataset create time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Dataset description.", "type": "string"}, "displayName": {"description": "Display name for the dataaset", "type": "string"}, "name": {"description": "Immutable. Identifier. Resource name of the dataset. Format: projects/{project}/locations/{location}/datasets/{dataset}", "type": "string"}, "ttl": {"description": "Optional. Option TTL for the dataset.", "format": "google-duration", "type": "string"}, "type": {"description": "Dataset usage type.", "enum": ["TYPE_UNSPECIFIED", "EVAL", "LIVE"], "enumDescriptions": ["Default value for unspecified.", "For evals only.", "Dataset with new conversations coming in regularly (Insights legacy conversations and AI trainer)"], "type": "string"}, "updateTime": {"description": "Output only. Dataset update time.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1DeleteIssueModelMetadata": {"description": "Metadata for deleting an issue model.", "id": "GoogleCloudContactcenterinsightsV1alpha1DeleteIssueModelMetadata", "properties": {"createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1DeleteIssueModelRequest", "description": "The original request for deletion."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1DeleteIssueModelRequest": {"description": "The request to delete an issue model.", "id": "GoogleCloudContactcenterinsightsV1alpha1DeleteIssueModelRequest", "properties": {"name": {"description": "Required. The name of the issue model to delete.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1DeployIssueModelMetadata": {"description": "Metadata for deploying an issue model.", "id": "GoogleCloudContactcenterinsightsV1alpha1DeployIssueModelMetadata", "properties": {"createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1DeployIssueModelRequest", "description": "The original request for deployment."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1DeployIssueModelRequest": {"description": "The request to deploy an issue model.", "id": "GoogleCloudContactcenterinsightsV1alpha1DeployIssueModelRequest", "properties": {"name": {"description": "Required. The issue model to deploy.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1DeployIssueModelResponse": {"description": "The response to deploy an issue model.", "id": "GoogleCloudContactcenterinsightsV1alpha1DeployIssueModelResponse", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1DialogflowIntent": {"description": "The data for a Dialogflow intent. Represents a detected intent in the conversation, e.g. MAKES_PROMISE.", "id": "GoogleCloudContactcenterinsightsV1alpha1DialogflowIntent", "properties": {"displayName": {"description": "The human-readable name of the intent.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1DialogflowInteractionData": {"description": "Dialogflow interaction data.", "id": "GoogleCloudContactcenterinsightsV1alpha1DialogflowInteractionData", "properties": {"confidence": {"description": "The confidence of the match ranging from 0.0 (completely uncertain) to 1.0 (completely certain).", "format": "float", "type": "number"}, "dialogflowIntentId": {"description": "The Dialogflow intent resource path. Format: projects/{project}/agent/{agent}/intents/{intent}", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1DialogflowSource": {"description": "A Dialogflow source of conversation data.", "id": "GoogleCloudContactcenterinsightsV1alpha1DialogflowSource", "properties": {"audioUri": {"description": "Cloud Storage URI that points to a file that contains the conversation audio.", "type": "string"}, "dialogflowConversation": {"description": "Output only. The name of the Dialogflow conversation that this conversation resource is derived from. Format: projects/{project}/locations/{location}/conversations/{conversation}", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1Dimension": {"description": "A dimension determines the grouping key for the query. In SQL terms, these would be part of both the \"SELECT\" and \"GROUP BY\" clauses.", "id": "GoogleCloudContactcenterinsightsV1alpha1Dimension", "properties": {"agentDimensionMetadata": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1DimensionAgentDimensionMetadata", "description": "Output only. <PERSON><PERSON><PERSON> about the agent dimension.", "readOnly": true}, "dimensionKey": {"description": "The key of the dimension.", "enum": ["DIMENSION_KEY_UNSPECIFIED", "ISSUE", "ISSUE_NAME", "AGENT", "AGENT_TEAM", "QA_QUESTION_ID", "QA_QUESTION_ANSWER_VALUE", "CONVERSATION_PROFILE_ID", "MEDIUM"], "enumDescriptions": ["The key of the dimension is unspecified.", "The dimension is keyed by issues.", "The dimension is keyed by issue names.", "The dimension is keyed by agents.", "The dimension is keyed by agent teams.", "The dimension is keyed by QaQuestionIds. Note that: We only group by the QuestionId and not the revision-id of the scorecard this question is a part of. This allows for showing stats for the same question across different scorecard revisions.", "The dimension is keyed by QaQuestionIds-Answer value pairs. Note that: We only group by the QuestionId and not the revision-id of the scorecard this question is a part of. This allows for showing distribution of answers per question across different scorecard revisions.", "The dimension is keyed by the conversation profile ID.", "The dimension is keyed by the conversation medium."], "type": "string"}, "issueDimensionMetadata": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1DimensionIssueDimensionMetadata", "description": "Output only. <PERSON><PERSON><PERSON> about the issue dimension.", "readOnly": true}, "qaQuestionAnswerDimensionMetadata": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1DimensionQaQuestionAnswerDimensionMetadata", "description": "Output only. <PERSON><PERSON><PERSON> about the QA question-answer dimension.", "readOnly": true}, "qaQuestionDimensionMetadata": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1DimensionQaQuestionDimensionMetadata", "description": "Output only. <PERSON><PERSON><PERSON> about the QA question dimension.", "readOnly": true}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1DimensionAgentDimensionMetadata": {"description": "<PERSON><PERSON><PERSON> about the agent dimension.", "id": "GoogleCloudContactcenterinsightsV1alpha1DimensionAgentDimensionMetadata", "properties": {"agentDisplayName": {"description": "Optional. The agent's name", "type": "string"}, "agentId": {"description": "Optional. A user-specified string representing the agent.", "type": "string"}, "agentTeam": {"description": "Optional. A user-specified string representing the agent's team.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1DimensionIssueDimensionMetadata": {"description": "<PERSON><PERSON><PERSON> about the issue dimension.", "id": "GoogleCloudContactcenterinsightsV1alpha1DimensionIssueDimensionMetadata", "properties": {"issueDisplayName": {"description": "The issue display name.", "type": "string"}, "issueId": {"description": "The issue ID.", "type": "string"}, "issueModelId": {"description": "The parent issue model ID.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1DimensionQaQuestionAnswerDimensionMetadata": {"description": "Metadata about the QA question-answer dimension. This is useful for showing the answer distribution for questions for a given scorecard.", "id": "GoogleCloudContactcenterinsightsV1alpha1DimensionQaQuestionAnswerDimensionMetadata", "properties": {"answerValue": {"description": "Optional. The full body of the question.", "type": "string"}, "qaQuestionId": {"description": "Optional. The QA question ID.", "type": "string"}, "qaScorecardId": {"description": "Optional. The QA scorecard ID.", "type": "string"}, "questionBody": {"description": "Optional. The full body of the question.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1DimensionQaQuestionDimensionMetadata": {"description": "<PERSON><PERSON><PERSON> about the QA question dimension.", "id": "GoogleCloudContactcenterinsightsV1alpha1DimensionQaQuestionDimensionMetadata", "properties": {"qaQuestionId": {"description": "Optional. The QA question ID.", "type": "string"}, "qaScorecardId": {"description": "Optional. The QA scorecard ID.", "type": "string"}, "questionBody": {"description": "Optional. The full body of the question.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1EncryptionSpec": {"description": "A customer-managed encryption key specification that can be applied to all created resources (e.g. `Conversation`).", "id": "GoogleCloudContactcenterinsightsV1alpha1EncryptionSpec", "properties": {"kmsKey": {"description": "Required. The name of customer-managed encryption key that is used to secure a resource and its sub-resources. If empty, the resource is secured by our default encryption key. Only the key in the same location as this resource is allowed to be used for encryption. Format: `projects/{project}/locations/{location}/keyRings/{keyRing}/cryptoKeys/{key}`", "type": "string"}, "name": {"description": "Immutable. The resource name of the encryption key specification resource. Format: projects/{project}/locations/{location}/encryptionSpec", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1Entity": {"description": "The data for an entity annotation. Represents a phrase in the conversation that is a known entity, such as a person, an organization, or location.", "id": "GoogleCloudContactcenterinsightsV1alpha1Entity", "properties": {"displayName": {"description": "The representative name for the entity.", "type": "string"}, "metadata": {"additionalProperties": {"type": "string"}, "description": "Metadata associated with the entity. For most entity types, the metadata is a Wikipedia URL (`wikipedia_url`) and Knowledge Graph MID (`mid`), if they are available. For the metadata associated with other entity types, see the Type table below.", "type": "object"}, "salience": {"description": "The salience score associated with the entity in the [0, 1.0] range. The salience score for an entity provides information about the importance or centrality of that entity to the entire document text. Scores closer to 0 are less salient, while scores closer to 1.0 are highly salient.", "format": "float", "type": "number"}, "sentiment": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1SentimentData", "description": "The aggregate sentiment expressed for this entity in the conversation."}, "type": {"description": "The entity type.", "enum": ["TYPE_UNSPECIFIED", "PERSON", "LOCATION", "ORGANIZATION", "EVENT", "WORK_OF_ART", "CONSUMER_GOOD", "OTHER", "PHONE_NUMBER", "ADDRESS", "DATE", "NUMBER", "PRICE"], "enumDescriptions": ["Unspecified.", "Person.", "Location.", "Organization.", "Event.", "Artwork.", "Consumer product.", "Other types of entities.", "Phone number. The metadata lists the phone number (formatted according to local convention), plus whichever additional elements appear in the text: * `number` - The actual number, broken down into sections according to local convention. * `national_prefix` - Country code, if detected. * `area_code` - Region or area code, if detected. * `extension` - Phone extension (to be dialed after connection), if detected.", "Address. The metadata identifies the street number and locality plus whichever additional elements appear in the text: * `street_number` - Street number. * `locality` - City or town. * `street_name` - Street/route name, if detected. * `postal_code` - Postal code, if detected. * `country` - Country, if detected. * `broad_region` - Administrative area, such as the state, if detected. * `narrow_region` - Smaller administrative area, such as county, if detected. * `sublocality` - Used in Asian addresses to demark a district within a city, if detected.", "Date. The metadata identifies the components of the date: * `year` - Four digit year, if detected. * `month` - Two digit month number, if detected. * `day` - Two digit day number, if detected.", "Number. The metadata is the number itself.", "Price. The metadata identifies the `value` and `currency`."], "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1EntityMentionData": {"description": "The data for an entity mention annotation. This represents a mention of an `Entity` in the conversation.", "id": "GoogleCloudContactcenterinsightsV1alpha1EntityMentionData", "properties": {"entityUniqueId": {"description": "The key of this entity in conversation entities. Can be used to retrieve the exact `Entity` this mention is attached to.", "type": "string"}, "sentiment": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1SentimentData", "description": "Sentiment expressed for this mention of the entity."}, "type": {"description": "The type of the entity mention.", "enum": ["MENTION_TYPE_UNSPECIFIED", "PROPER", "COMMON"], "enumDescriptions": ["Unspecified.", "Proper noun.", "Common noun (or noun compound)."], "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ExportInsightsDataMetadata": {"description": "Metadata for an export insights operation.", "id": "GoogleCloudContactcenterinsightsV1alpha1ExportInsightsDataMetadata", "properties": {"completedExportCount": {"description": "The number of conversations that were exported successfully.", "format": "int32", "type": "integer"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "failedExportCount": {"description": "The number of conversations that failed to be exported.", "format": "int32", "type": "integer"}, "partialErrors": {"description": "Partial errors during export operation that might cause the operation output to be incomplete.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1ExportInsightsDataRequest", "description": "The original request for export."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ExportInsightsDataRequest": {"description": "The request to export insights.", "id": "GoogleCloudContactcenterinsightsV1alpha1ExportInsightsDataRequest", "properties": {"bigQueryDestination": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1ExportInsightsDataRequestBigQueryDestination", "description": "Specified if sink is a BigQuery table."}, "exportSchemaVersion": {"description": "Optional. Version of the export schema.", "enum": ["EXPORT_SCHEMA_VERSION_UNSPECIFIED", "EXPORT_V1", "EXPORT_V2", "EXPORT_V3", "EXPORT_V4", "EXPORT_V5", "EXPORT_V6", "EXPORT_V7", "EXPORT_VERSION_LATEST_AVAILABLE"], "enumDescriptions": ["Unspecified. Defaults to EXPORT_V3.", "Export schema version 1.", "Export schema version 2.", "Export schema version 3.", "Export schema version 4.", "Export schema version 5.", "Export schema version 6.", "Export schema version 7.", "Export schema version latest available."], "type": "string"}, "filter": {"description": "A filter to reduce results to a specific subset. Useful for exporting conversations with specific properties.", "type": "string"}, "kmsKey": {"description": "A fully qualified KMS key name for BigQuery tables protected by CMEK. Format: projects/{project}/locations/{location}/keyRings/{keyring}/cryptoKeys/{key}/cryptoKeyVersions/{version}", "type": "string"}, "parent": {"description": "Required. The parent resource to export data from.", "type": "string"}, "writeDisposition": {"description": "Options for what to do if the destination table already exists.", "enum": ["WRITE_DISPOSITION_UNSPECIFIED", "WRITE_TRUNCATE", "WRITE_APPEND"], "enumDescriptions": ["Write disposition is not specified. Defaults to WRITE_TRUNCATE.", "If the table already exists, BigQuery will overwrite the table data and use the schema from the load.", "If the table already exists, BigQuery will append data to the table."], "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ExportInsightsDataRequestBigQueryDestination": {"description": "A BigQuery Table Reference.", "id": "GoogleCloudContactcenterinsightsV1alpha1ExportInsightsDataRequestBigQueryDestination", "properties": {"dataset": {"description": "Required. The name of the BigQuery dataset that the snapshot result should be exported to. If this dataset does not exist, the export call returns an INVALID_ARGUMENT error.", "type": "string"}, "projectId": {"description": "A project ID or number. If specified, then export will attempt to write data to this project instead of the resource project. Otherwise, the resource project will be used.", "type": "string"}, "table": {"description": "The BigQuery table name to which the insights data should be written. If this table does not exist, the export call returns an INVALID_ARGUMENT error.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ExportInsightsDataResponse": {"description": "Response for an export insights operation.", "id": "GoogleCloudContactcenterinsightsV1alpha1ExportInsightsDataResponse", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ExportIssueModelMetadata": {"description": "Metadata used for export issue model.", "id": "GoogleCloudContactcenterinsightsV1alpha1ExportIssueModelMetadata", "properties": {"createTime": {"description": "The time the operation was created.", "format": "google-datetime", "type": "string"}, "endTime": {"description": "The time the operation finished running.", "format": "google-datetime", "type": "string"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1ExportIssueModelRequest", "description": "The original export request."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ExportIssueModelRequest": {"description": "Request to export an issue model.", "id": "GoogleCloudContactcenterinsightsV1alpha1ExportIssueModelRequest", "properties": {"gcsDestination": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1ExportIssueModelRequestGcsDestination", "description": "Google Cloud Storage URI to export the issue model to."}, "name": {"description": "Required. The issue model to export.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ExportIssueModelRequestGcsDestination": {"description": "Google Cloud Storage Object URI to save the issue model to.", "id": "GoogleCloudContactcenterinsightsV1alpha1ExportIssueModelRequestGcsDestination", "properties": {"objectUri": {"description": "Required. Format: `gs:///`", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ExportIssueModelResponse": {"description": "Response from export issue model", "id": "GoogleCloudContactcenterinsightsV1alpha1ExportIssueModelResponse", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1FaqAnswerData": {"description": "Agent Assist frequently-asked-question answer data.", "id": "GoogleCloudContactcenterinsightsV1alpha1FaqAnswerData", "properties": {"answer": {"description": "The piece of text from the `source` knowledge base document.", "type": "string"}, "confidenceScore": {"description": "The system's confidence score that this answer is a good match for this conversation, ranging from 0.0 (completely uncertain) to 1.0 (completely certain).", "format": "float", "type": "number"}, "metadata": {"additionalProperties": {"type": "string"}, "description": "Map that contains metadata about the FAQ answer and the document that it originates from.", "type": "object"}, "queryRecord": {"description": "The name of the answer record. Format: projects/{project}/locations/{location}/answerRecords/{answer_record}", "type": "string"}, "question": {"description": "The corresponding FAQ question.", "type": "string"}, "source": {"description": "The knowledge document that this answer was extracted from. Format: projects/{project}/knowledgeBases/{knowledge_base}/documents/{document}.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1FeedbackLabel": {"description": "Represents a conversation, resource, and label provided by the user. Can take the form of a string label or a QaAnswer label. QaAnswer labels are used for Quality AI example conversations. String labels are used for Topic Modeling. AgentAssistSummary labels are used for Agent Assist Summarization.", "id": "GoogleCloudContactcenterinsightsV1alpha1FeedbackLabel", "properties": {"createTime": {"description": "Output only. Create time of the label.", "format": "google-datetime", "readOnly": true, "type": "string"}, "label": {"description": "String label used for Topic Modeling.", "type": "string"}, "labeledResource": {"description": "Name of the resource to be labeled. Supported resources are: * `projects/{project}/locations/{location}/qaScorecards/{scorecard}/revisions/{revision}/qaQuestions/{question}` * `projects/{project}/locations/{location}/issueModels/{issue_model}` * `projects/{project}/locations/{location}/generators/{generator_id}`", "type": "string"}, "name": {"description": "Immutable. Resource name of the FeedbackLabel. Format: projects/{project}/locations/{location}/conversations/{conversation}/feedbackLabels/{feedback_label}", "type": "string"}, "qaAnswerLabel": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1QaAnswerAnswerValue", "description": "QaAnswer label used for Quality AI example conversations."}, "updateTime": {"description": "Output only. Update time of the label.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1GcsSource": {"description": "A Cloud Storage source of conversation data.", "id": "GoogleCloudContactcenterinsightsV1alpha1GcsSource", "properties": {"audioUri": {"description": "Cloud Storage URI that points to a file that contains the conversation audio.", "type": "string"}, "transcriptUri": {"description": "Immutable. Cloud Storage URI that points to a file that contains the conversation transcript.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1HoldData": {"description": "The data for a hold annotation.", "id": "GoogleCloudContactcenterinsightsV1alpha1HoldData", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ImportIssueModelMetadata": {"description": "Metadata used for import issue model.", "id": "GoogleCloudContactcenterinsightsV1alpha1ImportIssueModelMetadata", "properties": {"createTime": {"description": "The time the operation was created.", "format": "google-datetime", "type": "string"}, "endTime": {"description": "The time the operation finished running.", "format": "google-datetime", "type": "string"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1ImportIssueModelRequest", "description": "The original import request."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ImportIssueModelRequest": {"description": "Request to import an issue model.", "id": "GoogleCloudContactcenterinsightsV1alpha1ImportIssueModelRequest", "properties": {"createNewModel": {"description": "Optional. If set to true, will create an issue model from the imported file with randomly generated IDs for the issue model and corresponding issues. Otherwise, replaces an existing model with the same ID as the file.", "type": "boolean"}, "gcsSource": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1ImportIssueModelRequestGcsSource", "description": "Google Cloud Storage source message."}, "parent": {"description": "Required. The parent resource of the issue model.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ImportIssueModelRequestGcsSource": {"description": "Google Cloud Storage Object URI to get the issue model file from.", "id": "GoogleCloudContactcenterinsightsV1alpha1ImportIssueModelRequestGcsSource", "properties": {"objectUri": {"description": "Required. Format: `gs:///`", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ImportIssueModelResponse": {"description": "Response from import issue model", "id": "GoogleCloudContactcenterinsightsV1alpha1ImportIssueModelResponse", "properties": {"issueModel": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1IssueModel", "description": "The issue model that was imported."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1IngestConversationsMetadata": {"description": "The metadata for an IngestConversations operation.", "id": "GoogleCloudContactcenterinsightsV1alpha1IngestConversationsMetadata", "properties": {"createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "ingestConversationsStats": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1IngestConversationsMetadataIngestConversationsStats", "description": "Output only. Statistics for IngestConversations operation.", "readOnly": true}, "partialErrors": {"description": "Output only. Partial errors during ingest operation that might cause the operation output to be incomplete.", "items": {"$ref": "GoogleRpcStatus"}, "readOnly": true, "type": "array"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1IngestConversationsRequest", "description": "Output only. The original request for ingest.", "readOnly": true}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1IngestConversationsMetadataIngestConversationsStats": {"description": "Statistics for IngestConversations operation.", "id": "GoogleCloudContactcenterinsightsV1alpha1IngestConversationsMetadataIngestConversationsStats", "properties": {"duplicatesSkippedCount": {"description": "Output only. The number of objects skipped because another conversation with the same transcript uri had already been ingested.", "format": "int32", "readOnly": true, "type": "integer"}, "failedIngestCount": {"description": "Output only. The number of objects which were unable to be ingested due to errors. The errors are populated in the partial_errors field.", "format": "int32", "readOnly": true, "type": "integer"}, "processedObjectCount": {"description": "Output only. The number of objects processed during the ingest operation.", "format": "int32", "readOnly": true, "type": "integer"}, "successfulIngestCount": {"description": "Output only. The number of new conversations added during this ingest operation.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1IngestConversationsRequest": {"description": "The request to ingest conversations.", "id": "GoogleCloudContactcenterinsightsV1alpha1IngestConversationsRequest", "properties": {"conversationConfig": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1IngestConversationsRequestConversationConfig", "description": "Configuration that applies to all conversations."}, "gcsSource": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1IngestConversationsRequestGcsSource", "description": "A cloud storage bucket source. Note that any previously ingested objects from the source will be skipped to avoid duplication."}, "parent": {"description": "Required. The parent resource for new conversations.", "type": "string"}, "redactionConfig": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1RedactionConfig", "description": "Optional. DLP settings for transcript redaction. Optional, will default to the config specified in Settings."}, "sampleSize": {"description": "Optional. If set, this fields indicates the number of objects to ingest from the Cloud Storage bucket. If empty, the entire bucket will be ingested. Unless they are first deleted, conversations produced through sampling won't be ingested by subsequent ingest requests.", "format": "int32", "type": "integer"}, "speechConfig": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1SpeechConfig", "description": "Optional. Default Speech-to-Text configuration. Optional, will default to the config specified in Settings."}, "transcriptObjectConfig": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1IngestConversationsRequestTranscriptObjectConfig", "description": "Configuration for when `source` contains conversation transcripts."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1IngestConversationsRequestConversationConfig": {"description": "Configuration that applies to all conversations.", "id": "GoogleCloudContactcenterinsightsV1alpha1IngestConversationsRequestConversationConfig", "properties": {"agentChannel": {"description": "Optional. Indicates which of the channels, 1 or 2, contains the agent. Note that this must be set for conversations to be properly displayed and analyzed.", "format": "int32", "type": "integer"}, "agentId": {"description": "Optional. An opaque, user-specified string representing a human agent who handled all conversations in the import. Note that this will be overridden if per-conversation metadata is provided through the `metadata_bucket_uri`.", "type": "string"}, "customerChannel": {"description": "Optional. Indicates which of the channels, 1 or 2, contains the agent. Note that this must be set for conversations to be properly displayed and analyzed.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1IngestConversationsRequestGcsSource": {"description": "Configuration for Cloud Storage bucket sources.", "id": "GoogleCloudContactcenterinsightsV1alpha1IngestConversationsRequestGcsSource", "properties": {"bucketObjectType": {"description": "Optional. Specifies the type of the objects in `bucket_uri`.", "enum": ["BUCKET_OBJECT_TYPE_UNSPECIFIED", "TRANSCRIPT", "AUDIO"], "enumDescriptions": ["The object type is unspecified and will default to `TRANSCRIPT`.", "The object is a transcript.", "The object is an audio file."], "type": "string"}, "bucketUri": {"description": "Required. The Cloud Storage bucket containing source objects.", "type": "string"}, "customMetadataKeys": {"description": "Optional. Custom keys to extract as conversation labels from metadata files in `metadata_bucket_uri`. Keys not included in this field will be ignored. Note that there is a limit of 100 labels per conversation.", "items": {"type": "string"}, "type": "array"}, "metadataBucketUri": {"description": "Optional. The Cloud Storage path to the conversation metadata. Note that: [1] Metadata files are expected to be in JSON format. [2] Metadata and source files (transcripts or audio) must be in separate buckets. [3] A source file and its corresponding metadata file must share the same name to be properly ingested, E.g. `gs://bucket/audio/conversation1.mp3` and `gs://bucket/metadata/conversation1.json`.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1IngestConversationsRequestTranscriptObjectConfig": {"description": "Configuration for processing transcript objects.", "id": "GoogleCloudContactcenterinsightsV1alpha1IngestConversationsRequestTranscriptObjectConfig", "properties": {"medium": {"description": "Required. The medium transcript objects represent.", "enum": ["MEDIUM_UNSPECIFIED", "PHONE_CALL", "CHAT"], "enumDescriptions": ["Default value, if unspecified will default to PHONE_CALL.", "The format for conversations that took place over the phone.", "The format for conversations that took place over chat."], "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1IngestConversationsResponse": {"description": "The response to an IngestConversations operation.", "id": "GoogleCloudContactcenterinsightsV1alpha1IngestConversationsResponse", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1InitializeEncryptionSpecMetadata": {"description": "Metadata for initializing a location-level encryption specification.", "id": "GoogleCloudContactcenterinsightsV1alpha1InitializeEncryptionSpecMetadata", "properties": {"createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "partialErrors": {"description": "Partial errors during initializing operation that might cause the operation output to be incomplete.", "items": {"$ref": "GoogleRpcStatus"}, "type": "array"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1InitializeEncryptionSpecRequest", "description": "Output only. The original request for initialization.", "readOnly": true}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1InitializeEncryptionSpecRequest": {"description": "The request to initialize a location-level encryption specification.", "id": "GoogleCloudContactcenterinsightsV1alpha1InitializeEncryptionSpecRequest", "properties": {"encryptionSpec": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1EncryptionSpec", "description": "Required. The encryption spec used for CMEK encryption. It is required that the kms key is in the same region as the endpoint. The same key will be used for all provisioned resources, if encryption is available. If the `kms_key_name` field is left empty, no encryption will be enforced."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1InitializeEncryptionSpecResponse": {"description": "The response to initialize a location-level encryption specification.", "id": "GoogleCloudContactcenterinsightsV1alpha1InitializeEncryptionSpecResponse", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1Intent": {"description": "The data for an intent. Represents a detected intent in the conversation, for example MAKES_PROMISE.", "id": "GoogleCloudContactcenterinsightsV1alpha1Intent", "properties": {"displayName": {"description": "The human-readable name of the intent.", "type": "string"}, "id": {"description": "The unique identifier of the intent.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1IntentMatchData": {"description": "The data for an intent match. Represents an intent match for a text segment in the conversation. A text segment can be part of a sentence, a complete sentence, or an utterance with multiple sentences.", "id": "GoogleCloudContactcenterinsightsV1alpha1IntentMatchData", "properties": {"intentUniqueId": {"description": "The id of the matched intent. Can be used to retrieve the corresponding intent information.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1InterruptionData": {"description": "The data for an interruption annotation.", "id": "GoogleCloudContactcenterinsightsV1alpha1InterruptionData", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1Issue": {"description": "The issue resource.", "id": "GoogleCloudContactcenterinsightsV1alpha1Issue", "properties": {"createTime": {"description": "Output only. The time at which this issue was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayDescription": {"description": "Representative description of the issue.", "type": "string"}, "displayName": {"description": "The representative name for the issue.", "type": "string"}, "name": {"description": "Immutable. The resource name of the issue. Format: projects/{project}/locations/{location}/issueModels/{issue_model}/issues/{issue}", "type": "string"}, "sampleUtterances": {"description": "Output only. Resource names of the sample representative utterances that match to this issue.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "updateTime": {"description": "Output only. The most recent time that this issue was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1IssueAssignment": {"description": "Information about the issue.", "id": "GoogleCloudContactcenterinsightsV1alpha1IssueAssignment", "properties": {"displayName": {"description": "Immutable. Display name of the assigned issue. This field is set at time of analysis and immutable since then.", "type": "string"}, "issue": {"description": "Resource name of the assigned issue.", "type": "string"}, "score": {"description": "Score indicating the likelihood of the issue assignment. currently bounded on [0,1].", "format": "double", "type": "number"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1IssueMatchData": {"description": "The data for an issue match annotation.", "id": "GoogleCloudContactcenterinsightsV1alpha1IssueMatchData", "properties": {"issueAssignment": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1IssueAssignment", "description": "Information about the issue's assignment."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1IssueModel": {"description": "The issue model resource.", "id": "GoogleCloudContactcenterinsightsV1alpha1IssueModel", "properties": {"createTime": {"description": "Output only. The time at which this issue model was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "The representative name for the issue model.", "type": "string"}, "inputDataConfig": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1IssueModelInputDataConfig", "description": "Configs for the input data that used to create the issue model."}, "issueCount": {"description": "Output only. Number of issues in this issue model.", "format": "int64", "readOnly": true, "type": "string"}, "languageCode": {"description": "Language of the model.", "type": "string"}, "modelType": {"description": "Type of the model.", "enum": ["MODEL_TYPE_UNSPECIFIED", "TYPE_V1", "TYPE_V2"], "enumDescriptions": ["Unspecified model type.", "Type V1.", "Type V2."], "type": "string"}, "name": {"description": "Immutable. The resource name of the issue model. Format: projects/{project}/locations/{location}/issueModels/{issue_model}", "type": "string"}, "state": {"description": "Output only. State of the model.", "enum": ["STATE_UNSPECIFIED", "UNDEPLOYED", "DEPLOYING", "DEPLOYED", "UNDEPLOYING", "DELETING"], "enumDescriptions": ["Unspecified.", "Model is not deployed but is ready to deploy.", "Model is being deployed.", "Model is deployed and is ready to be used. A model can only be used in analysis if it's in this state.", "Model is being undeployed.", "Model is being deleted."], "readOnly": true, "type": "string"}, "trainingStats": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1IssueModelLabelStats", "description": "Output only. Immutable. The issue model's label statistics on its training data.", "readOnly": true}, "updateTime": {"description": "Output only. The most recent time at which the issue model was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1IssueModelInputDataConfig": {"description": "Configs for the input data used to create the issue model.", "id": "GoogleCloudContactcenterinsightsV1alpha1IssueModelInputDataConfig", "properties": {"filter": {"description": "A filter to reduce the conversations used for training the model to a specific subset. Refer to https://cloud.google.com/contact-center/insights/docs/filtering for details.", "type": "string"}, "medium": {"deprecated": true, "description": "Medium of conversations used in training data. This field is being deprecated. To specify the medium to be used in training a new issue model, set the `medium` field on `filter`.", "enum": ["MEDIUM_UNSPECIFIED", "PHONE_CALL", "CHAT"], "enumDescriptions": ["Default value, if unspecified will default to PHONE_CALL.", "The format for conversations that took place over the phone.", "The format for conversations that took place over chat."], "type": "string"}, "trainingConversationsCount": {"description": "Output only. Number of conversations used in training. Output only.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1IssueModelLabelStats": {"description": "Aggregated statistics about an issue model.", "id": "GoogleCloudContactcenterinsightsV1alpha1IssueModelLabelStats", "properties": {"analyzedConversationsCount": {"description": "Number of conversations the issue model has analyzed at this point in time.", "format": "int64", "type": "string"}, "issueStats": {"additionalProperties": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1IssueModelLabelStatsIssueStats"}, "description": "Statistics on each issue. Key is the issue's resource name.", "type": "object"}, "unclassifiedConversationsCount": {"description": "Number of analyzed conversations for which no issue was applicable at this point in time.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1IssueModelLabelStatsIssueStats": {"description": "Aggregated statistics about an issue.", "id": "GoogleCloudContactcenterinsightsV1alpha1IssueModelLabelStatsIssueStats", "properties": {"displayName": {"description": "Display name of the issue.", "type": "string"}, "issue": {"description": "Issue resource. Format: projects/{project}/locations/{location}/issueModels/{issue_model}/issues/{issue}", "type": "string"}, "labeledConversationsCount": {"description": "Number of conversations attached to the issue at this point in time.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1IssueModelResult": {"description": "Issue Modeling result on a conversation.", "id": "GoogleCloudContactcenterinsightsV1alpha1IssueModelResult", "properties": {"issueModel": {"description": "Issue model that generates the result. Format: projects/{project}/locations/{location}/issueModels/{issue_model}", "type": "string"}, "issues": {"description": "All the matched issues.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1IssueAssignment"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ListAllFeedbackLabelsResponse": {"description": "The response for listing all feedback labels.", "id": "GoogleCloudContactcenterinsightsV1alpha1ListAllFeedbackLabelsResponse", "properties": {"feedbackLabels": {"description": "The feedback labels that match the request.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1FeedbackLabel"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1ListFeedbackLabelsResponse": {"description": "The response for listing feedback labels.", "id": "GoogleCloudContactcenterinsightsV1alpha1ListFeedbackLabelsResponse", "properties": {"feedbackLabels": {"description": "The feedback labels that match the request.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1FeedbackLabel"}, "type": "array"}, "nextPageToken": {"description": "The next page token.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1PhraseMatchData": {"description": "The data for a matched phrase matcher. Represents information identifying a phrase matcher for a given match.", "id": "GoogleCloudContactcenterinsightsV1alpha1PhraseMatchData", "properties": {"displayName": {"description": "The human-readable name of the phrase matcher.", "type": "string"}, "phraseMatcher": {"description": "The unique identifier (the resource name) of the phrase matcher.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1QaAnswer": {"description": "An answer to a QaQuestion.", "id": "GoogleCloudContactcenterinsightsV1alpha1QaAnswer", "properties": {"answerSources": {"description": "List of all individual answers given to the question.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1QaAnswerAnswerSource"}, "type": "array"}, "answerValue": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1QaAnswerAnswerValue", "description": "The main answer value, incorporating any manual edits if they exist."}, "conversation": {"description": "The conversation the answer applies to.", "type": "string"}, "qaQuestion": {"description": "The QaQuestion answered by this answer.", "type": "string"}, "questionBody": {"description": "Question text. E.g., \"Did the agent greet the customer?\"", "type": "string"}, "tags": {"description": "User-defined list of arbitrary tags. Matches the value from QaScorecard.ScorecardQuestion.tags. Used for grouping/organization and for weighting the score of each answer.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1QaAnswerAnswerSource": {"description": "A question may have multiple answers from varying sources, one of which becomes the \"main\" answer above. AnswerSource represents each individual answer.", "id": "GoogleCloudContactcenterinsightsV1alpha1QaAnswerAnswerSource", "properties": {"answerValue": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1QaAnswerAnswerValue", "description": "The answer value from this source."}, "sourceType": {"description": "What created the answer.", "enum": ["SOURCE_TYPE_UNSPECIFIED", "SYSTEM_GENERATED", "MANUAL_EDIT"], "enumDescriptions": ["Source type is unspecified.", "Answer was system-generated; created during an Insights analysis.", "Answer was created by a human via manual edit."], "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1QaAnswerAnswerValue": {"description": "Message for holding the value of a QaAnswer. QaQuestion.AnswerChoice defines the possible answer values for a question.", "id": "GoogleCloudContactcenterinsightsV1alpha1QaAnswerAnswerValue", "properties": {"boolValue": {"description": "Boolean value.", "type": "boolean"}, "key": {"description": "A short string used as an identifier. Matches the value used in QaQuestion.AnswerChoice.key.", "type": "string"}, "naValue": {"description": "A value of \"Not Applicable (N/A)\". Should only ever be `true`.", "type": "boolean"}, "normalizedScore": {"description": "Output only. Normalized score of the questions. Calculated as score / potential_score.", "format": "double", "readOnly": true, "type": "number"}, "numValue": {"description": "Numerical value.", "format": "double", "type": "number"}, "potentialScore": {"description": "Output only. The maximum potential score of the question.", "format": "double", "readOnly": true, "type": "number"}, "score": {"description": "Output only. Numerical score of the answer.", "format": "double", "readOnly": true, "type": "number"}, "strValue": {"description": "String value.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1QaScorecardResult": {"description": "The results of scoring a single conversation against a QaScorecard. Contains a collection of QaAnswers and aggregate score.", "id": "GoogleCloudContactcenterinsightsV1alpha1QaScorecardResult", "properties": {"agentId": {"description": "ID of the agent that handled the conversation.", "type": "string"}, "conversation": {"description": "The conversation scored by this result.", "type": "string"}, "createTime": {"description": "Output only. The timestamp that the revision was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Identifier. The name of the scorecard result. Format: projects/{project}/locations/{location}/qaScorecardResults/{qa_scorecard_result}", "type": "string"}, "normalizedScore": {"description": "The normalized score, which is the score divided by the potential score. Any manual edits are included if they exist.", "format": "double", "type": "number"}, "potentialScore": {"description": "The maximum potential overall score of the scorecard. Any questions answered using `na_value` are excluded from this calculation.", "format": "double", "type": "number"}, "qaAnswers": {"description": "Set of QaAnswers represented in the result.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1QaAnswer"}, "type": "array"}, "qaScorecardRevision": {"description": "The QaScorecardRevision scored by this result.", "type": "string"}, "qaTagResults": {"description": "Collection of tags and their scores.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1QaScorecardResultQaTagResult"}, "type": "array"}, "score": {"description": "The overall numerical score of the result, incorporating any manual edits if they exist.", "format": "double", "type": "number"}, "scoreSources": {"description": "List of all individual score sets.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1QaScorecardResultScoreSource"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1QaScorecardResultQaTagResult": {"description": "Tags and their corresponding results.", "id": "GoogleCloudContactcenterinsightsV1alpha1QaScorecardResultQaTagResult", "properties": {"normalizedScore": {"description": "The normalized score the tag applies to.", "format": "double", "type": "number"}, "potentialScore": {"description": "The potential score the tag applies to.", "format": "double", "type": "number"}, "score": {"description": "The score the tag applies to.", "format": "double", "type": "number"}, "tag": {"description": "The tag the score applies to.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1QaScorecardResultScoreSource": {"description": "A scorecard result may have multiple sets of scores from varying sources, one of which becomes the \"main\" answer above. A ScoreSource represents each individual set of scores.", "id": "GoogleCloudContactcenterinsightsV1alpha1QaScorecardResultScoreSource", "properties": {"normalizedScore": {"description": "The normalized score, which is the score divided by the potential score.", "format": "double", "type": "number"}, "potentialScore": {"description": "The maximum potential overall score of the scorecard. Any questions answered using `na_value` are excluded from this calculation.", "format": "double", "type": "number"}, "qaTagResults": {"description": "Collection of tags and their scores.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1QaScorecardResultQaTagResult"}, "type": "array"}, "score": {"description": "The overall numerical score of the result.", "format": "double", "type": "number"}, "sourceType": {"description": "What created the score.", "enum": ["SOURCE_TYPE_UNSPECIFIED", "SYSTEM_GENERATED_ONLY", "INCLUDES_MANUAL_EDITS"], "enumDescriptions": ["Source type is unspecified.", "Score is derived only from system-generated answers.", "Score is derived from both system-generated answers, and includes any manual edits if they exist."], "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1QueryMetricsMetadata": {"description": "The metadata from querying metrics.", "id": "GoogleCloudContactcenterinsightsV1alpha1QueryMetricsMetadata", "properties": {"resultIsTruncated": {"description": "Whether the result rows were truncated because the result row size is too large to materialize.", "type": "boolean"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1QueryMetricsResponse": {"description": "The response for querying metrics.", "id": "GoogleCloudContactcenterinsightsV1alpha1QueryMetricsResponse", "properties": {"location": {"description": "Required. The location of the data. \"projects/{project}/locations/{location}\"", "type": "string"}, "macroAverageSlice": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1QueryMetricsResponseSlice", "description": "The macro average slice contains aggregated averages across the selected dimension. i.e. if group_by agent is specified this field will contain the average across all agents. This field is only populated if the request specifies a Dimension."}, "slices": {"description": "A slice contains a total and (if the request specified a time granularity) a time series of metric values. Each slice contains a unique combination of the cardinality of dimensions from the request.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1QueryMetricsResponseSlice"}, "type": "array"}, "updateTime": {"description": "The metrics last update time.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1QueryMetricsResponseSlice": {"description": "A slice contains a total and (if the request specified a time granularity) a time series of metric values. Each slice contains a unique combination of the cardinality of dimensions from the request. For example, if the request specifies a single ISSUE dimension and it has a cardinality of 2 (i.e. the data used to compute the metrics has 2 issues in total), the response will have 2 slices: * Slice 1 -> dimensions=[Issue 1] * Slice 2 -> dimensions=[Issue 2]", "id": "GoogleCloudContactcenterinsightsV1alpha1QueryMetricsResponseSlice", "properties": {"dimensions": {"description": "A unique combination of dimensions that this slice represents.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1Dimension"}, "type": "array"}, "timeSeries": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1QueryMetricsResponseSliceTimeSeries", "description": "A time series of metric values. This is only populated if the request specifies a time granularity other than NONE."}, "total": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1QueryMetricsResponseSliceDataPoint", "description": "The total metric value. The interval of this data point is [starting create time, ending create time) from the request."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1QueryMetricsResponseSliceDataPoint": {"description": "A data point contains the metric values mapped to an interval.", "id": "GoogleCloudContactcenterinsightsV1alpha1QueryMetricsResponseSliceDataPoint", "properties": {"conversationMeasure": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1QueryMetricsResponseSliceDataPointConversationMeasure", "description": "The measure related to conversations."}, "interval": {"$ref": "GoogleTypeInterval", "description": "The interval that this data point represents. * If this is the total data point, the interval is [starting create time, ending create time) from the request. * If this a data point from the time series, the interval is [time, time + time granularity from the request)."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1QueryMetricsResponseSliceDataPointConversationMeasure": {"description": "The measure related to conversations.", "id": "GoogleCloudContactcenterinsightsV1alpha1QueryMetricsResponseSliceDataPointConversationMeasure", "properties": {"averageAgentSentimentScore": {"description": "The average agent's sentiment score.", "format": "float", "type": "number"}, "averageClientSentimentScore": {"description": "The average client's sentiment score.", "format": "float", "type": "number"}, "averageCustomerSatisfactionRating": {"description": "The average customer satisfaction rating.", "format": "double", "type": "number"}, "averageDuration": {"description": "The average duration.", "format": "google-duration", "type": "string"}, "averageQaNormalizedScore": {"description": "Average QA normalized score. Will exclude 0's in average calculation.", "format": "double", "type": "number"}, "averageQaQuestionNormalizedScore": {"description": "Average QA normalized score averaged for questions averaged across all revisions of the parent scorecard. Will be only populated if the request specifies a dimension of QA_QUESTION_ID.", "format": "double", "type": "number"}, "averageSilencePercentage": {"description": "The average silence percentage.", "format": "float", "type": "number"}, "averageTurnCount": {"description": "The average turn count.", "format": "float", "type": "number"}, "conversationCount": {"description": "The conversation count.", "format": "int32", "type": "integer"}, "qaTagScores": {"description": "Average QA normalized score for all the tags.", "items": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1QueryMetricsResponseSliceDataPointConversationMeasureQaTagScore"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1QueryMetricsResponseSliceDataPointConversationMeasureQaTagScore": {"description": "Average QA normalized score for the tag.", "id": "GoogleCloudContactcenterinsightsV1alpha1QueryMetricsResponseSliceDataPointConversationMeasureQaTagScore", "properties": {"averageTagNormalizedScore": {"description": "Average tag normalized score per tag.", "format": "double", "type": "number"}, "tag": {"description": "Tag name.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1QueryMetricsResponseSliceTimeSeries": {"description": "A time series of metric values.", "id": "GoogleCloudContactcenterinsightsV1alpha1QueryMetricsResponseSliceTimeSeries", "properties": {"dataPoints": {"description": "The data points that make up the time series .", "items": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1QueryMetricsResponseSliceDataPoint"}, "type": "array"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1QueryPerformanceOverviewMetadata": {"description": "The metadata for querying performance overview.", "id": "GoogleCloudContactcenterinsightsV1alpha1QueryPerformanceOverviewMetadata", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1QueryPerformanceOverviewResponse": {"description": "The response for querying performance overview.", "id": "GoogleCloudContactcenterinsightsV1alpha1QueryPerformanceOverviewResponse", "properties": {"summaryText": {"description": "The summary text of the performance.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1RedactionConfig": {"description": "DLP resources used for redaction while ingesting conversations. DLP settings are applied to conversations ingested from the `UploadConversation` and `IngestConversations` endpoints, including conversation coming from CCAI Platform. They are not applied to conversations ingested from the `CreateConversation` endpoint or the Dialogflow / Agent Assist runtime integrations. When using Dialogflow / Agent Assist runtime integrations, redaction should be performed in Dialogflow / Agent Assist.", "id": "GoogleCloudContactcenterinsightsV1alpha1RedactionConfig", "properties": {"deidentifyTemplate": {"description": "The fully-qualified DLP deidentify template resource name. Format: `projects/{project}/deidentifyTemplates/{template}`", "type": "string"}, "inspectTemplate": {"description": "The fully-qualified DLP inspect template resource name. Format: `projects/{project}/locations/{location}/inspectTemplates/{template}`", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1RuntimeAnnotation": {"description": "An annotation that was generated during the customer and agent interaction.", "id": "GoogleCloudContactcenterinsightsV1alpha1RuntimeAnnotation", "properties": {"annotationId": {"description": "The unique identifier of the annotation. Format: projects/{project}/locations/{location}/conversationDatasets/{dataset}/conversationDataItems/{data_item}/conversationAnnotations/{annotation}", "type": "string"}, "answerFeedback": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1AnswerFeedback", "description": "The feedback that the customer has about the answer in `data`."}, "articleSuggestion": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1ArticleSuggestionData", "description": "Agent Assist Article Suggestion data."}, "conversationSummarizationSuggestion": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1ConversationSummarizationSuggestionData", "description": "Conversation summarization suggestion data."}, "createTime": {"description": "The time at which this annotation was created.", "format": "google-datetime", "type": "string"}, "dialogflowInteraction": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1DialogflowInteractionData", "description": "Dialogflow interaction data."}, "endBoundary": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1AnnotationBoundary", "description": "The boundary in the conversation where the annotation ends, inclusive."}, "faqAnswer": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1FaqAnswerData", "description": "Agent Assist FAQ answer data."}, "smartComposeSuggestion": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1SmartComposeSuggestionData", "description": "Agent Assist Smart Compose suggestion data."}, "smartReply": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1SmartReplyData", "description": "Agent Assist Smart Reply data."}, "startBoundary": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1AnnotationBoundary", "description": "The boundary in the conversation where the annotation starts, inclusive."}, "userInput": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1RuntimeAnnotationUserInput", "description": "Explicit input used for generating the answer"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1RuntimeAnnotationUserInput": {"description": "Explicit input used for generating the answer", "id": "GoogleCloudContactcenterinsightsV1alpha1RuntimeAnnotationUserInput", "properties": {"generatorName": {"description": "The resource name of associated generator. Format: `projects//locations//generators/`", "type": "string"}, "query": {"description": "Query text. Article Search uses this to store the input query used to generate the search results.", "type": "string"}, "querySource": {"description": "Query source for the answer.", "enum": ["QUERY_SOURCE_UNSPECIFIED", "AGENT_QUERY", "SUGGESTED_QUERY"], "enumDescriptions": ["Unknown query source.", "The query is from agents.", "The query is a query from previous suggestions, e.g. from a preceding SuggestKnowledgeAssist response."], "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1SampleConversationsMetadata": {"description": "The metadata for an SampleConversations operation.", "id": "GoogleCloudContactcenterinsightsV1alpha1SampleConversationsMetadata", "properties": {"createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "partialErrors": {"description": "Output only. Partial errors during sample conversations operation that might cause the operation output to be incomplete.", "items": {"$ref": "GoogleRpcStatus"}, "readOnly": true, "type": "array"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1SampleConversationsRequest", "description": "Output only. The original request for sample conversations to dataset.", "readOnly": true}, "sampleConversationsStats": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1SampleConversationsMetadataSampleConversationsStats", "description": "Output only. Statistics for SampleConversations operation.", "readOnly": true}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1SampleConversationsMetadataSampleConversationsStats": {"description": "Statistics for SampleConversations operation.", "id": "GoogleCloudContactcenterinsightsV1alpha1SampleConversationsMetadataSampleConversationsStats", "properties": {"failedSampleCount": {"description": "Output only. The number of objects which were unable to be sampled due to errors. The errors are populated in the partial_errors field.", "format": "int32", "readOnly": true, "type": "integer"}, "successfulSampleCount": {"description": "Output only. The number of new conversations added during this sample operation.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1SampleConversationsRequest": {"description": "The request to sample conversations to a dataset.", "id": "GoogleCloudContactcenterinsightsV1alpha1SampleConversationsRequest", "properties": {"destinationDataset": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1Dataset", "description": "The dataset resource to copy the sampled conversations to."}, "parent": {"description": "Required. The parent resource of the dataset.", "type": "string"}, "sampleRule": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1SampleRule", "description": "Optional. The sample rule used for sampling conversations."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1SampleConversationsResponse": {"description": "The response to an SampleConversations operation.", "id": "GoogleCloudContactcenterinsightsV1alpha1SampleConversationsResponse", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1SampleRule": {"description": "Message for sampling conversations.", "id": "GoogleCloudContactcenterinsightsV1alpha1SampleRule", "properties": {"conversationFilter": {"description": "To specify the filter for the conversions that should apply this sample rule. An empty filter means this sample rule applies to all conversations.", "type": "string"}, "dimension": {"description": "Optional. Group by dimension to sample the conversation. If no dimension is provided, the sampling will be applied to the project level. Current supported dimensions is 'quality_metadata.agent_info.agent_id'.", "type": "string"}, "samplePercentage": {"description": "Percentage of conversations that we should sample based on the dimension between [0, 100].", "format": "double", "type": "number"}, "sampleRow": {"description": "Number of the conversations that we should sample based on the dimension.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1SentimentData": {"description": "The data for a sentiment annotation.", "id": "GoogleCloudContactcenterinsightsV1alpha1SentimentData", "properties": {"magnitude": {"description": "A non-negative number from 0 to infinity which represents the absolute magnitude of sentiment regardless of score.", "format": "float", "type": "number"}, "score": {"description": "The sentiment score between -1.0 (negative) and 1.0 (positive).", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1SilenceData": {"description": "The data for a silence annotation.", "id": "GoogleCloudContactcenterinsightsV1alpha1SilenceData", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1SmartComposeSuggestionData": {"description": "Agent Assist Smart Compose suggestion data.", "id": "GoogleCloudContactcenterinsightsV1alpha1SmartComposeSuggestionData", "properties": {"confidenceScore": {"description": "The system's confidence score that this suggestion is a good match for this conversation, ranging from 0.0 (completely uncertain) to 1.0 (completely certain).", "format": "double", "type": "number"}, "metadata": {"additionalProperties": {"type": "string"}, "description": "Map that contains metadata about the Smart Compose suggestion and the document from which it originates.", "type": "object"}, "queryRecord": {"description": "The name of the answer record. Format: projects/{project}/locations/{location}/answerRecords/{answer_record}", "type": "string"}, "suggestion": {"description": "The content of the suggestion.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1SmartReplyData": {"description": "Agent Assist Smart Reply data.", "id": "GoogleCloudContactcenterinsightsV1alpha1SmartReplyData", "properties": {"confidenceScore": {"description": "The system's confidence score that this reply is a good match for this conversation, ranging from 0.0 (completely uncertain) to 1.0 (completely certain).", "format": "double", "type": "number"}, "metadata": {"additionalProperties": {"type": "string"}, "description": "Map that contains metadata about the Smart Reply and the document from which it originates.", "type": "object"}, "queryRecord": {"description": "The name of the answer record. Format: projects/{project}/locations/{location}/answerRecords/{answer_record}", "type": "string"}, "reply": {"description": "The content of the reply.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1SpeechConfig": {"description": "Speech-to-Text configuration. Speech-to-Text settings are applied to conversations ingested from the `UploadConversation` and `IngestConversations` endpoints, including conversation coming from CCAI Platform. They are not applied to conversations ingested from the `CreateConversation` endpoint.", "id": "GoogleCloudContactcenterinsightsV1alpha1SpeechConfig", "properties": {"speechRecognizer": {"description": "The fully-qualified Speech Recognizer resource name. Format: `projects/{project_id}/locations/{location}/recognizer/{recognizer}`", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1UndeployIssueModelMetadata": {"description": "Metadata for undeploying an issue model.", "id": "GoogleCloudContactcenterinsightsV1alpha1UndeployIssueModelMetadata", "properties": {"createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1UndeployIssueModelRequest", "description": "The original request for undeployment."}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1UndeployIssueModelRequest": {"description": "The request to undeploy an issue model.", "id": "GoogleCloudContactcenterinsightsV1alpha1UndeployIssueModelRequest", "properties": {"name": {"description": "Required. The issue model to undeploy.", "type": "string"}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1UndeployIssueModelResponse": {"description": "The response to undeploy an issue model.", "id": "GoogleCloudContactcenterinsightsV1alpha1UndeployIssueModelResponse", "properties": {}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1UploadConversationMetadata": {"description": "The metadata for an `UploadConversation` operation.", "id": "GoogleCloudContactcenterinsightsV1alpha1UploadConversationMetadata", "properties": {"analysisOperation": {"description": "Output only. The operation name for a successfully created analysis operation, if any.", "readOnly": true, "type": "string"}, "appliedRedactionConfig": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1RedactionConfig", "description": "Output only. The redaction config applied to the uploaded conversation.", "readOnly": true}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "request": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1UploadConversationRequest", "description": "Output only. The original request.", "readOnly": true}}, "type": "object"}, "GoogleCloudContactcenterinsightsV1alpha1UploadConversationRequest": {"description": "Request to upload a conversation.", "id": "GoogleCloudContactcenterinsightsV1alpha1UploadConversationRequest", "properties": {"conversation": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1Conversation", "description": "Required. The conversation resource to create."}, "conversationId": {"description": "Optional. A unique ID for the new conversation. This ID will become the final component of the conversation's resource name. If no ID is specified, a server-generated ID will be used. This value should be 4-64 characters and must match the regular expression `^[a-z0-9-]{4,64}$`. Valid characters are `a-z-`", "type": "string"}, "parent": {"description": "Required. The parent resource of the conversation.", "type": "string"}, "redactionConfig": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1RedactionConfig", "description": "Optional. DLP settings for transcript redaction. Will default to the config specified in Settings."}, "speechConfig": {"$ref": "GoogleCloudContactcenterinsightsV1alpha1SpeechConfig", "description": "Optional. Speech-to-Text configuration. Will default to the config specified in Settings."}}, "type": "object"}, "GoogleLongrunningListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "GoogleLongrunningListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "GoogleLongrunningOperation"}, "type": "array"}}, "type": "object"}, "GoogleLongrunningOperation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "GoogleLongrunningOperation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "GoogleRpcStatus", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "GoogleProtobufEmpty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "GoogleProtobufEmpty", "properties": {}, "type": "object"}, "GoogleRpcStatus": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "GoogleRpcStatus", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "GoogleTypeInterval": {"description": "Represents a time interval, encoded as a Timestamp start (inclusive) and a Timestamp end (exclusive). The start must be less than or equal to the end. When the start equals the end, the interval is empty (matches no time). When both start and end are unspecified, the interval matches any time.", "id": "GoogleTypeInterval", "properties": {"endTime": {"description": "Optional. Exclusive end of the interval. If specified, a Timestamp matching this interval will have to be before the end.", "format": "google-datetime", "type": "string"}, "startTime": {"description": "Optional. Inclusive start of the interval. If specified, a Timestamp matching this interval will have to be the same or after the start.", "format": "google-datetime", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Contact Center AI Insights API", "version": "v1", "version_module": true}