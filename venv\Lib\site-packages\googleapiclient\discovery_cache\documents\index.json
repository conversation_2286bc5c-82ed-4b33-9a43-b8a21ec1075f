{"kind": "discovery#directoryList", "discoveryVersion": "v1", "items": [{"kind": "discovery#directoryItem", "id": "abusiveexperiencereport:v1", "name": "abusiveexperiencereport", "version": "v1", "title": "Abusive Experience Report API", "description": "Views Abusive Experience Report data, and gets a list of sites that have a significant number of abusive experiences.", "discoveryRestUrl": "https://abusiveexperiencereport.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/abusive-experience-report/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "acceleratedmobilepageurl:v1", "name": "acceleratedmobilepageurl", "version": "v1", "title": "Accelerated Mobile Pages (AMP) URL API", "description": "Retrieves the list of AMP URLs (and equivalent AMP Cache URLs) for a given list of public URL(s).", "discoveryRestUrl": "https://acceleratedmobilepageurl.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/amp/cache/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "accessapproval:v1", "name": "accessapproval", "version": "v1", "title": "Access Approval API", "description": "An API for controlling access to data by Google personnel.", "discoveryRestUrl": "https://accessapproval.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/access-approval/docs", "preferred": true}, {"kind": "discovery#directoryItem", "id": "accesscontextmanager:v1beta", "name": "accesscontextmanager", "version": "v1beta", "title": "Access Context Manager API", "description": "An API for setting attribute based access control to requests to GCP services.", "discoveryRestUrl": "https://accesscontextmanager.googleapis.com/$discovery/rest?version=v1beta", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/access-context-manager/docs/reference/rest/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "accesscontextmanager:v1", "name": "accesscontextmanager", "version": "v1", "title": "Access Context Manager API", "description": "An API for setting attribute based access control to requests to GCP services.", "discoveryRestUrl": "https://accesscontextmanager.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/access-context-manager/docs/reference/rest/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "adexchangebuyer:v1.2", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "v1.2", "title": "Ad Exchange Buyer API", "description": "Accesses your bidding-account information, submits creatives for validation, finds available direct deals, and retrieves performance reports.", "discoveryRestUrl": "https://www.googleapis.com/discovery/v1/apis/adexchangebuyer/v1.2/rest", "discoveryLink": "./apis/adexchangebuyer/v1.2/rest", "icons": {"x16": "https://www.google.com/images/icons/product/doubleclick-16.gif", "x32": "https://www.google.com/images/icons/product/doubleclick-32.gif"}, "documentationLink": "https://developers.google.com/ad-exchange/buyer-rest", "preferred": false}, {"kind": "discovery#directoryItem", "id": "adexchangebuyer:v1.3", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "v1.3", "title": "Ad Exchange Buyer API", "description": "Accesses your bidding-account information, submits creatives for validation, finds available direct deals, and retrieves performance reports.", "discoveryRestUrl": "https://www.googleapis.com/discovery/v1/apis/adexchangebuyer/v1.3/rest", "discoveryLink": "./apis/adexchangebuyer/v1.3/rest", "icons": {"x16": "https://www.google.com/images/icons/product/doubleclick-16.gif", "x32": "https://www.google.com/images/icons/product/doubleclick-32.gif"}, "documentationLink": "https://developers.google.com/ad-exchange/buyer-rest", "preferred": false}, {"kind": "discovery#directoryItem", "id": "adexchangebuyer:v1.4", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "v1.4", "title": "Ad Exchange Buyer API", "description": "Accesses your bidding-account information, submits creatives for validation, finds available direct deals, and retrieves performance reports.", "discoveryRestUrl": "https://www.googleapis.com/discovery/v1/apis/adexchangebuyer/v1.4/rest", "discoveryLink": "./apis/adexchangebuyer/v1.4/rest", "icons": {"x16": "https://www.google.com/images/icons/product/doubleclick-16.gif", "x32": "https://www.google.com/images/icons/product/doubleclick-32.gif"}, "documentationLink": "https://developers.google.com/ad-exchange/buyer-rest", "preferred": true}, {"kind": "discovery#directoryItem", "id": "adexchangebuyer2:v2beta1", "name": "adexchangebuyer2", "version": "v2beta1", "title": "Ad Exchange Buyer API II", "description": "Accesses the latest features for managing Authorized Buyers accounts, Real-Time Bidding configurations and auction metrics, and Marketplace programmatic deals.", "discoveryRestUrl": "https://adexchangebuyer.googleapis.com/$discovery/rest?version=v2beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/authorized-buyers/apis/reference/rest/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "adexperiencereport:v1", "name": "adexperiencereport", "version": "v1", "title": "Ad Experience Report API", "description": "Views Ad Experience Report data, and gets a list of sites that have a significant number of annoying ads.", "discoveryRestUrl": "https://adexperiencereport.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/ad-experience-report/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "admin:datatransfer_v1", "name": "admin", "version": "datatransfer_v1", "title": "Admin SDK API", "description": "Admin SDK lets administrators of enterprise domains to view and manage resources like user, groups etc. It also provides audit and usage reports of domain.", "discoveryRestUrl": "https://admin.googleapis.com/$discovery/rest?version=datatransfer_v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "http://developers.google.com/admin-sdk/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "admin:directory_v1", "name": "admin", "version": "directory_v1", "title": "Admin SDK API", "description": "Admin SDK lets administrators of enterprise domains to view and manage resources like user, groups etc. It also provides audit and usage reports of domain.", "discoveryRestUrl": "https://admin.googleapis.com/$discovery/rest?version=directory_v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "http://developers.google.com/admin-sdk/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "admin:reports_v1", "name": "admin", "version": "reports_v1", "title": "Admin SDK API", "description": "Admin SDK lets administrators of enterprise domains to view and manage resources like user, groups etc. It also provides audit and usage reports of domain.", "discoveryRestUrl": "https://admin.googleapis.com/$discovery/rest?version=reports_v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "http://developers.google.com/admin-sdk/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "admob:v1beta", "name": "admob", "version": "v1beta", "title": "AdMob API", "description": "The AdMob API allows publishers to programmatically get information about their AdMob account.", "discoveryRestUrl": "https://admob.googleapis.com/$discovery/rest?version=v1beta", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/admob/api/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "admob:v1", "name": "admob", "version": "v1", "title": "AdMob API", "description": "The AdMob API allows publishers to programmatically get information about their AdMob account.", "discoveryRestUrl": "https://admob.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/admob/api/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "adsense:v1.4", "name": "adsense", "version": "v1.4", "title": "AdSense Management API", "description": "The AdSense Management API allows publishers to access their inventory and run earnings and performance reports.", "discoveryRestUrl": "https://adsense.googleapis.com/$discovery/rest?version=v1.4", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "http://code.google.com/apis/adsense/management/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "adsensehost:v4.1", "name": "adsensehost", "version": "v4.1", "title": "AdSense Host API", "description": "The AdSense Host API gives AdSense Hosts access to report generation, ad code generation, and publisher management capabilities.", "discoveryRestUrl": "https://adsensehost.googleapis.com/$discovery/rest?version=v4.1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/adsense/host/index", "preferred": true}, {"kind": "discovery#directoryItem", "id": "alertcenter:v1beta1", "name": "alertcenter", "version": "v1beta1", "title": "G Suite Alert Center API", "description": "Manages alerts on issues affecting your domain.", "discoveryRestUrl": "https://alertcenter.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/admin-sdk/alertcenter/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "analytics:v3", "name": "analytics", "version": "v3", "title": "Google Analytics API", "description": "The Analytics API provides access to Analytics configuration and report data.", "discoveryRestUrl": "https://analytics.googleapis.com/$discovery/rest?version=v3", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "http://code.google.com/apis/analytics/docs/mgmt/home.html", "preferred": true}, {"kind": "discovery#directoryItem", "id": "analyticsadmin:v1alpha", "name": "analyticsadmin", "version": "v1alpha", "title": "Google Analytics Admin API", "description": "", "discoveryRestUrl": "https://analyticsadmin.googleapis.com/$discovery/rest?version=v1alpha", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "http://code.google.com/apis/analytics/docs/mgmt/home.html", "preferred": true}, {"kind": "discovery#directoryItem", "id": "analyticsdata:v1alpha", "name": "analyticsdata", "version": "v1alpha", "title": "Google Analytics Data API", "description": "Accesses report data in Google Analytics.", "discoveryRestUrl": "https://analyticsdata.googleapis.com/$discovery/rest?version=v1alpha", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/analytics/trusted-testing/analytics-data/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "analyticsreporting:v4", "name": "analyticsreporting", "version": "v4", "title": "Analytics Reporting API", "description": "Accesses Analytics report data.", "discoveryRestUrl": "https://analyticsreporting.googleapis.com/$discovery/rest?version=v4", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/analytics/devguides/reporting/core/v4/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "androiddeviceprovisioning:v1", "name": "androiddeviceprovisioning", "version": "v1", "title": "Android Device Provisioning Partner API", "description": "Automates Android zero-touch enrollment for device resellers, customers, and EMMs.", "discoveryRestUrl": "https://androiddeviceprovisioning.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/zero-touch/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "androidenterprise:v1", "name": "androidenterprise", "version": "v1", "title": "Google Play EMM API", "description": "Manages the deployment of apps to Android Enterprise devices.", "discoveryRestUrl": "https://androidenterprise.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "preferred": true}, {"kind": "discovery#directoryItem", "id": "androidmanagement:v1", "name": "androidmanagement", "version": "v1", "title": "Android Management API", "description": "The Android Management API provides remote enterprise management of Android devices and apps.", "discoveryRestUrl": "https://androidmanagement.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/android/management", "preferred": true}, {"kind": "discovery#directoryItem", "id": "androidpublisher:v3", "name": "androidpublisher", "version": "v3", "title": "Google Play Android Developer API", "description": "Lets Android application developers access their Google Play accounts.", "discoveryRestUrl": "https://androidpublisher.googleapis.com/$discovery/rest?version=v3", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/android-publisher", "preferred": true}, {"kind": "discovery#directoryItem", "id": "apigateway:v1beta", "name": "apigateway", "version": "v1beta", "title": "API Gateway API", "description": "", "discoveryRestUrl": "https://apigateway.googleapis.com/$discovery/rest?version=v1beta", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": " https://cloud.google.com/api-gateway/docs", "preferred": true}, {"kind": "discovery#directoryItem", "id": "apigee:v1", "name": "apigee", "version": "v1", "title": "Apigee API", "description": "Use the Apigee API to programmatically develop and manage APIs with a set of RESTful operations. Develop and secure API proxies, deploy and undeploy API proxy revisions, monitor APIs, configure environments, manage users, and more. Get started using the APIs <https://cloud.google.com/apigee/docs/api-platform/get-started/api-get-started>. *Note:* This product is available as a free trial for a time period of 60 days.", "discoveryRestUrl": "https://apigee.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/apigee-api-management/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "appengine:v1alpha", "name": "appengine", "version": "v1alpha", "title": "App Engine Admin API", "description": "Provisions and manages developers' App Engine applications.", "discoveryRestUrl": "https://appengine.googleapis.com/$discovery/rest?version=v1alpha", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/appengine/docs/admin-api/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "appengine:v1beta", "name": "appengine", "version": "v1beta", "title": "App Engine Admin API", "description": "Provisions and manages developers' App Engine applications.", "discoveryRestUrl": "https://appengine.googleapis.com/$discovery/rest?version=v1beta", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/appengine/docs/admin-api/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "appengine:v1beta4", "name": "appengine", "version": "v1beta4", "title": "App Engine Admin API", "description": "Provisions and manages developers' App Engine applications.", "discoveryRestUrl": "https://appengine.googleapis.com/$discovery/rest?version=v1beta4", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/appengine/docs/admin-api/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "appengine:v1beta5", "name": "appengine", "version": "v1beta5", "title": "App Engine Admin API", "description": "Provisions and manages developers' App Engine applications.", "discoveryRestUrl": "https://appengine.googleapis.com/$discovery/rest?version=v1beta5", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/appengine/docs/admin-api/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "appengine:v1", "name": "appengine", "version": "v1", "title": "App Engine Admin API", "description": "Provisions and manages developers' App Engine applications.", "discoveryRestUrl": "https://appengine.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/appengine/docs/admin-api/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "area120tables:v1alpha1", "name": "area120tables", "version": "v1alpha1", "title": "Area120 Tables API", "description": "", "discoveryRestUrl": "https://area120tables.googleapis.com/$discovery/rest?version=v1alpha1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://tables.area120.google.com", "preferred": true}, {"kind": "discovery#directoryItem", "id": "artifactregistry:v1beta1", "name": "artifactregistry", "version": "v1beta1", "title": "Artifact Registry API", "description": "Store and manage build artifacts in a scalable and integrated service built on Google infrastructure.", "discoveryRestUrl": "https://artifactregistry.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/artifacts/docs/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "artifactregistry:v1beta2", "name": "artifactregistry", "version": "v1beta2", "title": "Artifact Registry API", "description": "Store and manage build artifacts in a scalable and integrated service built on Google infrastructure.", "discoveryRestUrl": "https://artifactregistry.googleapis.com/$discovery/rest?version=v1beta2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/artifacts/docs/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "assuredworkloads:v1", "name": "assuredworkloads", "version": "v1", "title": "Assured Workloads API", "description": "", "discoveryRestUrl": "https://assuredworkloads.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com", "preferred": true}, {"kind": "discovery#directoryItem", "id": "bigquery:v2", "name": "big<PERSON>y", "version": "v2", "title": "BigQuery API", "description": "A data platform for customers to create, manage, share and query data.", "discoveryRestUrl": "https://bigquery.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/bigquery/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "bigqueryconnection:v1beta1", "name": "bigqueryconnection", "version": "v1beta1", "title": "BigQuery Connection API", "description": "Allows users to manage BigQuery connections to external data sources.", "discoveryRestUrl": "https://bigqueryconnection.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/bigquery/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "bigquerydatatransfer:v1", "name": "bigquerydatatransfer", "version": "v1", "title": "BigQuery Data Transfer API", "description": "Schedule queries or transfer external data from SaaS applications to Google BigQuery on a regular basis.", "discoveryRestUrl": "https://bigquerydatatransfer.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/bigquery/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "bigqueryreservation:v1alpha2", "name": "bigqueryreservation", "version": "v1alpha2", "title": "BigQuery Reservation API", "description": "A service to modify your BigQuery flat-rate reservations.", "discoveryRestUrl": "https://bigqueryreservation.googleapis.com/$discovery/rest?version=v1alpha2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/bigquery/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "bigqueryreservation:v1beta1", "name": "bigqueryreservation", "version": "v1beta1", "title": "BigQuery Reservation API", "description": "A service to modify your BigQuery flat-rate reservations.", "discoveryRestUrl": "https://bigqueryreservation.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/bigquery/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "bigqueryreservation:v1", "name": "bigqueryreservation", "version": "v1", "title": "BigQuery Reservation API", "description": "A service to modify your BigQuery flat-rate reservations.", "discoveryRestUrl": "https://bigqueryreservation.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/bigquery/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "bigtableadmin:v1", "name": "big<PERSON><PERSON>min", "version": "v1", "title": "Cloud Bigtable Admin API", "description": "Administer your Cloud Bigtable tables and instances.", "discoveryRestUrl": "https://bigtableadmin.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/bigtable/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "bigtableadmin:v2", "name": "big<PERSON><PERSON>min", "version": "v2", "title": "Cloud Bigtable Admin API", "description": "Administer your Cloud Bigtable tables and instances.", "discoveryRestUrl": "https://bigtableadmin.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/bigtable/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "billingbudgets:v1beta1", "name": "billingbudgets", "version": "v1beta1", "title": "Cloud Billing Budget API", "description": "The Cloud Billing Budget API stores Cloud Billing budgets, which define a budget plan and the rules to execute as spend is tracked against that plan.", "discoveryRestUrl": "https://billingbudgets.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/billing/docs/how-to/budget-api-overview", "preferred": false}, {"kind": "discovery#directoryItem", "id": "billingbudgets:v1", "name": "billingbudgets", "version": "v1", "title": "Cloud Billing Budget API", "description": "The Cloud Billing Budget API stores Cloud Billing budgets, which define a budget plan and the rules to execute as spend is tracked against that plan.", "discoveryRestUrl": "https://billingbudgets.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/billing/docs/how-to/budget-api-overview", "preferred": true}, {"kind": "discovery#directoryItem", "id": "binaryauthorization:v1beta1", "name": "binaryauthorization", "version": "v1beta1", "title": "Binary Authorization API", "description": "The management interface for Binary Authorization, a system providing policy control for images deployed to Kubernetes Engine clusters.", "discoveryRestUrl": "https://binaryauthorization.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/binary-authorization/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "binaryauthorization:v1", "name": "binaryauthorization", "version": "v1", "title": "Binary Authorization API", "description": "The management interface for Binary Authorization, a system providing policy control for images deployed to Kubernetes Engine clusters.", "discoveryRestUrl": "https://binaryauthorization.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/binary-authorization/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "blogger:v2", "name": "blogger", "version": "v2", "title": "Blogger API v3", "description": "The Blogger API provides access to posts, comments and pages of a Blogger blog.", "discoveryRestUrl": "https://blogger.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/blogger/docs/3.0/getting_started", "preferred": false}, {"kind": "discovery#directoryItem", "id": "blogger:v3", "name": "blogger", "version": "v3", "title": "Blogger API v3", "description": "The Blogger API provides access to posts, comments and pages of a Blogger blog.", "discoveryRestUrl": "https://blogger.googleapis.com/$discovery/rest?version=v3", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/blogger/docs/3.0/getting_started", "preferred": true}, {"kind": "discovery#directoryItem", "id": "books:v1", "name": "books", "version": "v1", "title": "Books API", "description": "The Google Books API allows clients to access the Google Books repository.", "discoveryRestUrl": "https://books.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://code.google.com/apis/books/docs/v1/getting_started.html", "preferred": true}, {"kind": "discovery#directoryItem", "id": "calendar:v3", "name": "calendar", "version": "v3", "title": "Google Calendar API", "description": "The Google Calendar API lets you manage your calendars and events.", "discoveryRestUrl": "https://calendar-json.googleapis.com/$discovery/rest?version=v3", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "http://code.google.com/apis/calendar/v3/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "chat:v1", "name": "chat", "version": "v1", "title": "Hangouts Chat API", "description": "Enables bots to fetch information and perform actions in Hangouts Chat.", "discoveryRestUrl": "https://chat.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/hangouts/chat", "preferred": true}, {"kind": "discovery#directoryItem", "id": "chromeuxreport:v1", "name": "chromeuxreport", "version": "v1", "title": "Chrome UX Report API", "description": "The Chrome UX Report API lets you view real user experience data for millions of websites.", "discoveryRestUrl": "https://chromeuxreport.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/web/tools/chrome-user-experience-report/api/reference", "preferred": true}, {"kind": "discovery#directoryItem", "id": "civicinfo:v2", "name": "civicinfo", "version": "v2", "title": "Google Civic Information API", "description": "Provides polling places, early vote locations, contest data, election officials, and government representatives for U.S. residential addresses.", "discoveryRestUrl": "https://civicinfo.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/civic-information/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "classroom:v1", "name": "classroom", "version": "v1", "title": "Google Classroom API", "description": "Manages classes, rosters, and invitations in Google Classroom.", "discoveryRestUrl": "https://classroom.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/classroom", "preferred": true}, {"kind": "discovery#directoryItem", "id": "cloudasset:v1p1beta1", "name": "cloudasset", "version": "v1p1beta1", "title": "Cloud Asset API", "description": "The cloud asset API manages the history and inventory of cloud resources.", "discoveryRestUrl": "https://cloudasset.googleapis.com/$discovery/rest?version=v1p1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/asset-inventory/docs/quickstart", "preferred": false}, {"kind": "discovery#directoryItem", "id": "cloudasset:v1p4beta1", "name": "cloudasset", "version": "v1p4beta1", "title": "Cloud Asset API", "description": "The cloud asset API manages the history and inventory of cloud resources.", "discoveryRestUrl": "https://cloudasset.googleapis.com/$discovery/rest?version=v1p4beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/asset-inventory/docs/quickstart", "preferred": false}, {"kind": "discovery#directoryItem", "id": "cloudasset:v1p5beta1", "name": "cloudasset", "version": "v1p5beta1", "title": "Cloud Asset API", "description": "The cloud asset API manages the history and inventory of cloud resources.", "discoveryRestUrl": "https://cloudasset.googleapis.com/$discovery/rest?version=v1p5beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/asset-inventory/docs/quickstart", "preferred": false}, {"kind": "discovery#directoryItem", "id": "cloudasset:v1beta1", "name": "cloudasset", "version": "v1beta1", "title": "Cloud Asset API", "description": "The cloud asset API manages the history and inventory of cloud resources.", "discoveryRestUrl": "https://cloudasset.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/asset-inventory/docs/quickstart", "preferred": false}, {"kind": "discovery#directoryItem", "id": "cloudasset:v1", "name": "cloudasset", "version": "v1", "title": "Cloud Asset API", "description": "The cloud asset API manages the history and inventory of cloud resources.", "discoveryRestUrl": "https://cloudasset.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/asset-inventory/docs/quickstart", "preferred": true}, {"kind": "discovery#directoryItem", "id": "cloudbilling:v1", "name": "cloudbilling", "version": "v1", "title": "Cloud Billing API", "description": "Allows developers to manage billing for their Google Cloud Platform projects programmatically.", "discoveryRestUrl": "https://cloudbilling.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/billing/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "cloudbuild:v1alpha1", "name": "cloudbuild", "version": "v1alpha1", "title": "Cloud Build API", "description": "Creates and manages builds on Google Cloud Platform.", "discoveryRestUrl": "https://cloudbuild.googleapis.com/$discovery/rest?version=v1alpha1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/cloud-build/docs/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "cloudbuild:v1alpha2", "name": "cloudbuild", "version": "v1alpha2", "title": "Cloud Build API", "description": "Creates and manages builds on Google Cloud Platform.", "discoveryRestUrl": "https://cloudbuild.googleapis.com/$discovery/rest?version=v1alpha2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/cloud-build/docs/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "cloudbuild:v1", "name": "cloudbuild", "version": "v1", "title": "Cloud Build API", "description": "Creates and manages builds on Google Cloud Platform.", "discoveryRestUrl": "https://cloudbuild.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/cloud-build/docs/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "clouddebugger:v2", "name": "clouddebugger", "version": "v2", "title": "Cloud Debugger API", "description": "Examines the call stack and variables of a running application without stopping or slowing it down.", "discoveryRestUrl": "https://clouddebugger.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/debugger", "preferred": true}, {"kind": "discovery#directoryItem", "id": "clouderrorreporting:v1beta1", "name": "clouderrorreporting", "version": "v1beta1", "title": "Error Reporting API", "description": "Groups and counts similar errors from cloud services and applications, reports new errors, and provides access to error groups and their associated errors.", "discoveryRestUrl": "https://clouderrorreporting.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/error-reporting/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "cloudfunctions:v1", "name": "cloudfunctions", "version": "v1", "title": "Cloud Functions API", "description": "Manages lightweight user-provided functions executed in response to events.", "discoveryRestUrl": "https://cloudfunctions.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/functions", "preferred": true}, {"kind": "discovery#directoryItem", "id": "cloudidentity:v1beta1", "name": "cloudidentity", "version": "v1beta1", "title": "Cloud Identity API", "description": "API for provisioning and managing identity resources.", "discoveryRestUrl": "https://cloudidentity.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/identity/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "cloudidentity:v1", "name": "cloudidentity", "version": "v1", "title": "Cloud Identity API", "description": "API for provisioning and managing identity resources.", "discoveryRestUrl": "https://cloudidentity.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/identity/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "cloudiot:v1", "name": "cloudiot", "version": "v1", "title": "Cloud IoT API", "description": "Registers and manages IoT (Internet of Things) devices that connect to the Google Cloud Platform.", "discoveryRestUrl": "https://cloudiot.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/iot", "preferred": true}, {"kind": "discovery#directoryItem", "id": "cloudkms:v1", "name": "cloudkms", "version": "v1", "title": "Cloud Key Management Service (KMS) API", "description": "Manages keys and performs cryptographic operations in a central cloud service, for direct use by other cloud resources and applications.", "discoveryRestUrl": "https://cloudkms.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/kms/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "cloudprofiler:v2", "name": "cloudprofiler", "version": "v2", "title": "Stackdriver Profiler API", "description": "Manages continuous profiling information.", "discoveryRestUrl": "https://cloudprofiler.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/profiler/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "cloudresourcemanager:v1beta1", "name": "cloudresourcemanager", "version": "v1beta1", "title": "Cloud Resource Manager API", "description": "Creates, reads, and updates metadata for Google Cloud Platform resource containers.", "discoveryRestUrl": "https://cloudresourcemanager.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/resource-manager", "preferred": false}, {"kind": "discovery#directoryItem", "id": "cloudresourcemanager:v2beta1", "name": "cloudresourcemanager", "version": "v2beta1", "title": "Cloud Resource Manager API", "description": "Creates, reads, and updates metadata for Google Cloud Platform resource containers.", "discoveryRestUrl": "https://cloudresourcemanager.googleapis.com/$discovery/rest?version=v2beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/resource-manager", "preferred": false}, {"kind": "discovery#directoryItem", "id": "cloudresourcemanager:v1", "name": "cloudresourcemanager", "version": "v1", "title": "Cloud Resource Manager API", "description": "Creates, reads, and updates metadata for Google Cloud Platform resource containers.", "discoveryRestUrl": "https://cloudresourcemanager.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/resource-manager", "preferred": false}, {"kind": "discovery#directoryItem", "id": "cloudresourcemanager:v2", "name": "cloudresourcemanager", "version": "v2", "title": "Cloud Resource Manager API", "description": "Creates, reads, and updates metadata for Google Cloud Platform resource containers.", "discoveryRestUrl": "https://cloudresourcemanager.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/resource-manager", "preferred": true}, {"kind": "discovery#directoryItem", "id": "cloudscheduler:v1beta1", "name": "cloudscheduler", "version": "v1beta1", "title": "Cloud Scheduler API", "description": "Creates and manages jobs run on a regular recurring schedule.", "discoveryRestUrl": "https://cloudscheduler.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/scheduler/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "cloudscheduler:v1", "name": "cloudscheduler", "version": "v1", "title": "Cloud Scheduler API", "description": "Creates and manages jobs run on a regular recurring schedule.", "discoveryRestUrl": "https://cloudscheduler.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/scheduler/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "cloudsearch:v1", "name": "cloudsearch", "version": "v1", "title": "Cloud Search API", "description": "Cloud Search provides cloud-based search capabilities over G Suite data. The Cloud Search API allows indexing of non-G Suite data into Cloud Search.", "discoveryRestUrl": "https://cloudsearch.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/cloud-search/docs/guides/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "cloudshell:v1alpha1", "name": "cloudshell", "version": "v1alpha1", "title": "Cloud Shell API", "description": "Allows users to start, configure, and connect to interactive shell sessions running in the cloud.", "discoveryRestUrl": "https://cloudshell.googleapis.com/$discovery/rest?version=v1alpha1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/shell/docs/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "cloudshell:v1", "name": "cloudshell", "version": "v1", "title": "Cloud Shell API", "description": "Allows users to start, configure, and connect to interactive shell sessions running in the cloud.", "discoveryRestUrl": "https://cloudshell.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/shell/docs/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "cloudtasks:v2beta2", "name": "cloudtasks", "version": "v2beta2", "title": "Cloud Tasks API", "description": "Manages the execution of large numbers of distributed requests.", "discoveryRestUrl": "https://cloudtasks.googleapis.com/$discovery/rest?version=v2beta2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/tasks/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "cloudtasks:v2beta3", "name": "cloudtasks", "version": "v2beta3", "title": "Cloud Tasks API", "description": "Manages the execution of large numbers of distributed requests.", "discoveryRestUrl": "https://cloudtasks.googleapis.com/$discovery/rest?version=v2beta3", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/tasks/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "cloudtasks:v2", "name": "cloudtasks", "version": "v2", "title": "Cloud Tasks API", "description": "Manages the execution of large numbers of distributed requests.", "discoveryRestUrl": "https://cloudtasks.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/tasks/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "cloudtrace:v2beta1", "name": "cloudtrace", "version": "v2beta1", "title": "Cloud Trace API", "description": "Sends application trace data to Cloud Trace for viewing. Trace data is collected for all App Engine applications by default. Trace data from other applications can be provided using this API. This library is used to interact with the Cloud Trace API directly. If you are looking to instrument your application for Cloud Trace, we recommend using OpenCensus.", "discoveryRestUrl": "https://cloudtrace.googleapis.com/$discovery/rest?version=v2beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/trace", "preferred": false}, {"kind": "discovery#directoryItem", "id": "cloudtrace:v1", "name": "cloudtrace", "version": "v1", "title": "Cloud Trace API", "description": "Sends application trace data to Cloud Trace for viewing. Trace data is collected for all App Engine applications by default. Trace data from other applications can be provided using this API. This library is used to interact with the Cloud Trace API directly. If you are looking to instrument your application for Cloud Trace, we recommend using OpenCensus.", "discoveryRestUrl": "https://cloudtrace.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/trace", "preferred": false}, {"kind": "discovery#directoryItem", "id": "cloudtrace:v2", "name": "cloudtrace", "version": "v2", "title": "Cloud Trace API", "description": "Sends application trace data to Cloud Trace for viewing. Trace data is collected for all App Engine applications by default. Trace data from other applications can be provided using this API. This library is used to interact with the Cloud Trace API directly. If you are looking to instrument your application for Cloud Trace, we recommend using OpenCensus.", "discoveryRestUrl": "https://cloudtrace.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/trace", "preferred": true}, {"kind": "discovery#directoryItem", "id": "composer:v1beta1", "name": "composer", "version": "v1beta1", "title": "Cloud Composer API", "description": "Manages Apache Airflow environments on Google Cloud Platform.", "discoveryRestUrl": "https://composer.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/composer/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "composer:v1", "name": "composer", "version": "v1", "title": "Cloud Composer API", "description": "Manages Apache Airflow environments on Google Cloud Platform.", "discoveryRestUrl": "https://composer.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/composer/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "compute:alpha", "name": "compute", "version": "alpha", "title": "Compute Engine API", "description": "Creates and runs virtual machines on Google Cloud Platform.", "discoveryRestUrl": "https://www.googleapis.com/discovery/v1/apis/compute/alpha/rest", "discoveryLink": "./apis/compute/alpha/rest", "icons": {"x16": "https://www.google.com/images/icons/product/compute_engine-16.png", "x32": "https://www.google.com/images/icons/product/compute_engine-32.png"}, "documentationLink": "https://developers.google.com/compute/docs/reference/latest/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "compute:beta", "name": "compute", "version": "beta", "title": "Compute Engine API", "description": "Creates and runs virtual machines on Google Cloud Platform.", "discoveryRestUrl": "https://www.googleapis.com/discovery/v1/apis/compute/beta/rest", "discoveryLink": "./apis/compute/beta/rest", "icons": {"x16": "https://www.google.com/images/icons/product/compute_engine-16.png", "x32": "https://www.google.com/images/icons/product/compute_engine-32.png"}, "documentationLink": "https://developers.google.com/compute/docs/reference/latest/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "compute:v1", "name": "compute", "version": "v1", "title": "Compute Engine API", "description": "Creates and runs virtual machines on Google Cloud Platform.", "discoveryRestUrl": "https://www.googleapis.com/discovery/v1/apis/compute/v1/rest", "discoveryLink": "./apis/compute/v1/rest", "icons": {"x16": "https://www.google.com/images/icons/product/compute_engine-16.png", "x32": "https://www.google.com/images/icons/product/compute_engine-32.png"}, "documentationLink": "https://developers.google.com/compute/docs/reference/latest/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "container:v1beta1", "name": "container", "version": "v1beta1", "title": "Kubernetes Engine API", "description": "Builds and manages container-based applications, powered by the open source Kubernetes technology.", "discoveryRestUrl": "https://container.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/container-engine/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "container:v1", "name": "container", "version": "v1", "title": "Kubernetes Engine API", "description": "Builds and manages container-based applications, powered by the open source Kubernetes technology.", "discoveryRestUrl": "https://container.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/container-engine/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "containeranalysis:v1alpha1", "name": "containeranalysis", "version": "v1alpha1", "title": "Container Analysis API", "description": "An implementation of the Grafeas API, which stores, and enables querying and retrieval of critical metadata about all of your software artifacts.", "discoveryRestUrl": "https://containeranalysis.googleapis.com/$discovery/rest?version=v1alpha1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/container-analysis/api/reference/rest/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "containeranalysis:v1beta1", "name": "containeranalysis", "version": "v1beta1", "title": "Container Analysis API", "description": "An implementation of the Grafeas API, which stores, and enables querying and retrieval of critical metadata about all of your software artifacts.", "discoveryRestUrl": "https://containeranalysis.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/container-analysis/api/reference/rest/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "content:v2", "name": "content", "version": "v2", "title": "Content API for Shopping", "description": "Manage your product listings and accounts for Google Shopping", "discoveryRestUrl": "https://shoppingcontent.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/shopping-content/v2/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "content:v2.1", "name": "content", "version": "v2.1", "title": "Content API for Shopping", "description": "Manage your product listings and accounts for Google Shopping", "discoveryRestUrl": "https://shoppingcontent.googleapis.com/$discovery/rest?version=v2.1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/shopping-content/v2/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "customsearch:v1", "name": "customsearch", "version": "v1", "title": "Custom Search API", "description": "Searches over a website or collection of websites", "discoveryRestUrl": "https://customsearch.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/custom-search/v1/introduction", "preferred": true}, {"kind": "discovery#directoryItem", "id": "datacatalog:v1beta1", "name": "datacatalog", "version": "v1beta1", "title": "Google Cloud Data Catalog API", "description": "A fully managed and highly scalable data discovery and metadata management service.", "discoveryRestUrl": "https://datacatalog.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/data-catalog/docs/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "dataflow:v1b3", "name": "dataflow", "version": "v1b3", "title": "Dataflow API", "description": "Manages Google Cloud Dataflow projects on Google Cloud Platform.", "discoveryRestUrl": "https://dataflow.googleapis.com/$discovery/rest?version=v1b3", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/dataflow", "preferred": true}, {"kind": "discovery#directoryItem", "id": "datafusion:v1beta1", "name": "datafusion", "version": "v1beta1", "title": "Cloud Data Fusion API", "description": "Cloud Data Fusion is a fully-managed, cloud native, enterprise data integration service for quickly building and managing data pipelines. It provides a graphical interface to increase time efficiency and reduce complexity, and allows business users, developers, and data scientists to easily and reliably build scalable data integration solutions to cleanse, prepare, blend, transfer and transform data without having to wrestle with infrastructure.", "discoveryRestUrl": "https://datafusion.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/data-fusion/docs", "preferred": false}, {"kind": "discovery#directoryItem", "id": "datafusion:v1", "name": "datafusion", "version": "v1", "title": "Cloud Data Fusion API", "description": "Cloud Data Fusion is a fully-managed, cloud native, enterprise data integration service for quickly building and managing data pipelines. It provides a graphical interface to increase time efficiency and reduce complexity, and allows business users, developers, and data scientists to easily and reliably build scalable data integration solutions to cleanse, prepare, blend, transfer and transform data without having to wrestle with infrastructure.", "discoveryRestUrl": "https://datafusion.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/data-fusion/docs", "preferred": true}, {"kind": "discovery#directoryItem", "id": "datalabeling:v1beta1", "name": "datalabeling", "version": "v1beta1", "title": "Data Labeling API", "description": "Public API for Google Cloud AI Data Labeling Service.", "discoveryRestUrl": "https://datalabeling.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/data-labeling/docs/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "datamigration:v1beta1", "name": "datamigration", "version": "v1beta1", "title": "Database Migration API", "description": "Manage Cloud Database Migration Service resources on Google Cloud Platform.", "discoveryRestUrl": "https://datamigration.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/datamigration/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "dataproc:v1beta2", "name": "dataproc", "version": "v1beta2", "title": "Cloud Dataproc API", "description": "Manages Hadoop-based clusters and jobs on Google Cloud Platform.", "discoveryRestUrl": "https://dataproc.googleapis.com/$discovery/rest?version=v1beta2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/dataproc/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "dataproc:v1", "name": "dataproc", "version": "v1", "title": "Cloud Dataproc API", "description": "Manages Hadoop-based clusters and jobs on Google Cloud Platform.", "discoveryRestUrl": "https://dataproc.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/dataproc/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "datastore:v1beta1", "name": "datastore", "version": "v1beta1", "title": "Cloud Datastore API", "description": "Accesses the schemaless NoSQL database to provide fully managed, robust, scalable storage for your application.", "discoveryRestUrl": "https://datastore.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/datastore/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "datastore:v1beta3", "name": "datastore", "version": "v1beta3", "title": "Cloud Datastore API", "description": "Accesses the schemaless NoSQL database to provide fully managed, robust, scalable storage for your application.", "discoveryRestUrl": "https://datastore.googleapis.com/$discovery/rest?version=v1beta3", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/datastore/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "datastore:v1", "name": "datastore", "version": "v1", "title": "Cloud Datastore API", "description": "Accesses the schemaless NoSQL database to provide fully managed, robust, scalable storage for your application.", "discoveryRestUrl": "https://datastore.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/datastore/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "deploymentmanager:alpha", "name": "deploymentmanager", "version": "alpha", "title": "Cloud Deployment Manager V2 API", "description": "The Google Cloud Deployment Manager v2 API provides services for configuring, deploying, and viewing Google Cloud services and APIs via templates which specify deployments of Cloud resources.", "discoveryRestUrl": "https://deploymentmanager.googleapis.com/$discovery/rest?version=alpha", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/deployment-manager", "preferred": false}, {"kind": "discovery#directoryItem", "id": "deploymentmanager:v2beta", "name": "deploymentmanager", "version": "v2beta", "title": "Cloud Deployment Manager V2 API", "description": "The Google Cloud Deployment Manager v2 API provides services for configuring, deploying, and viewing Google Cloud services and APIs via templates which specify deployments of Cloud resources.", "discoveryRestUrl": "https://deploymentmanager.googleapis.com/$discovery/rest?version=v2beta", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/deployment-manager", "preferred": false}, {"kind": "discovery#directoryItem", "id": "deploymentmanager:v2", "name": "deploymentmanager", "version": "v2", "title": "Cloud Deployment Manager V2 API", "description": "The Google Cloud Deployment Manager v2 API provides services for configuring, deploying, and viewing Google Cloud services and APIs via templates which specify deployments of Cloud resources.", "discoveryRestUrl": "https://deploymentmanager.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/deployment-manager", "preferred": true}, {"kind": "discovery#directoryItem", "id": "dfareporting:v3.3", "name": "dfareporting", "version": "v3.3", "title": "Campaign Manager 360 API", "description": "Manage your DoubleClick Campaign Manager ad campaigns and reports.", "discoveryRestUrl": "https://dfareporting.googleapis.com/$discovery/rest?version=v3.3", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/doubleclick-advertisers/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "dfareporting:v3.4", "name": "dfareporting", "version": "v3.4", "title": "Campaign Manager 360 API", "description": "Manage your DoubleClick Campaign Manager ad campaigns and reports.", "discoveryRestUrl": "https://dfareporting.googleapis.com/$discovery/rest?version=v3.4", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/doubleclick-advertisers/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "dialogflow:v2beta1", "name": "dialogflow", "version": "v2beta1", "title": "Dialogflow API", "description": "Builds conversational interfaces (for example, chatbots, and voice-powered apps and devices).", "discoveryRestUrl": "https://dialogflow.googleapis.com/$discovery/rest?version=v2beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/dialogflow/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "dialogflow:v3beta1", "name": "dialogflow", "version": "v3beta1", "title": "Dialogflow API", "description": "Builds conversational interfaces (for example, chatbots, and voice-powered apps and devices).", "discoveryRestUrl": "https://dialogflow.googleapis.com/$discovery/rest?version=v3beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/dialogflow/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "dialogflow:v2", "name": "dialogflow", "version": "v2", "title": "Dialogflow API", "description": "Builds conversational interfaces (for example, chatbots, and voice-powered apps and devices).", "discoveryRestUrl": "https://dialogflow.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/dialogflow/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "digitalassetlinks:v1", "name": "digitalassetlinks", "version": "v1", "title": "Digital Asset Links API", "description": "Discovers relationships between online assets such as websites or mobile apps.", "discoveryRestUrl": "https://digitalassetlinks.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/digital-asset-links/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "discovery:v1", "name": "discovery", "version": "v1", "title": "API Discovery Service", "description": "Google API Discovery Service allows service consumers to list the discovery metadata of all public APIs managed by the API Platform.", "discoveryRestUrl": "https://discovery.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/discovery", "preferred": true}, {"kind": "discovery#directoryItem", "id": "displayvideo:v1", "name": "displayvideo", "version": "v1", "title": "Display & Video 360 API", "description": "Display & Video 360 API allows users to manage and create campaigns and reports.", "discoveryRestUrl": "https://displayvideo.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/display-video/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "dlp:v2", "name": "dlp", "version": "v2", "title": "Cloud Data Loss Prevention (DLP) API", "description": "Provides methods for detection, risk analysis, and de-identification of privacy-sensitive fragments in text, images, and Google Cloud Platform storage repositories.", "discoveryRestUrl": "https://dlp.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/dlp/docs/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "dns:v1beta2", "name": "dns", "version": "v1beta2", "title": "Cloud DNS API", "description": "", "discoveryRestUrl": "https://dns.googleapis.com/$discovery/rest?version=v1beta2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/dns/docs", "preferred": false}, {"kind": "discovery#directoryItem", "id": "dns:v1", "name": "dns", "version": "v1", "title": "Cloud DNS API", "description": "", "discoveryRestUrl": "https://dns.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/dns/docs", "preferred": true}, {"kind": "discovery#directoryItem", "id": "docs:v1", "name": "docs", "version": "v1", "title": "Google Docs API", "description": "Reads and writes Google Docs documents.", "discoveryRestUrl": "https://docs.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/docs/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "documentai:v1beta2", "name": "documentai", "version": "v1beta2", "title": "Cloud Document AI API", "description": "Service to parse structured information from unstructured or semi-structured documents using state-of-the-art Google AI such as natural language, computer vision, translation, and AutoML.", "discoveryRestUrl": "https://documentai.googleapis.com/$discovery/rest?version=v1beta2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/document-ai/docs/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "documentai:v1beta3", "name": "documentai", "version": "v1beta3", "title": "Cloud Document AI API", "description": "Service to parse structured information from unstructured or semi-structured documents using state-of-the-art Google AI such as natural language, computer vision, translation, and AutoML.", "discoveryRestUrl": "https://documentai.googleapis.com/$discovery/rest?version=v1beta3", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/document-ai/docs/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "domains:v1alpha2", "name": "domains", "version": "v1alpha2", "title": "Cloud Domains API", "description": "Enables management and configuration of domain names.", "discoveryRestUrl": "https://domains.googleapis.com/$discovery/rest?version=v1alpha2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/domains/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "domains:v1beta1", "name": "domains", "version": "v1beta1", "title": "Cloud Domains API", "description": "Enables management and configuration of domain names.", "discoveryRestUrl": "https://domains.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/domains/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "domainsrdap:v1", "name": "domainsrdap", "version": "v1", "title": "Domains RDAP API", "description": "Read-only public API that lets users search for information about domain names.", "discoveryRestUrl": "https://domainsrdap.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/domains/rdap/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "doubleclickbidmanager:v1", "name": "doubleclickbidmanager", "version": "v1", "title": "DoubleClick Bid Manager API", "description": "DoubleClick Bid Manager API allows users to manage and create campaigns and reports.", "discoveryRestUrl": "https://doubleclickbidmanager.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/bid-manager/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "doubleclickbidmanager:v1.1", "name": "doubleclickbidmanager", "version": "v1.1", "title": "DoubleClick Bid Manager API", "description": "DoubleClick Bid Manager API allows users to manage and create campaigns and reports.", "discoveryRestUrl": "https://doubleclickbidmanager.googleapis.com/$discovery/rest?version=v1.1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/bid-manager/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "doubleclicksearch:v2", "name": "doubleclicksearch", "version": "v2", "title": "Search Ads 360 API", "description": "The Search Ads 360 API allows developers to automate uploading conversions and downloading reports from Search Ads 360.", "discoveryRestUrl": "https://doubleclicksearch.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/search-ads", "preferred": true}, {"kind": "discovery#directoryItem", "id": "drive:v2", "name": "drive", "version": "v2", "title": "Drive API", "description": "Manages files in Drive including uploading, downloading, searching, detecting changes, and updating sharing permissions.", "discoveryRestUrl": "https://www.googleapis.com/discovery/v1/apis/drive/v2/rest", "discoveryLink": "./apis/drive/v2/rest", "icons": {"x16": "https://ssl.gstatic.com/docs/doclist/images/drive_icon_16.png", "x32": "https://ssl.gstatic.com/docs/doclist/images/drive_icon_32.png"}, "documentationLink": "https://developers.google.com/drive/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "drive:v3", "name": "drive", "version": "v3", "title": "Drive API", "description": "Manages files in Drive including uploading, downloading, searching, detecting changes, and updating sharing permissions.", "discoveryRestUrl": "https://www.googleapis.com/discovery/v1/apis/drive/v3/rest", "discoveryLink": "./apis/drive/v3/rest", "icons": {"x16": "https://ssl.gstatic.com/docs/doclist/images/drive_icon_16.png", "x32": "https://ssl.gstatic.com/docs/doclist/images/drive_icon_32.png"}, "documentationLink": "https://developers.google.com/drive/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "driveactivity:v2", "name": "driveactivity", "version": "v2", "title": "Drive Activity API", "description": "Provides a historical view of activity in Google Drive.", "discoveryRestUrl": "https://driveactivity.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/drive/activity/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "eventarc:v1beta1", "name": "eventarc", "version": "v1beta1", "title": "Eventarc API", "description": "", "discoveryRestUrl": "https://eventarc.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/eventarc", "preferred": true}, {"kind": "discovery#directoryItem", "id": "factchecktools:v1alpha1", "name": "factchecktools", "version": "v1alpha1", "title": "Fact Check Tools API", "description": "", "discoveryRestUrl": "https://factchecktools.googleapis.com/$discovery/rest?version=v1alpha1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/fact-check/tools/api/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "fcm:v1", "name": "fcm", "version": "v1", "title": "Firebase Cloud Messaging API", "description": "FCM send API that provides a cross-platform messaging solution to reliably deliver messages at no cost.", "discoveryRestUrl": "https://fcm.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://firebase.google.com/docs/cloud-messaging", "preferred": true}, {"kind": "discovery#directoryItem", "id": "file:v1beta1", "name": "file", "version": "v1beta1", "title": "Cloud Filestore API", "description": "The Cloud Filestore API is used for creating and managing cloud file servers.", "discoveryRestUrl": "https://file.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/filestore/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "file:v1", "name": "file", "version": "v1", "title": "Cloud Filestore API", "description": "The Cloud Filestore API is used for creating and managing cloud file servers.", "discoveryRestUrl": "https://file.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/filestore/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "firebase:v1beta1", "name": "firebase", "version": "v1beta1", "title": "Firebase Management API", "description": "The Firebase Management API enables programmatic setup and management of Firebase projects, including a project's Firebase resources and Firebase apps.", "discoveryRestUrl": "https://firebase.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://firebase.google.com", "preferred": true}, {"kind": "discovery#directoryItem", "id": "firebasedatabase:v1beta", "name": "firebasedatabase", "version": "v1beta", "title": "Firebase Realtime Database Management API", "description": "The Firebase Realtime Database Management API enables programmatic provisioning and management of Realtime Database instances.", "discoveryRestUrl": "https://firebasedatabase.googleapis.com/$discovery/rest?version=v1beta", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://firebase.google.com/docs/reference/rest/database/database-management/rest/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "firebasedynamiclinks:v1", "name": "firebasedynamiclinks", "version": "v1", "title": "Firebase Dynamic Links API", "description": "Programmatically creates and manages Firebase Dynamic Links.", "discoveryRestUrl": "https://firebasedynamiclinks.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://firebase.google.com/docs/dynamic-links/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "firebasehosting:v1beta1", "name": "firebasehosting", "version": "v1beta1", "title": "Firebase Hosting API", "description": "The Firebase Hosting REST API enables programmatic and customizable deployments to your Firebase-hosted sites. Use this REST API to deploy new or updated hosting configurations and content files.", "discoveryRestUrl": "https://firebasehosting.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://firebase.google.com/docs/hosting/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "firebasehosting:v1", "name": "firebasehosting", "version": "v1", "title": "Firebase Hosting API", "description": "The Firebase Hosting REST API enables programmatic and customizable deployments to your Firebase-hosted sites. Use this REST API to deploy new or updated hosting configurations and content files.", "discoveryRestUrl": "https://firebasehosting.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://firebase.google.com/docs/hosting/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "firebaseml:v1beta2", "name": "firebaseml", "version": "v1beta2", "title": "Firebase ML API", "description": "Access custom machine learning models hosted via Firebase ML.", "discoveryRestUrl": "https://firebaseml.googleapis.com/$discovery/rest?version=v1beta2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://firebase.google.com", "preferred": false}, {"kind": "discovery#directoryItem", "id": "firebaseml:v1", "name": "firebaseml", "version": "v1", "title": "Firebase ML API", "description": "Access custom machine learning models hosted via Firebase ML.", "discoveryRestUrl": "https://firebaseml.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://firebase.google.com", "preferred": true}, {"kind": "discovery#directoryItem", "id": "firebaserules:v1", "name": "firebase<PERSON>les", "version": "v1", "title": "Firebase Rules API", "description": "Creates and manages rules that determine when a Firebase Rules-enabled service should permit a request.", "discoveryRestUrl": "https://firebaserules.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://firebase.google.com/docs/storage/security", "preferred": true}, {"kind": "discovery#directoryItem", "id": "firestore:v1beta1", "name": "firestore", "version": "v1beta1", "title": "Cloud Firestore API", "description": "Accesses the NoSQL document database built for automatic scaling, high performance, and ease of application development.", "discoveryRestUrl": "https://firestore.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/firestore", "preferred": false}, {"kind": "discovery#directoryItem", "id": "firestore:v1beta2", "name": "firestore", "version": "v1beta2", "title": "Cloud Firestore API", "description": "Accesses the NoSQL document database built for automatic scaling, high performance, and ease of application development.", "discoveryRestUrl": "https://firestore.googleapis.com/$discovery/rest?version=v1beta2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/firestore", "preferred": false}, {"kind": "discovery#directoryItem", "id": "firestore:v1", "name": "firestore", "version": "v1", "title": "Cloud Firestore API", "description": "Accesses the NoSQL document database built for automatic scaling, high performance, and ease of application development.", "discoveryRestUrl": "https://firestore.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/firestore", "preferred": true}, {"kind": "discovery#directoryItem", "id": "fitness:v1", "name": "fitness", "version": "v1", "title": "Fitness API", "description": "The Fitness API for managing users' fitness tracking data.", "discoveryRestUrl": "https://fitness.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/fit/rest/v1/get-started", "preferred": true}, {"kind": "discovery#directoryItem", "id": "games:v1", "name": "games", "version": "v1", "title": "Google Play Game Services", "description": "The Google Play games service allows developers to enhance games with social leaderboards, achievements, game state, sign-in with Google, and more.", "discoveryRestUrl": "https://games.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/games/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "gamesConfiguration:v1configuration", "name": "gamesConfiguration", "version": "v1configuration", "title": "Google Play Game Services Publishing API", "description": "The Google Play Game Services Publishing API allows developers to configure their games in Game Services.", "discoveryRestUrl": "https://gamesconfiguration.googleapis.com/$discovery/rest?version=v1configuration", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/games/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "gamesManagement:v1management", "name": "gamesManagement", "version": "v1management", "title": "Google Play Game Management", "description": "The Google Play Game Management API allows developers to manage resources from the Google Play Game service.", "discoveryRestUrl": "https://gamesmanagement.googleapis.com/$discovery/rest?version=v1management", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/games/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "gameservices:v1beta", "name": "gameservices", "version": "v1beta", "title": "Game Services API", "description": "Deploy and manage infrastructure for global multiplayer gaming experiences.", "discoveryRestUrl": "https://gameservices.googleapis.com/$discovery/rest?version=v1beta", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/solutions/gaming/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "gameservices:v1", "name": "gameservices", "version": "v1", "title": "Game Services API", "description": "Deploy and manage infrastructure for global multiplayer gaming experiences.", "discoveryRestUrl": "https://gameservices.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/solutions/gaming/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "genomics:v1alpha2", "name": "genomics", "version": "v1alpha2", "title": "Genomics API", "description": "Uploads, processes, queries, and searches Genomics data in the cloud.", "discoveryRestUrl": "https://genomics.googleapis.com/$discovery/rest?version=v1alpha2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/genomics", "preferred": false}, {"kind": "discovery#directoryItem", "id": "genomics:v2alpha1", "name": "genomics", "version": "v2alpha1", "title": "Genomics API", "description": "Uploads, processes, queries, and searches Genomics data in the cloud.", "discoveryRestUrl": "https://genomics.googleapis.com/$discovery/rest?version=v2alpha1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/genomics", "preferred": false}, {"kind": "discovery#directoryItem", "id": "genomics:v1", "name": "genomics", "version": "v1", "title": "Genomics API", "description": "Uploads, processes, queries, and searches Genomics data in the cloud.", "discoveryRestUrl": "https://genomics.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/genomics", "preferred": true}, {"kind": "discovery#directoryItem", "id": "gmail:v1", "name": "gmail", "version": "v1", "title": "Gmail API", "description": "The Gmail API lets you view and manage Gmail mailbox data like threads, messages, and labels.", "discoveryRestUrl": "https://gmail.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/gmail/api/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "gmailpostmastertools:v1beta1", "name": "gmailpostmastertools", "version": "v1beta1", "title": "Gmail Postmaster Tools API", "description": "The Postmaster Tools API is a RESTful API that provides programmatic access to email traffic metrics (like spam reports, delivery errors etc) otherwise available through the Gmail Postmaster Tools UI currently.", "discoveryRestUrl": "https://gmailpostmastertools.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/gmail/postmaster", "preferred": true}, {"kind": "discovery#directoryItem", "id": "groupsmigration:v1", "name": "groupsmigration", "version": "v1", "title": "Groups Migration API", "description": "The Groups Migration API allows domain administrators to archive emails into Google groups.", "discoveryRestUrl": "https://groupsmigration.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/google-apps/groups-migration/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "groupssettings:v1", "name": "groupssettings", "version": "v1", "title": "Groups Settings API", "description": "The Groups Settings API allows domain administrators to view and manage access levels and advanced settings for a group.", "discoveryRestUrl": "https://groupssettings.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/admin-sdk/groups-settings", "preferred": true}, {"kind": "discovery#directoryItem", "id": "healthcare:v1beta1", "name": "healthcare", "version": "v1beta1", "title": "Cloud Healthcare API", "description": "Manage, store, and access healthcare data in Google Cloud Platform.", "discoveryRestUrl": "https://healthcare.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/healthcare", "preferred": false}, {"kind": "discovery#directoryItem", "id": "healthcare:v1", "name": "healthcare", "version": "v1", "title": "Cloud Healthcare API", "description": "Manage, store, and access healthcare data in Google Cloud Platform.", "discoveryRestUrl": "https://healthcare.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/healthcare", "preferred": true}, {"kind": "discovery#directoryItem", "id": "homegraph:v1", "name": "homegraph", "version": "v1", "title": "HomeGraph API", "description": "", "discoveryRestUrl": "https://homegraph.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/actions/smarthome/create-app#request-sync", "preferred": true}, {"kind": "discovery#directoryItem", "id": "iam:v1", "name": "iam", "version": "v1", "title": "Identity and Access Management (IAM) API", "description": "Manages identity and access control for Google Cloud Platform resources, including the creation of service accounts, which you can use to authenticate to Google and make API calls.", "discoveryRestUrl": "https://iam.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/iam/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "iamcredentials:v1", "name": "iamcredentials", "version": "v1", "title": "IAM Service Account Credentials API", "description": "Creates short-lived credentials for impersonating IAM service accounts. To enable this API, you must enable the IAM API (iam.googleapis.com).", "discoveryRestUrl": "https://iamcredentials.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/iam/docs/creating-short-lived-service-account-credentials", "preferred": true}, {"kind": "discovery#directoryItem", "id": "iap:v1beta1", "name": "iap", "version": "v1beta1", "title": "Cloud Identity-Aware Proxy API", "description": "Controls access to cloud applications running on Google Cloud Platform.", "discoveryRestUrl": "https://iap.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/iap", "preferred": false}, {"kind": "discovery#directoryItem", "id": "iap:v1", "name": "iap", "version": "v1", "title": "Cloud Identity-Aware Proxy API", "description": "Controls access to cloud applications running on Google Cloud Platform.", "discoveryRestUrl": "https://iap.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/iap", "preferred": true}, {"kind": "discovery#directoryItem", "id": "identitytoolkit:v3", "name": "identitytoolkit", "version": "v3", "title": "Identity Toolkit API", "description": "The Google Identity Toolkit API lets you use open standards to verify a user's identity.", "discoveryRestUrl": "https://identitytoolkit.googleapis.com/$discovery/rest?version=v3", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://firebase.google.com/docs/auth/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "indexing:v3", "name": "indexing", "version": "v3", "title": "Indexing API", "description": "Notifies Google when your web pages change.", "discoveryRestUrl": "https://indexing.googleapis.com/$discovery/rest?version=v3", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/search/apis/indexing-api/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "jobs:v3p1beta1", "name": "jobs", "version": "v3p1beta1", "title": "Cloud Talent Solution API", "description": "Cloud Talent Solution provides the capability to create, read, update, and delete job postings, as well as search jobs based on keywords and filters.", "discoveryRestUrl": "https://jobs.googleapis.com/$discovery/rest?version=v3p1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/talent-solution/job-search/docs/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "jobs:v2", "name": "jobs", "version": "v2", "title": "Cloud Talent Solution API", "description": "Cloud Talent Solution provides the capability to create, read, update, and delete job postings, as well as search jobs based on keywords and filters.", "discoveryRestUrl": "https://jobs.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/talent-solution/job-search/docs/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "jobs:v3", "name": "jobs", "version": "v3", "title": "Cloud Talent Solution API", "description": "Cloud Talent Solution provides the capability to create, read, update, and delete job postings, as well as search jobs based on keywords and filters.", "discoveryRestUrl": "https://jobs.googleapis.com/$discovery/rest?version=v3", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/talent-solution/job-search/docs/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "kgsearch:v1", "name": "kgsearch", "version": "v1", "title": "Knowledge Graph Search API", "description": "Searches the Google Knowledge Graph for entities.", "discoveryRestUrl": "https://kgsearch.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/knowledge-graph/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "language:v1beta1", "name": "language", "version": "v1beta1", "title": "Cloud Natural Language API", "description": "Provides natural language understanding technologies, such as sentiment analysis, entity recognition, entity sentiment analysis, and other text annotations, to developers.", "discoveryRestUrl": "https://language.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/natural-language/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "language:v1beta2", "name": "language", "version": "v1beta2", "title": "Cloud Natural Language API", "description": "Provides natural language understanding technologies, such as sentiment analysis, entity recognition, entity sentiment analysis, and other text annotations, to developers.", "discoveryRestUrl": "https://language.googleapis.com/$discovery/rest?version=v1beta2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/natural-language/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "language:v1", "name": "language", "version": "v1", "title": "Cloud Natural Language API", "description": "Provides natural language understanding technologies, such as sentiment analysis, entity recognition, entity sentiment analysis, and other text annotations, to developers.", "discoveryRestUrl": "https://language.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/natural-language/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "libraryagent:v1", "name": "libraryagent", "version": "v1", "title": "Library Agent API", "description": "A simple Google Example Library API.", "discoveryRestUrl": "https://libraryagent.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/docs/quota", "preferred": true}, {"kind": "discovery#directoryItem", "id": "licensing:v1", "name": "licensing", "version": "v1", "title": "Enterprise License Manager API", "description": "The Google Enterprise License Manager API's allows you to license apps for all the users of a domain managed by you.", "discoveryRestUrl": "https://licensing.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/admin-sdk/licensing/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "lifesciences:v2beta", "name": "lifesciences", "version": "v2beta", "title": "Cloud Life Sciences API", "description": "Cloud Life Sciences is a suite of services and tools for managing, processing, and transforming life sciences data.", "discoveryRestUrl": "https://lifesciences.googleapis.com/$discovery/rest?version=v2beta", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/life-sciences", "preferred": true}, {"kind": "discovery#directoryItem", "id": "localservices:v1", "name": "localservices", "version": "v1", "title": "Local Services API", "description": "", "discoveryRestUrl": "https://localservices.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://ads.google.com/local-services-ads/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "logging:v2", "name": "logging", "version": "v2", "title": "Cloud Logging API", "description": "Writes log entries and manages your Cloud Logging configuration. The table entries below are presented in alphabetical order, not in order of common use. For explanations of the concepts found in the table entries, read the documentation at https://cloud.google.com/logging/docs.", "discoveryRestUrl": "https://logging.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/logging/docs/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "managedidentities:v1alpha1", "name": "managedidentities", "version": "v1alpha1", "title": "Managed Service for Microsoft Active Directory API", "description": "The Managed Service for Microsoft Active Directory API is used for managing a highly available, hardened service running Microsoft Active Directory (AD).", "discoveryRestUrl": "https://managedidentities.googleapis.com/$discovery/rest?version=v1alpha1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/managed-microsoft-ad/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "managedidentities:v1beta1", "name": "managedidentities", "version": "v1beta1", "title": "Managed Service for Microsoft Active Directory API", "description": "The Managed Service for Microsoft Active Directory API is used for managing a highly available, hardened service running Microsoft Active Directory (AD).", "discoveryRestUrl": "https://managedidentities.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/managed-microsoft-ad/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "managedidentities:v1", "name": "managedidentities", "version": "v1", "title": "Managed Service for Microsoft Active Directory API", "description": "The Managed Service for Microsoft Active Directory API is used for managing a highly available, hardened service running Microsoft Active Directory (AD).", "discoveryRestUrl": "https://managedidentities.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/managed-microsoft-ad/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "manufacturers:v1", "name": "manufacturers", "version": "v1", "title": "Manufacturer Center API", "description": "Public API for managing Manufacturer Center related data.", "discoveryRestUrl": "https://manufacturers.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/manufacturers/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "memcache:v1beta2", "name": "memcache", "version": "v1beta2", "title": "Cloud Memorystore for Memcached API", "description": "Google Cloud Memorystore for Memcached API is used for creating and managing Memcached instances in GCP.", "discoveryRestUrl": "https://memcache.googleapis.com/$discovery/rest?version=v1beta2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/memorystore/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "ml:v1", "name": "ml", "version": "v1", "title": "AI Platform Training & Prediction API", "description": "An API to enable creating and using machine learning models.", "discoveryRestUrl": "https://ml.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/ml/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "monitoring:v1", "name": "monitoring", "version": "v1", "title": "Cloud Monitoring API", "description": "Manages your Cloud Monitoring data and configurations. Most projects must be associated with a Workspace, with a few exceptions as noted on the individual method pages. The table entries below are presented in alphabetical order, not in order of common use. For explanations of the concepts found in the table entries, read the [Cloud Monitoring documentation](/monitoring/docs).", "discoveryRestUrl": "https://monitoring.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/monitoring/api/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "monitoring:v3", "name": "monitoring", "version": "v3", "title": "Cloud Monitoring API", "description": "Manages your Cloud Monitoring data and configurations. Most projects must be associated with a Workspace, with a few exceptions as noted on the individual method pages. The table entries below are presented in alphabetical order, not in order of common use. For explanations of the concepts found in the table entries, read the [Cloud Monitoring documentation](/monitoring/docs).", "discoveryRestUrl": "https://monitoring.googleapis.com/$discovery/rest?version=v3", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/monitoring/api/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "networkmanagement:v1beta1", "name": "networkmanagement", "version": "v1beta1", "title": "Network Management API", "description": "The Network Management API provides a collection of network performance monitoring and diagnostic capabilities.", "discoveryRestUrl": "https://networkmanagement.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "networkmanagement:v1", "name": "networkmanagement", "version": "v1", "title": "Network Management API", "description": "The Network Management API provides a collection of network performance monitoring and diagnostic capabilities.", "discoveryRestUrl": "https://networkmanagement.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "notebooks:v1", "name": "notebooks", "version": "v1", "title": "Notebooks API", "description": "AI Platform Notebooks API is used to manage notebook resources in Google Cloud.", "discoveryRestUrl": "https://notebooks.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/ai-platform/notebooks/docs/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "oauth2:v2", "name": "oauth2", "version": "v2", "title": "Google OAuth2 API", "description": "[Deprecated] Obtains end-user authorization grants for use with other Google APIs.", "discoveryRestUrl": "https://www.googleapis.com/discovery/v1/apis/oauth2/v2/rest", "discoveryLink": "./apis/oauth2/v2/rest", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/accounts/docs/OAuth2", "preferred": true}, {"kind": "discovery#directoryItem", "id": "osconfig:v1alpha2", "name": "osconfig", "version": "v1alpha2", "title": "OS Config API", "description": "OS management tools that can be used for patch management, patch compliance, and configuration management on VM instances.", "discoveryRestUrl": "https://osconfig.googleapis.com/$discovery/rest?version=v1alpha2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/compute/docs/manage-os", "preferred": false}, {"kind": "discovery#directoryItem", "id": "osconfig:v1beta", "name": "osconfig", "version": "v1beta", "title": "OS Config API", "description": "OS management tools that can be used for patch management, patch compliance, and configuration management on VM instances.", "discoveryRestUrl": "https://osconfig.googleapis.com/$discovery/rest?version=v1beta", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/compute/docs/manage-os", "preferred": false}, {"kind": "discovery#directoryItem", "id": "osconfig:v1", "name": "osconfig", "version": "v1", "title": "OS Config API", "description": "OS management tools that can be used for patch management, patch compliance, and configuration management on VM instances.", "discoveryRestUrl": "https://osconfig.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/compute/docs/manage-os", "preferred": true}, {"kind": "discovery#directoryItem", "id": "oslogin:v1alpha", "name": "oslogin", "version": "v1alpha", "title": "Cloud OS Login API", "description": "You can use OS Login to manage access to your VM instances using IAM roles.", "discoveryRestUrl": "https://oslogin.googleapis.com/$discovery/rest?version=v1alpha", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/compute/docs/oslogin/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "oslogin:v1beta", "name": "oslogin", "version": "v1beta", "title": "Cloud OS Login API", "description": "You can use OS Login to manage access to your VM instances using IAM roles.", "discoveryRestUrl": "https://oslogin.googleapis.com/$discovery/rest?version=v1beta", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/compute/docs/oslogin/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "oslogin:v1", "name": "oslogin", "version": "v1", "title": "Cloud OS Login API", "description": "You can use OS Login to manage access to your VM instances using IAM roles.", "discoveryRestUrl": "https://oslogin.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/compute/docs/oslogin/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "pagespeedonline:v5", "name": "pagespeedonline", "version": "v5", "title": "PageSpeed Insights API", "description": "The PageSpeed Insights API lets you analyze the performance of your website with a simple API. It offers tailored suggestions for how you can optimize your site, and lets you easily integrate PageSpeed Insights analysis into your development tools and workflow.", "discoveryRestUrl": "https://pagespeedonline.googleapis.com/$discovery/rest?version=v5", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/speed/docs/insights/v5/about", "preferred": true}, {"kind": "discovery#directoryItem", "id": "people:v1", "name": "people", "version": "v1", "title": "People API", "description": "Provides access to information about profiles and contacts.", "discoveryRestUrl": "https://people.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/people/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "playablelocations:v3", "name": "playablelocations", "version": "v3", "title": "Playable Locations API", "description": "", "discoveryRestUrl": "https://playablelocations.googleapis.com/$discovery/rest?version=v3", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/maps/contact-sales/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "playcustomapp:v1", "name": "playcustomapp", "version": "v1", "title": "Google Play Custom App Publishing API", "description": "API to create and publish custom Android apps", "discoveryRestUrl": "https://playcustomapp.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/android/work/play/custom-app-api/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "policytroubleshooter:v1beta", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>er", "version": "v1beta", "title": "Policy Troubleshooter API", "description": "", "discoveryRestUrl": "https://policytroubleshooter.googleapis.com/$discovery/rest?version=v1beta", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/iam/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "policytroubleshooter:v1", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>er", "version": "v1", "title": "Policy Troubleshooter API", "description": "", "discoveryRestUrl": "https://policytroubleshooter.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/iam/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "poly:v1", "name": "poly", "version": "v1", "title": "Poly API", "description": "The Poly API provides read access to assets hosted on poly.google.com to all, and upload access to poly.google.com for whitelisted accounts.", "discoveryRestUrl": "https://poly.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/poly/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "privateca:v1beta1", "name": "privateca", "version": "v1beta1", "title": "Certificate Authority API", "description": "The Certificate Authority Service API is a highly-available, scalable service that enables you to simplify and automate the management of private certificate authorities (CAs) while staying in control of your private keys.\"", "discoveryRestUrl": "https://privateca.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "prod_tt_sasportal:v1alpha1", "name": "prod_tt_sasportal", "version": "v1alpha1", "title": "SAS Portal API (Testing)", "description": "", "discoveryRestUrl": "https://prod-tt-sasportal.googleapis.com/$discovery/rest?version=v1alpha1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/spectrum-access-system/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "pubsub:v1beta1a", "name": "pubsub", "version": "v1beta1a", "title": "Cloud Pub/Sub API", "description": "Provides reliable, many-to-many, asynchronous messaging between applications.", "discoveryRestUrl": "https://pubsub.googleapis.com/$discovery/rest?version=v1beta1a", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/pubsub/docs", "preferred": false}, {"kind": "discovery#directoryItem", "id": "pubsub:v1beta2", "name": "pubsub", "version": "v1beta2", "title": "Cloud Pub/Sub API", "description": "Provides reliable, many-to-many, asynchronous messaging between applications.", "discoveryRestUrl": "https://pubsub.googleapis.com/$discovery/rest?version=v1beta2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/pubsub/docs", "preferred": false}, {"kind": "discovery#directoryItem", "id": "pubsub:v1", "name": "pubsub", "version": "v1", "title": "Cloud Pub/Sub API", "description": "Provides reliable, many-to-many, asynchronous messaging between applications.", "discoveryRestUrl": "https://pubsub.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/pubsub/docs", "preferred": true}, {"kind": "discovery#directoryItem", "id": "pubsublite:v1", "name": "pubsublite", "version": "v1", "title": "Pub/Sub Lite API", "description": "", "discoveryRestUrl": "https://pubsublite.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/pubsub/lite/docs", "preferred": true}, {"kind": "discovery#directoryItem", "id": "realtimebidding:v1", "name": "realtimebidding", "version": "v1", "title": "Real-time Bidding API", "description": "Allows external bidders to manage their RTB integration with Google. This includes managing bidder endpoints, QPS quotas, configuring what ad inventory to receive via pretargeting, submitting creatives for verification, and accessing creative metadata such as approval status.", "discoveryRestUrl": "https://realtimebidding.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/authorized-buyers/apis/realtimebidding/reference/rest/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "recommendationengine:v1beta1", "name": "recommendationengine", "version": "v1beta1", "title": "Recommendations AI", "description": "Recommendations AI service enables customers to build end-to-end personalized recommendation systems without requiring a high level of expertise in machine learning, recommendation system, or Google Cloud.", "discoveryRestUrl": "https://recommendationengine.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/recommendations-ai/docs", "preferred": true}, {"kind": "discovery#directoryItem", "id": "recommender:v1beta1", "name": "recommender", "version": "v1beta1", "title": "Recommender API", "description": "", "discoveryRestUrl": "https://recommender.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/recommender/docs/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "recommender:v1", "name": "recommender", "version": "v1", "title": "Recommender API", "description": "", "discoveryRestUrl": "https://recommender.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/recommender/docs/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "redis:v1beta1", "name": "redis", "version": "v1beta1", "title": "Google Cloud Memorystore for Redis API", "description": "Creates and manages Redis instances on the Google Cloud Platform.", "discoveryRestUrl": "https://redis.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/memorystore/docs/redis/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "redis:v1", "name": "redis", "version": "v1", "title": "Google Cloud Memorystore for Redis API", "description": "Creates and manages Redis instances on the Google Cloud Platform.", "discoveryRestUrl": "https://redis.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/memorystore/docs/redis/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "remotebuildexecution:v1alpha", "name": "remotebuildexecution", "version": "v1alpha", "title": "Remote Build Execution API", "description": "Supplies a Remote Execution API service for tools such as bazel.", "discoveryRestUrl": "https://remotebuildexecution.googleapis.com/$discovery/rest?version=v1alpha", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/remote-build-execution/docs/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "remotebuildexecution:v1", "name": "remotebuildexecution", "version": "v1", "title": "Remote Build Execution API", "description": "Supplies a Remote Execution API service for tools such as bazel.", "discoveryRestUrl": "https://remotebuildexecution.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/remote-build-execution/docs/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "remotebuildexecution:v2", "name": "remotebuildexecution", "version": "v2", "title": "Remote Build Execution API", "description": "Supplies a Remote Execution API service for tools such as bazel.", "discoveryRestUrl": "https://remotebuildexecution.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/remote-build-execution/docs/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "reseller:v1", "name": "reseller", "version": "v1", "title": "Google Workspace Reseller API", "description": "Perform common functions that are available on the Google Apps Reseller Console at scale like placing orders and viewing customer information", "discoveryRestUrl": "https://reseller.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/google-apps/reseller/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "run:v1alpha1", "name": "run", "version": "v1alpha1", "title": "Cloud Run Admin API", "description": "Deploy and manage user provided container images that scale automatically based on HTTP traffic.", "discoveryRestUrl": "https://run.googleapis.com/$discovery/rest?version=v1alpha1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/run/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "run:v1beta1", "name": "run", "version": "v1beta1", "title": "Cloud Run Admin API", "description": "Deploy and manage user provided container images that scale automatically based on HTTP traffic.", "discoveryRestUrl": "https://run.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/run/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "run:v1", "name": "run", "version": "v1", "title": "Cloud Run Admin API", "description": "Deploy and manage user provided container images that scale automatically based on HTTP traffic.", "discoveryRestUrl": "https://run.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/run/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "runtimeconfig:v1beta1", "name": "runtimeconfig", "version": "v1beta1", "title": "Cloud Runtime Configuration API", "description": "The Runtime Configurator allows you to dynamically configure and expose variables through Google Cloud Platform. In addition, you can also set Watchers and Waiters that will watch for changes to your data and return based on certain conditions.", "discoveryRestUrl": "https://runtimeconfig.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/deployment-manager/runtime-configurator/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "runtimeconfig:v1", "name": "runtimeconfig", "version": "v1", "title": "Cloud Runtime Configuration API", "description": "The Runtime Configurator allows you to dynamically configure and expose variables through Google Cloud Platform. In addition, you can also set Watchers and Waiters that will watch for changes to your data and return based on certain conditions.", "discoveryRestUrl": "https://runtimeconfig.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/deployment-manager/runtime-configurator/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "safebrowsing:v4", "name": "safebrowsing", "version": "v4", "title": "Safe Browsing API", "description": "Enables client applications to check web resources (most commonly URLs) against Google-generated lists of unsafe web resources. The Safe Browsing APIs are for non-commercial use only. If you need to use APIs to detect malicious URLs for commercial purposes – meaning “for sale or revenue-generating purposes” – please refer to the Web Risk API.", "discoveryRestUrl": "https://safebrowsing.googleapis.com/$discovery/rest?version=v4", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/safe-browsing/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "sasportal:v1alpha1", "name": "sasportal", "version": "v1alpha1", "title": "SAS Portal API", "description": "", "discoveryRestUrl": "https://sasportal.googleapis.com/$discovery/rest?version=v1alpha1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/spectrum-access-system/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "script:v1", "name": "script", "version": "v1", "title": "Apps Script API", "description": "Manages and executes Google Apps Script projects.", "discoveryRestUrl": "https://script.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/apps-script/api/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "searchconsole:v1", "name": "searchconsole", "version": "v1", "title": "Google Search Console API", "description": "The Search Console API provides access to both Search Console data (verified users only) and to public information on an URL basis (anyone)", "discoveryRestUrl": "https://searchconsole.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/webmaster-tools/search-console-api/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "secretmanager:v1beta1", "name": "secretmanager", "version": "v1beta1", "title": "Secret Manager API", "description": "Stores sensitive data such as API keys, passwords, and certificates. Provides convenience while improving security.", "discoveryRestUrl": "https://secretmanager.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/secret-manager/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "secretmanager:v1", "name": "secretmanager", "version": "v1", "title": "Secret Manager API", "description": "Stores sensitive data such as API keys, passwords, and certificates. Provides convenience while improving security.", "discoveryRestUrl": "https://secretmanager.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/secret-manager/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "securitycenter:v1beta1", "name": "securitycenter", "version": "v1beta1", "title": "Security Command Center API", "description": "Security Command Center API provides access to temporal views of assets and findings within an organization.", "discoveryRestUrl": "https://securitycenter.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://console.cloud.google.com/apis/api/securitycenter.googleapis.com/overview", "preferred": false}, {"kind": "discovery#directoryItem", "id": "securitycenter:v1beta2", "name": "securitycenter", "version": "v1beta2", "title": "Security Command Center API", "description": "Security Command Center API provides access to temporal views of assets and findings within an organization.", "discoveryRestUrl": "https://securitycenter.googleapis.com/$discovery/rest?version=v1beta2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://console.cloud.google.com/apis/api/securitycenter.googleapis.com/overview", "preferred": false}, {"kind": "discovery#directoryItem", "id": "securitycenter:v1", "name": "securitycenter", "version": "v1", "title": "Security Command Center API", "description": "Security Command Center API provides access to temporal views of assets and findings within an organization.", "discoveryRestUrl": "https://securitycenter.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://console.cloud.google.com/apis/api/securitycenter.googleapis.com/overview", "preferred": true}, {"kind": "discovery#directoryItem", "id": "serviceconsumermanagement:v1beta1", "name": "serviceconsumermanagement", "version": "v1beta1", "title": "Service Consumer Management API", "description": "Manages the service consumers of a Service Infrastructure service.", "discoveryRestUrl": "https://serviceconsumermanagement.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/service-consumer-management/docs/overview", "preferred": false}, {"kind": "discovery#directoryItem", "id": "serviceconsumermanagement:v1", "name": "serviceconsumermanagement", "version": "v1", "title": "Service Consumer Management API", "description": "Manages the service consumers of a Service Infrastructure service.", "discoveryRestUrl": "https://serviceconsumermanagement.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/service-consumer-management/docs/overview", "preferred": true}, {"kind": "discovery#directoryItem", "id": "servicecontrol:v1", "name": "servicecontrol", "version": "v1", "title": "Service Control API", "description": "Provides control plane functionality to managed services, such as logging, monitoring, and status checks.", "discoveryRestUrl": "https://servicecontrol.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/service-control/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "servicecontrol:v2", "name": "servicecontrol", "version": "v2", "title": "Service Control API", "description": "Provides control plane functionality to managed services, such as logging, monitoring, and status checks.", "discoveryRestUrl": "https://servicecontrol.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/service-control/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "servicedirectory:v1beta1", "name": "servicedirectory", "version": "v1beta1", "title": "Service Directory API", "description": "Service Directory is a platform for discovering, publishing, and connecting services.", "discoveryRestUrl": "https://servicedirectory.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/service-directory", "preferred": true}, {"kind": "discovery#directoryItem", "id": "servicemanagement:v1", "name": "servicemanagement", "version": "v1", "title": "Service Management API", "description": "Google Service Management allows service producers to publish their services on Google Cloud Platform so that they can be discovered and used by service consumers.", "discoveryRestUrl": "https://servicemanagement.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/service-management/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "servicenetworking:v1beta", "name": "servicenetworking", "version": "v1beta", "title": "Service Networking API", "description": "Provides automatic management of network configurations necessary for certain services.", "discoveryRestUrl": "https://servicenetworking.googleapis.com/$discovery/rest?version=v1beta", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/service-infrastructure/docs/service-networking/getting-started", "preferred": false}, {"kind": "discovery#directoryItem", "id": "servicenetworking:v1", "name": "servicenetworking", "version": "v1", "title": "Service Networking API", "description": "Provides automatic management of network configurations necessary for certain services.", "discoveryRestUrl": "https://servicenetworking.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/service-infrastructure/docs/service-networking/getting-started", "preferred": true}, {"kind": "discovery#directoryItem", "id": "serviceusage:v1beta1", "name": "serviceusage", "version": "v1beta1", "title": "Service Usage API", "description": "Enables services that service consumers want to use on Google Cloud Platform, lists the available or enabled services, or disables services that service consumers no longer use.", "discoveryRestUrl": "https://serviceusage.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/service-usage/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "serviceusage:v1", "name": "serviceusage", "version": "v1", "title": "Service Usage API", "description": "Enables services that service consumers want to use on Google Cloud Platform, lists the available or enabled services, or disables services that service consumers no longer use.", "discoveryRestUrl": "https://serviceusage.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/service-usage/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "sheets:v4", "name": "sheets", "version": "v4", "title": "Google Sheets API", "description": "Reads and writes Google Sheets.", "discoveryRestUrl": "https://sheets.googleapis.com/$discovery/rest?version=v4", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/sheets/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "siteVerification:v1", "name": "siteVerification", "version": "v1", "title": "Site Verification API", "description": "The Google Site Verification API lets applications automate the process of managing ownership records for websites and domains.", "discoveryRestUrl": "https://siteverification.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://code.google.com/apis/siteverification/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "slides:v1", "name": "slides", "version": "v1", "title": "Google Slides API", "description": "Reads and writes Google Slides presentations.", "discoveryRestUrl": "https://slides.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/slides/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "smartdevicemanagement:v1", "name": "smartdevicemanagement", "version": "v1", "title": "Smart Device Management API", "description": "Allow select enterprise partners to access, control, and manage Google and Nest devices programmatically.", "discoveryRestUrl": "https://smartdevicemanagement.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/nest/device-access", "preferred": true}, {"kind": "discovery#directoryItem", "id": "sourcerepo:v1", "name": "sourcerepo", "version": "v1", "title": "Cloud Source Repositories API", "description": "Accesses source code repositories hosted by Google.", "discoveryRestUrl": "https://sourcerepo.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/source-repositories/docs/apis", "preferred": true}, {"kind": "discovery#directoryItem", "id": "spanner:v1", "name": "spanner", "version": "v1", "title": "Cloud Spanner API", "description": "Cloud Spanner is a managed, mission-critical, globally consistent and scalable relational database service.", "discoveryRestUrl": "https://spanner.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/spanner/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "speech:v1p1beta1", "name": "speech", "version": "v1p1beta1", "title": "Cloud Speech-to-Text API", "description": "Converts audio to text by applying powerful neural network models.", "discoveryRestUrl": "https://speech.googleapis.com/$discovery/rest?version=v1p1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/speech-to-text/docs/quickstart-protocol", "preferred": false}, {"kind": "discovery#directoryItem", "id": "speech:v2beta1", "name": "speech", "version": "v2beta1", "title": "Cloud Speech-to-Text API", "description": "Converts audio to text by applying powerful neural network models.", "discoveryRestUrl": "https://speech.googleapis.com/$discovery/rest?version=v2beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/speech-to-text/docs/quickstart-protocol", "preferred": false}, {"kind": "discovery#directoryItem", "id": "speech:v1", "name": "speech", "version": "v1", "title": "Cloud Speech-to-Text API", "description": "Converts audio to text by applying powerful neural network models.", "discoveryRestUrl": "https://speech.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/speech-to-text/docs/quickstart-protocol", "preferred": true}, {"kind": "discovery#directoryItem", "id": "sqladmin:v1beta4", "name": "sqladmin", "version": "v1beta4", "title": "Cloud SQL Admin API", "description": "API for Cloud SQL database instance management", "discoveryRestUrl": "https://sqladmin.googleapis.com/$discovery/rest?version=v1beta4", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/cloud-sql/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "storage:v1", "name": "storage", "version": "v1", "title": "Cloud Storage API", "description": "Lets you store and retrieve potentially-large, immutable data objects.", "discoveryRestUrl": "https://storage.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/storage/docs/json_api/v1/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "storagetransfer:v1", "name": "storagetransfer", "version": "v1", "title": "Storage Transfer API", "description": "Transfers data from external data sources to a Google Cloud Storage bucket or between Google Cloud Storage buckets.", "discoveryRestUrl": "https://storagetransfer.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/storage-transfer/docs", "preferred": true}, {"kind": "discovery#directoryItem", "id": "streetviewpublish:v1", "name": "streetviewpublish", "version": "v1", "title": "Street View Publish API", "description": "Publishes 360 photos to Google Maps, along with position, orientation, and connectivity metadata. Apps can offer an interface for positioning, connecting, and uploading user-generated Street View images.", "discoveryRestUrl": "https://streetviewpublish.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/streetview/publish/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "sts:v1beta", "name": "sts", "version": "v1beta", "title": "Security Token Service API", "description": "The Security Token Service exchanges Google or third-party credentials for a short-lived access token to Google Cloud resources.", "discoveryRestUrl": "https://sts.googleapis.com/$discovery/rest?version=v1beta", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "http://cloud.google.com/iam/docs/workload-identity-federation", "preferred": false}, {"kind": "discovery#directoryItem", "id": "sts:v1", "name": "sts", "version": "v1", "title": "Security Token Service API", "description": "The Security Token Service exchanges Google or third-party credentials for a short-lived access token to Google Cloud resources.", "discoveryRestUrl": "https://sts.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "http://cloud.google.com/iam/docs/workload-identity-federation", "preferred": true}, {"kind": "discovery#directoryItem", "id": "tagmanager:v1", "name": "tagmanager", "version": "v1", "title": "Tag Manager API", "description": "This API allows clients to access and modify container and tag configuration.", "discoveryRestUrl": "https://tagmanager.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/tag-manager", "preferred": false}, {"kind": "discovery#directoryItem", "id": "tagmanager:v2", "name": "tagmanager", "version": "v2", "title": "Tag Manager API", "description": "This API allows clients to access and modify container and tag configuration.", "discoveryRestUrl": "https://tagmanager.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/tag-manager", "preferred": true}, {"kind": "discovery#directoryItem", "id": "tasks:v1", "name": "tasks", "version": "v1", "title": "Tasks API", "description": "The Google Tasks API lets you manage your tasks and task lists.", "discoveryRestUrl": "https://tasks.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "preferred": true}, {"kind": "discovery#directoryItem", "id": "testing:v1", "name": "testing", "version": "v1", "title": "Cloud Testing API", "description": "Allows developers to run automated tests for their mobile applications on Google infrastructure.", "discoveryRestUrl": "https://testing.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/cloud-test-lab/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "texttospeech:v1beta1", "name": "texttospeech", "version": "v1beta1", "title": "Cloud Text-to-Speech API", "description": "Synthesizes natural-sounding speech by applying powerful neural network models.", "discoveryRestUrl": "https://texttospeech.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/text-to-speech/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "texttospeech:v1", "name": "texttospeech", "version": "v1", "title": "Cloud Text-to-Speech API", "description": "Synthesizes natural-sounding speech by applying powerful neural network models.", "discoveryRestUrl": "https://texttospeech.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/text-to-speech/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "toolresults:v1beta3", "name": "toolresults", "version": "v1beta3", "title": "Cloud Tool Results API", "description": "API to publish and access results from developer tools.", "discoveryRestUrl": "https://toolresults.googleapis.com/$discovery/rest?version=v1beta3", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://firebase.google.com/docs/test-lab/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "tpu:v1alpha1", "name": "tpu", "version": "v1alpha1", "title": "Cloud TPU API", "description": "TPU API provides customers with access to Google TPU technology.", "discoveryRestUrl": "https://tpu.googleapis.com/$discovery/rest?version=v1alpha1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/tpu/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "tpu:v1", "name": "tpu", "version": "v1", "title": "Cloud TPU API", "description": "TPU API provides customers with access to Google TPU technology.", "discoveryRestUrl": "https://tpu.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/tpu/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "trafficdirector:v2", "name": "trafficdirector", "version": "v2", "title": "Traffic Director API", "description": "", "discoveryRestUrl": "https://trafficdirector.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/traffic-director", "preferred": true}, {"kind": "discovery#directoryItem", "id": "transcoder:v1beta1", "name": "transcoder", "version": "v1beta1", "title": "Transcoder API", "description": "This API converts video files into formats suitable for consumer distribution.", "discoveryRestUrl": "https://transcoder.googleapis.com/$discovery/rest?version=v1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/transcoder/docs/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "translate:v3beta1", "name": "translate", "version": "v3beta1", "title": "Cloud Translation API", "description": "Integrates text translation into your website or application.", "discoveryRestUrl": "https://translation.googleapis.com/$discovery/rest?version=v3beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/translate/docs/quickstarts", "preferred": false}, {"kind": "discovery#directoryItem", "id": "translate:v2", "name": "translate", "version": "v2", "title": "Cloud Translation API", "description": "Integrates text translation into your website or application.", "discoveryRestUrl": "https://translation.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/translate/docs/quickstarts", "preferred": false}, {"kind": "discovery#directoryItem", "id": "translate:v3", "name": "translate", "version": "v3", "title": "Cloud Translation API", "description": "Integrates text translation into your website or application.", "discoveryRestUrl": "https://translation.googleapis.com/$discovery/rest?version=v3", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/translate/docs/quickstarts", "preferred": true}, {"kind": "discovery#directoryItem", "id": "vault:v1", "name": "vault", "version": "v1", "title": "G Suite Vault API", "description": "Archiving and eDiscovery for G Suite.", "discoveryRestUrl": "https://vault.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/vault", "preferred": true}, {"kind": "discovery#directoryItem", "id": "vectortile:v1", "name": "vectortile", "version": "v1", "title": "Semantic Tile API", "description": "Serves vector tiles containing geospatial data.", "discoveryRestUrl": "https://vectortile.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/maps/contact-sales/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "verifiedaccess:v1", "name": "verifiedaccess", "version": "v1", "title": "Chrome Verified Access API", "description": "API for Verified Access chrome extension to provide credential verification for chrome devices connecting to an enterprise network", "discoveryRestUrl": "https://verifiedaccess.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/chrome/verified-access", "preferred": true}, {"kind": "discovery#directoryItem", "id": "videointelligence:v1p1beta1", "name": "videointelligence", "version": "v1p1beta1", "title": "Cloud Video Intelligence API", "description": "Detects objects, explicit content, and scene changes in videos. It also specifies the region for annotation and transcribes speech to text. Supports both asynchronous API and streaming API.", "discoveryRestUrl": "https://videointelligence.googleapis.com/$discovery/rest?version=v1p1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/video-intelligence/docs/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "videointelligence:v1p2beta1", "name": "videointelligence", "version": "v1p2beta1", "title": "Cloud Video Intelligence API", "description": "Detects objects, explicit content, and scene changes in videos. It also specifies the region for annotation and transcribes speech to text. Supports both asynchronous API and streaming API.", "discoveryRestUrl": "https://videointelligence.googleapis.com/$discovery/rest?version=v1p2beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/video-intelligence/docs/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "videointelligence:v1p3beta1", "name": "videointelligence", "version": "v1p3beta1", "title": "Cloud Video Intelligence API", "description": "Detects objects, explicit content, and scene changes in videos. It also specifies the region for annotation and transcribes speech to text. Supports both asynchronous API and streaming API.", "discoveryRestUrl": "https://videointelligence.googleapis.com/$discovery/rest?version=v1p3beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/video-intelligence/docs/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "videointelligence:v1beta2", "name": "videointelligence", "version": "v1beta2", "title": "Cloud Video Intelligence API", "description": "Detects objects, explicit content, and scene changes in videos. It also specifies the region for annotation and transcribes speech to text. Supports both asynchronous API and streaming API.", "discoveryRestUrl": "https://videointelligence.googleapis.com/$discovery/rest?version=v1beta2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/video-intelligence/docs/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "videointelligence:v1", "name": "videointelligence", "version": "v1", "title": "Cloud Video Intelligence API", "description": "Detects objects, explicit content, and scene changes in videos. It also specifies the region for annotation and transcribes speech to text. Supports both asynchronous API and streaming API.", "discoveryRestUrl": "https://videointelligence.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/video-intelligence/docs/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "vision:v1p1beta1", "name": "vision", "version": "v1p1beta1", "title": "Cloud Vision API", "description": "Integrates Google Vision features, including image labeling, face, logo, and landmark detection, optical character recognition (OCR), and detection of explicit content, into applications.", "discoveryRestUrl": "https://vision.googleapis.com/$discovery/rest?version=v1p1beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/vision/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "vision:v1p2beta1", "name": "vision", "version": "v1p2beta1", "title": "Cloud Vision API", "description": "Integrates Google Vision features, including image labeling, face, logo, and landmark detection, optical character recognition (OCR), and detection of explicit content, into applications.", "discoveryRestUrl": "https://vision.googleapis.com/$discovery/rest?version=v1p2beta1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/vision/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "vision:v1", "name": "vision", "version": "v1", "title": "Cloud Vision API", "description": "Integrates Google Vision features, including image labeling, face, logo, and landmark detection, optical character recognition (OCR), and detection of explicit content, into applications.", "discoveryRestUrl": "https://vision.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/vision/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "webfonts:v1", "name": "webfonts", "version": "v1", "title": "Web Fonts Developer API", "description": "The Google Web Fonts Developer API lets you retrieve information about web fonts served by Google.", "discoveryRestUrl": "https://webfonts.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/fonts/docs/developer_api", "preferred": true}, {"kind": "discovery#directoryItem", "id": "webmasters:v3", "name": "webmasters", "version": "v3", "title": "Search Console API", "description": "[Deprecated] View Google Search Console data for your verified sites.", "discoveryRestUrl": "https://www.googleapis.com/discovery/v1/apis/webmasters/v3/rest", "discoveryLink": "./apis/webmasters/v3/rest", "icons": {"x16": "https://www.google.com/images/icons/product/webmaster_tools-16.png", "x32": "https://www.google.com/images/icons/product/webmaster_tools-32.png"}, "documentationLink": "https://developers.google.com/webmaster-tools/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "websecurityscanner:v1alpha", "name": "websecurityscanner", "version": "v1alpha", "title": "Web Security Scanner API", "description": "Scans your Compute and App Engine apps for common web vulnerabilities.", "discoveryRestUrl": "https://websecurityscanner.googleapis.com/$discovery/rest?version=v1alpha", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/security-command-center/docs/concepts-web-security-scanner-overview/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "websecurityscanner:v1beta", "name": "websecurityscanner", "version": "v1beta", "title": "Web Security Scanner API", "description": "Scans your Compute and App Engine apps for common web vulnerabilities.", "discoveryRestUrl": "https://websecurityscanner.googleapis.com/$discovery/rest?version=v1beta", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/security-command-center/docs/concepts-web-security-scanner-overview/", "preferred": false}, {"kind": "discovery#directoryItem", "id": "websecurityscanner:v1", "name": "websecurityscanner", "version": "v1", "title": "Web Security Scanner API", "description": "Scans your Compute and App Engine apps for common web vulnerabilities.", "discoveryRestUrl": "https://websecurityscanner.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/security-command-center/docs/concepts-web-security-scanner-overview/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "workflowexecutions:v1beta", "name": "workflowexecutions", "version": "v1beta", "title": "Workflow Executions API", "description": "Execute workflows created with Workflows API.", "discoveryRestUrl": "https://workflowexecutions.googleapis.com/$discovery/rest?version=v1beta", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/workflows", "preferred": true}, {"kind": "discovery#directoryItem", "id": "workflows:v1beta", "name": "workflows", "version": "v1beta", "title": "Workflows API", "description": "Orchestrate Workflows consisting of Google Cloud APIs, SaaS APIs or private API endpoints.", "discoveryRestUrl": "https://workflows.googleapis.com/$discovery/rest?version=v1beta", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://cloud.google.com/workflows", "preferred": true}, {"kind": "discovery#directoryItem", "id": "youtube:v3", "name": "youtube", "version": "v3", "title": "YouTube Data API v3", "description": "The YouTube Data API v3 is an API that provides access to YouTube data, such as videos, playlists, and channels.", "discoveryRestUrl": "https://youtube.googleapis.com/$discovery/rest?version=v3", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/youtube/", "preferred": true}, {"kind": "discovery#directoryItem", "id": "youtubeAnalytics:v1", "name": "youtubeAnalytics", "version": "v1", "title": "YouTube Analytics API", "description": "Retrieves your YouTube Analytics data.", "discoveryRestUrl": "https://youtubeanalytics.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/youtube/analytics", "preferred": false}, {"kind": "discovery#directoryItem", "id": "youtubeAnalytics:v2", "name": "youtubeAnalytics", "version": "v2", "title": "YouTube Analytics API", "description": "Retrieves your YouTube Analytics data.", "discoveryRestUrl": "https://youtubeanalytics.googleapis.com/$discovery/rest?version=v2", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/youtube/analytics", "preferred": true}, {"kind": "discovery#directoryItem", "id": "youtubereporting:v1", "name": "youtubereporting", "version": "v1", "title": "YouTube Reporting API", "description": "Schedules reporting jobs containing your YouTube Analytics data and downloads the resulting bulk data reports in the form of CSV files.", "discoveryRestUrl": "https://youtubereporting.googleapis.com/$discovery/rest?version=v1", "icons": {"x16": "https://www.gstatic.com/images/branding/product/1x/googleg_16dp.png", "x32": "https://www.gstatic.com/images/branding/product/1x/googleg_32dp.png"}, "documentationLink": "https://developers.google.com/youtube/reporting/v1/reports/", "preferred": true}]}