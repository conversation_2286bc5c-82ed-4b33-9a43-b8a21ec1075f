{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://gkeonprem.googleapis.com/", "batchPath": "batch", "canonicalName": "GKE On-Prem", "description": "", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/anthos/clusters/docs/on-prem/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "gkeonprem:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://gkeonprem.mtls.googleapis.com/", "name": "gkeonprem", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "gkeonprem.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "gkeonprem.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"bareMetalAdminClusters": {"methods": {"create": {"description": "Creates a new bare metal admin cluster in a given project and location. The API needs to be combined with creating a bootstrap cluster to work. See: https://cloud.google.com/anthos/clusters/docs/bare-metal/latest/installing/creating-clusters/create-admin-cluster-api#prepare_bootstrap_environment", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters", "httpMethod": "POST", "id": "gkeonprem.projects.locations.bareMetalAdminClusters.create", "parameterOrder": ["parent"], "parameters": {"allowPreflightFailure": {"description": "Optional. If set to true, CLM will force CCFE to persist the cluster resource in RMS when the creation fails during standalone preflight checks. In that case the subsequent create call will fail with \"cluster already exists\" error and hence a update cluster is required to fix the cluster.", "location": "query", "type": "boolean"}, "bareMetalAdminClusterId": {"description": "Required. User provided identifier that is used as part of the resource name; must conform to RFC-1034 and additionally restrict to lower-cased letters. This comes out roughly to: /^a-z+[a-z0-9]$/", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent of the project and location where the cluster is created in. Format: \"projects/{project}/locations/{location}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Validate the request without actually doing any updates.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/bareMetalAdminClusters", "request": {"$ref": "BareMetalAdminCluster"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "enroll": {"description": "Enrolls an existing bare metal admin cluster to the Anthos On-Prem API within a given project and location. Through enrollment, an existing admin cluster will become Anthos On-Prem API managed. The corresponding GCP resources will be created and all future modifications to the cluster will be expected to be performed through the API.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters:enroll", "httpMethod": "POST", "id": "gkeonprem.projects.locations.bareMetalAdminClusters.enroll", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent of the project and location where the cluster is enrolled in. Format: \"projects/{project}/locations/{location}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/bareMetalAdminClusters:enroll", "request": {"$ref": "EnrollBareMetalAdminClusterRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single bare metal admin cluster.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters/{bareMetalAdminClustersId}", "httpMethod": "GET", "id": "gkeonprem.projects.locations.bareMetalAdminClusters.get", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If true, return BareMetal Admin Cluster including the one that only exists in RMS.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Name of the bare metal admin cluster to get. Format: \"projects/{project}/locations/{location}/bareMetalAdminClusters/{bare_metal_admin_cluster}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalAdminClusters/[^/]+$", "required": true, "type": "string"}, "view": {"description": "View for bare metal admin cluster. When `BASIC` is specified, only the cluster resource name and membership are returned. The default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL', which returns the complete cluster configuration details.", "enum": ["CLUSTER_VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["If the value is not set, the default `FULL` view is used.", "Includes basic information of a cluster resource including cluster resource name and membership.", "Includes the complete configuration for bare metal admin cluster resource. This is the default value for GetBareMetalAdminClusterRequest method."], "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "BareMetalAdminCluster"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters/{bareMetalAdminClustersId}:getIamPolicy", "httpMethod": "GET", "id": "gkeonprem.projects.locations.bareMetalAdminClusters.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalAdminClusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists bare metal admin clusters in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters", "httpMethod": "GET", "id": "gkeonprem.projects.locations.bareMetalAdminClusters.list", "parameterOrder": ["parent"], "parameters": {"allowMissing": {"description": "Optional. If true, return list of BareMetal Admin Clusters including the ones that only exists in RMS.", "location": "query", "type": "boolean"}, "pageSize": {"description": "Requested page size. Server may return fewer items than requested. If unspecified, at most 50 clusters will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent of the project and location where the clusters are listed in. Format: \"projects/{project}/locations/{location}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "view": {"description": "View for bare metal admin clusters. When `BASIC` is specified, only the admin cluster resource name and membership are returned. The default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL', which returns the complete admin cluster configuration details.", "enum": ["CLUSTER_VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["If the value is not set, the default `FULL` view is used.", "Includes basic information of a admin cluster resource including admin cluster resource name and membership.", "Includes the complete configuration for bare metal admin cluster resource. This is the default value for ListBareMetalAdminClustersRequest method."], "location": "query", "type": "string"}}, "path": "v1/{+parent}/bareMetalAdminClusters", "response": {"$ref": "ListBareMetalAdminClustersResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single bare metal admin cluster.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters/{bareMetalAdminClustersId}", "httpMethod": "PATCH", "id": "gkeonprem.projects.locations.bareMetalAdminClusters.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. The bare metal admin cluster resource name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalAdminClusters/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the BareMetalAdminCluster resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all populated fields in the BareMetalAdminCluster message will be updated. Empty fields will be ignored unless a field mask is used.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Validate the request without actually doing any updates.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "BareMetalAdminCluster"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "queryVersionConfig": {"description": "Queries the bare metal admin cluster version config.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters:queryVersionConfig", "httpMethod": "POST", "id": "gkeonprem.projects.locations.bareMetalAdminClusters.queryVersionConfig", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent of the project and location to query for version config. Format: \"projects/{project}/locations/{location}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "upgradeConfig.clusterName": {"description": "The admin cluster resource name. This is the full resource name of the admin cluster resource. Format: \"projects/{project}/locations/{location}/bareMetalAdminClusters/{bare_metal_admin_cluster}\"", "location": "query", "type": "string"}}, "path": "v1/{+parent}/bareMetalAdminClusters:queryVersionConfig", "response": {"$ref": "QueryBareMetalAdminVersionConfigResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters/{bareMetalAdminClustersId}:setIamPolicy", "httpMethod": "POST", "id": "gkeonprem.projects.locations.bareMetalAdminClusters.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalAdminClusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters/{bareMetalAdminClustersId}:testIamPermissions", "httpMethod": "POST", "id": "gkeonprem.projects.locations.bareMetalAdminClusters.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalAdminClusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "unenroll": {"description": "Unenrolls an existing bare metal admin cluster from the Anthos On-Prem API within a given project and location. Unenrollment removes the Cloud reference to the cluster without modifying the underlying OnPrem Resources. Clusters will continue to run; however, they will no longer be accessible through the Anthos On-Prem API or its clients.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters/{bareMetalAdminClustersId}:unenroll", "httpMethod": "DELETE", "id": "gkeonprem.projects.locations.bareMetalAdminClusters.unenroll", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "If set to true, and the bare metal admin cluster is not found, the request will succeed but no action will be taken on the server and return a completed LRO.", "location": "query", "type": "boolean"}, "etag": {"description": "The current etag of the bare metal admin cluster. If an etag is provided and does not match the current etag of the cluster, deletion will be blocked and an ABORTED error will be returned.", "location": "query", "type": "string"}, "ignoreErrors": {"description": "If set to true, the unenrollment of a bare metal admin cluster resource will succeed even if errors occur during unenrollment. This parameter can be used when you want to unenroll admin cluster resource and the on-prem admin cluster is disconnected / unreachable. WARNING: Using this parameter when your admin cluster still exists may result in a deleted GCP admin cluster but existing resourcelink in on-prem admin cluster and membership.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Name of the bare metal admin cluster to be unenrolled. Format: \"projects/{project}/locations/{location}/bareMetalAdminClusters/{cluster}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalAdminClusters/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Validate the request without actually doing any updates.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}:unenroll", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters/{bareMetalAdminClustersId}/operations/{operationsId}", "httpMethod": "GET", "id": "gkeonprem.projects.locations.bareMetalAdminClusters.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalAdminClusters/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters/{bareMetalAdminClustersId}/operations", "httpMethod": "GET", "id": "gkeonprem.projects.locations.bareMetalAdminClusters.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalAdminClusters/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "bareMetalClusters": {"methods": {"create": {"description": "Creates a new bare metal cluster in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters", "httpMethod": "POST", "id": "gkeonprem.projects.locations.bareMetalClusters.create", "parameterOrder": ["parent"], "parameters": {"allowPreflightFailure": {"description": "Optional. If set to true, CLM will force CCFE to persist the cluster resource in RMS when the creation fails during standalone preflight checks. In that case the subsequent create call will fail with \"cluster already exists\" error and hence a update cluster is required to fix the cluster.", "location": "query", "type": "boolean"}, "bareMetalClusterId": {"description": "Required. User provided identifier that is used as part of the resource name; must conform to RFC-1034 and additionally restrict to lower-cased letters. This comes out roughly to: /^a-z+[a-z0-9]$/", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent of the project and location where the cluster is created in. Format: \"projects/{project}/locations/{location}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Validate the request without actually doing any updates.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/bareMetalClusters", "request": {"$ref": "BareMetalCluster"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single bare metal Cluster.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}", "httpMethod": "DELETE", "id": "gkeonprem.projects.locations.bareMetalClusters.delete", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "If set to true, and the bare metal cluster is not found, the request will succeed but no action will be taken on the server and return a completed LRO.", "location": "query", "type": "boolean"}, "etag": {"description": "The current etag of the bare metal Cluster. If an etag is provided and does not match the current etag of the cluster, deletion will be blocked and an ABORTED error will be returned.", "location": "query", "type": "string"}, "force": {"description": "If set to true, any node pools from the cluster will also be deleted.", "location": "query", "type": "boolean"}, "ignoreErrors": {"description": "If set to true, the deletion of a bare metal user cluster resource will succeed even if errors occur during deletion. This parameter can be used when you want to delete GCP's cluster resource and the on-prem admin cluster that hosts your user cluster is disconnected / unreachable or deleted. WARNING: Using this parameter when your user cluster still exists may result in a deleted GCP user cluster but an existing on-prem user cluster.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Name of the bare metal user cluster to be deleted. Format: \"projects/{project}/locations/{location}/bareMetalClusters/{bare_metal_cluster}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalClusters/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Validate the request without actually doing any updates.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "enroll": {"description": "Enrolls an existing bare metal user cluster and its node pools to the Anthos On-Prem API within a given project and location. Through enrollment, an existing cluster will become Anthos On-Prem API managed. The corresponding GCP resources will be created and all future modifications to the cluster and/or its node pools will be expected to be performed through the API.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters:enroll", "httpMethod": "POST", "id": "gkeonprem.projects.locations.bareMetalClusters.enroll", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent of the project and location where the cluster is enrolled in. Format: \"projects/{project}/locations/{location}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/bareMetalClusters:enroll", "request": {"$ref": "EnrollBareMetalClusterRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single bare metal Cluster.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}", "httpMethod": "GET", "id": "gkeonprem.projects.locations.bareMetalClusters.get", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If true, return BareMetal Cluster including the one that only exists in RMS.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Name of the bare metal user cluster to get. Format: \"projects/{project}/locations/{location}/bareMetalClusters/{bare_metal_cluster}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalClusters/[^/]+$", "required": true, "type": "string"}, "view": {"description": "View for bare metal user cluster. When `BASIC` is specified, only the cluster resource name and admin cluster membership are returned. The default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL', which returns the complete cluster configuration details.", "enum": ["CLUSTER_VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["If the value is not set, the default `FULL` view is used.", "Includes basic information of a cluster resource including cluster resource name and admin cluster membership.", "Includes the complete configuration for bare metal cluster resource. This is the default value for GetBareMetalClusterRequest method."], "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "BareMetalCluster"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}:getIamPolicy", "httpMethod": "GET", "id": "gkeonprem.projects.locations.bareMetalClusters.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalClusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists bare metal clusters in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters", "httpMethod": "GET", "id": "gkeonprem.projects.locations.bareMetalClusters.list", "parameterOrder": ["parent"], "parameters": {"allowMissing": {"description": "Optional. If true, return list of BareMetal Clusters including the ones that only exists in RMS.", "location": "query", "type": "boolean"}, "filter": {"description": "A resource filtering expression following https://google.aip.dev/160. When non-empty, only resource's whose attributes field matches the filter are returned.", "location": "query", "type": "string"}, "pageSize": {"description": "Requested page size. Server may return fewer items than requested. If unspecified, at most 50 clusters will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent of the project and location where the clusters are listed in. Format: \"projects/{project}/locations/{location}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "view": {"description": "View for bare metal Clusters. When `BASIC` is specified, only the cluster resource name and admin cluster membership are returned. The default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL', which returns the complete cluster configuration details.", "enum": ["CLUSTER_VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["If the value is not set, the default `FULL` view is used.", "Includes basic information of a cluster resource including cluster resource name and admin cluster membership.", "Includes the complete configuration for bare metal cluster resource. This is the default value for ListBareMetalClustersRequest method."], "location": "query", "type": "string"}}, "path": "v1/{+parent}/bareMetalClusters", "response": {"$ref": "ListBareMetalClustersResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single bare metal Cluster.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}", "httpMethod": "PATCH", "id": "gkeonprem.projects.locations.bareMetalClusters.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "If set to true, and the bare metal cluster is not found, the request will create a new bare metal cluster with the provided configuration. The user must have both create and update permission to call Update with allow_missing set to true.", "location": "query", "type": "boolean"}, "name": {"description": "Immutable. The bare metal user cluster resource name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalClusters/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the BareMetalCluster resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all populated fields in the BareMetalCluster message will be updated. Empty fields will be ignored unless a field mask is used.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Validate the request without actually doing any updates.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "BareMetalCluster"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "queryVersionConfig": {"description": "Queries the bare metal user cluster version config.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters:queryVersionConfig", "httpMethod": "POST", "id": "gkeonprem.projects.locations.bareMetalClusters.queryVersionConfig", "parameterOrder": ["parent"], "parameters": {"createConfig.adminClusterMembership": {"description": "The admin cluster membership. This is the full resource name of the admin cluster's fleet membership. Format: \"projects/{project}/locations/{location}/memberships/{membership}\"", "location": "query", "type": "string"}, "createConfig.adminClusterName": {"description": "The admin cluster resource name. This is the full resource name of the admin cluster resource. Format: \"projects/{project}/locations/{location}/bareMetalAdminClusters/{bare_metal_admin_cluster}\"", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent of the project and location to query for version config. Format: \"projects/{project}/locations/{location}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "upgradeConfig.clusterName": {"description": "The user cluster resource name. This is the full resource name of the user cluster resource. Format: \"projects/{project}/locations/{location}/bareMetalClusters/{bare_metal_cluster}\"", "location": "query", "type": "string"}}, "path": "v1/{+parent}/bareMetalClusters:queryVersionConfig", "response": {"$ref": "QueryBareMetalVersionConfigResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}:setIamPolicy", "httpMethod": "POST", "id": "gkeonprem.projects.locations.bareMetalClusters.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalClusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}:testIamPermissions", "httpMethod": "POST", "id": "gkeonprem.projects.locations.bareMetalClusters.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalClusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "unenroll": {"description": "Unenrolls an existing bare metal user cluster and its node pools from the Anthos On-Prem API within a given project and location. Unenrollment removes the Cloud reference to the cluster without modifying the underlying OnPrem Resources. Clusters and node pools will continue to run; however, they will no longer be accessible through the Anthos On-Prem API or its clients.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}:unenroll", "httpMethod": "DELETE", "id": "gkeonprem.projects.locations.bareMetalClusters.unenroll", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "If set to true, and the bare metal cluster is not found, the request will succeed but no action will be taken on the server and return a completed LRO.", "location": "query", "type": "boolean"}, "etag": {"description": "The current etag of the bare metal Cluster. If an etag is provided and does not match the current etag of the cluster, deletion will be blocked and an ABORTED error will be returned.", "location": "query", "type": "string"}, "force": {"description": "This is required if the cluster has any associated node pools. When set, any child node pools will also be unenrolled.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Name of the bare metal user cluster to be unenrolled. Format: \"projects/{project}/locations/{location}/bareMetalClusters/{cluster}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalClusters/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Validate the request without actually doing any updates.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}:unenroll", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"bareMetalNodePools": {"methods": {"create": {"description": "Creates a new bare metal node pool in a given project, location and Bare Metal cluster.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools", "httpMethod": "POST", "id": "gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.create", "parameterOrder": ["parent"], "parameters": {"bareMetalNodePoolId": {"description": "The ID to use for the node pool, which will become the final component of the node pool's resource name. This value must be up to 63 characters, and valid characters are /a-z-/. The value must not be permitted to be a UUID (or UUID-like: anything matching /^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$/i).", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource where this node pool will be created. projects/{project}/locations/{location}/bareMetalClusters/{cluster}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalClusters/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "If set, only validate the request, but do not actually create the node pool.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/bareMetalNodePools", "request": {"$ref": "BareMetalNodePool"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single bare metal node pool.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools/{bareMetalNodePoolsId}", "httpMethod": "DELETE", "id": "gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.delete", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "If set to true, and the bare metal node pool is not found, the request will succeed but no action will be taken on the server and return a completed LRO.", "location": "query", "type": "boolean"}, "etag": {"description": "The current etag of the BareMetalNodePool. If an etag is provided and does not match the current etag of the node pool, deletion will be blocked and an ABORTED error will be returned.", "location": "query", "type": "string"}, "ignoreErrors": {"description": "If set to true, the deletion of a bare metal node pool resource will succeed even if errors occur during deletion. This parameter can be used when you want to delete GCP's node pool resource and you've already deleted the on-prem admin cluster that hosted your node pool. WARNING: Using this parameter when your user cluster still exists may result in a deleted GCP node pool but an existing on-prem node pool.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The name of the node pool to delete. Format: projects/{project}/locations/{location}/bareMetalClusters/{cluster}/bareMetalNodePools/{nodepool}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalClusters/[^/]+/bareMetalNodePools/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "If set, only validate the request, but do not actually delete the node pool.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "enroll": {"description": "Enrolls an existing bare metal node pool to the Anthos On-Prem API within a given project and location. Through enrollment, an existing node pool will become Anthos On-Prem API managed. The corresponding GCP resources will be created.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools:enroll", "httpMethod": "POST", "id": "gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.enroll", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource where this node pool will be created. projects/{project}/locations/{location}/bareMetalClusters/{cluster}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalClusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/bareMetalNodePools:enroll", "request": {"$ref": "EnrollBareMetalNodePoolRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single bare metal node pool.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools/{bareMetalNodePoolsId}", "httpMethod": "GET", "id": "gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the node pool to retrieve. projects/{project}/locations/{location}/bareMetalClusters/{cluster}/bareMetalNodePools/{nodepool}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalClusters/[^/]+/bareMetalNodePools/[^/]+$", "required": true, "type": "string"}, "view": {"description": "View for bare metal node pool. When `BASIC` is specified, only the node pool resource name is returned. The default/unset value `NODE_POOL_VIEW_UNSPECIFIED` is the same as `FULL', which returns the complete node pool configuration details.", "enum": ["NODE_POOL_VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["If the value is not set, the default `FULL` view is used.", "Includes basic information of a node pool resource including node pool resource name.", "Includes the complete configuration for bare metal node pool resource. This is the default value for GetBareMetalNodePoolRequest method."], "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "BareMetalNodePool"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools/{bareMetalNodePoolsId}:getIamPolicy", "httpMethod": "GET", "id": "gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalClusters/[^/]+/bareMetalNodePools/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists bare metal node pools in a given project, location and bare metal cluster.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools", "httpMethod": "GET", "id": "gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of node pools to return. The service may return fewer than this value. If unspecified, at most 50 node pools will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListBareMetalNodePools` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListBareMetalNodePools` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of node pools. Format: projects/{project}/locations/{location}/bareMetalClusters/{bareMetalCluster}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalClusters/[^/]+$", "required": true, "type": "string"}, "view": {"description": "View for bare metal node pools. When `BASIC` is specified, only the node pool resource name is returned. The default/unset value `NODE_POOL_VIEW_UNSPECIFIED` is the same as `FULL', which returns the complete node pool configuration details.", "enum": ["NODE_POOL_VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["If the value is not set, the default `FULL` view is used.", "Includes basic information of a node pool resource including node pool resource name.", "Includes the complete configuration for bare metal node pool resource. This is the default value for ListBareMetalNodePoolsRequest method."], "location": "query", "type": "string"}}, "path": "v1/{+parent}/bareMetalNodePools", "response": {"$ref": "ListBareMetalNodePoolsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single bare metal node pool.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools/{bareMetalNodePoolsId}", "httpMethod": "PATCH", "id": "gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "If set to true, and the bare metal node pool is not found, the request will create a new bare metal node pool with the provided configuration. The user must have both create and update permission to call Update with allow_missing set to true.", "location": "query", "type": "boolean"}, "name": {"description": "Immutable. The bare metal node pool resource name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalClusters/[^/]+/bareMetalNodePools/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the BareMetalNodePool resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all populated fields in the BareMetalNodePool message will be updated. Empty fields will be ignored unless a field mask is used.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Validate the request without actually doing any updates.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "BareMetalNodePool"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools/{bareMetalNodePoolsId}:setIamPolicy", "httpMethod": "POST", "id": "gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalClusters/[^/]+/bareMetalNodePools/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools/{bareMetalNodePoolsId}:testIamPermissions", "httpMethod": "POST", "id": "gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalClusters/[^/]+/bareMetalNodePools/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "unenroll": {"description": "Unenrolls a bare metal node pool from Anthos On-Prem API.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools/{bareMetalNodePoolsId}:unenroll", "httpMethod": "DELETE", "id": "gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.unenroll", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "If set to true, and the bare metal node pool is not found, the request will succeed but no action will be taken on the server and return a completed LRO.", "location": "query", "type": "boolean"}, "etag": {"description": "The current etag of the bare metal node pool. If an etag is provided and does not match the current etag of node pool, deletion will be blocked and an ABORTED error will be returned.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the node pool to unenroll. Format: projects/{project}/locations/{location}/bareMetalClusters/{cluster}/bareMetalNodePools/{nodepool}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalClusters/[^/]+/bareMetalNodePools/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "If set, only validate the request, but do not actually unenroll the node pool.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}:unenroll", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools/{bareMetalNodePoolsId}/operations/{operationsId}", "httpMethod": "GET", "id": "gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalClusters/[^/]+/bareMetalNodePools/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools/{bareMetalNodePoolsId}/operations", "httpMethod": "GET", "id": "gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalClusters/[^/]+/bareMetalNodePools/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/operations/{operationsId}", "httpMethod": "GET", "id": "gkeonprem.projects.locations.bareMetalClusters.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalClusters/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/operations", "httpMethod": "GET", "id": "gkeonprem.projects.locations.bareMetalClusters.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/bareMetalClusters/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "gkeonprem.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "gkeonprem.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "gkeonprem.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "gkeonprem.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "vmwareAdminClusters": {"methods": {"create": {"description": "Creates a new VMware admin cluster in a given project and location. The API needs to be combined with creating a bootstrap cluster to work.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters", "httpMethod": "POST", "id": "gkeonprem.projects.locations.vmwareAdminClusters.create", "parameterOrder": ["parent"], "parameters": {"allowPreflightFailure": {"description": "Optional. If set to true, CLM will force CCFE to persist the cluster resource in RMS when the creation fails during standalone preflight checks. In that case the subsequent create call will fail with \"cluster already exists\" error and hence a update cluster is required to fix the cluster.", "location": "query", "type": "boolean"}, "parent": {"description": "Required. The parent of the project and location where the cluster is created in. Format: \"projects/{project}/locations/{location}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Validate the request without actually doing any updates.", "location": "query", "type": "boolean"}, "vmwareAdminClusterId": {"description": "Required. User provided identifier that is used as part of the resource name; must conform to RFC-1034 and additionally restrict to lower-cased letters. This comes out roughly to: /^a-z+[a-z0-9]$/", "location": "query", "type": "string"}}, "path": "v1/{+parent}/vmwareAdminClusters", "request": {"$ref": "VmwareAdminCluster"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "enroll": {"description": "Enrolls an existing VMware admin cluster to the Anthos On-Prem API within a given project and location. Through enrollment, an existing admin cluster will become Anthos On-Prem API managed. The corresponding GCP resources will be created and all future modifications to the cluster will be expected to be performed through the API.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters:enroll", "httpMethod": "POST", "id": "gkeonprem.projects.locations.vmwareAdminClusters.enroll", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent of the project and location where the cluster is enrolled in. Format: \"projects/{project}/locations/{location}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/vmwareAdminClusters:enroll", "request": {"$ref": "EnrollVmwareAdminClusterRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single VMware admin cluster.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters/{vmwareAdminClustersId}", "httpMethod": "GET", "id": "gkeonprem.projects.locations.vmwareAdminClusters.get", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If true, return Vmware Admin Cluster including the one that only exists in RMS.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Name of the VMware admin cluster to be returned. Format: \"projects/{project}/locations/{location}/vmwareAdminClusters/{vmware_admin_cluster}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareAdminClusters/[^/]+$", "required": true, "type": "string"}, "view": {"description": "View for VMware admin cluster. When `BASIC` is specified, only the cluster resource name and membership are returned. The default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL', which returns the complete cluster configuration details.", "enum": ["CLUSTER_VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["If the value is not set, the default `FULL` view is used.", "Includes basic information of a cluster resource including cluster resource name and membership.", "Includes the complete configuration for VMware admin cluster resource. This is the default value for GetVmwareAdminClusterRequest method."], "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "VmwareAdminCluster"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters/{vmwareAdminClustersId}:getIamPolicy", "httpMethod": "GET", "id": "gkeonprem.projects.locations.vmwareAdminClusters.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareAdminClusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists VMware admin clusters in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters", "httpMethod": "GET", "id": "gkeonprem.projects.locations.vmwareAdminClusters.list", "parameterOrder": ["parent"], "parameters": {"allowMissing": {"description": "Optional. If true, return list of Vmware Admin Clusters including the ones that only exists in RMS.", "location": "query", "type": "boolean"}, "pageSize": {"description": "Requested page size. Server may return fewer items than requested. If unspecified, at most 50 clusters will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent of the project and location where the clusters are listed in. Format: \"projects/{project}/locations/{location}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "view": {"description": "View for VMware admin clusters. When `BASIC` is specified, only the admin cluster resource name and membership are returned. The default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL', which returns the complete admin cluster configuration details.", "enum": ["CLUSTER_VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["If the value is not set, the default `FULL` view is used.", "Includes basic information of a admin cluster resource including admin cluster resource name and membership.", "Includes the complete configuration for bare metal admin cluster resource. This is the default value for ListVmwareAdminClustersRequest method."], "location": "query", "type": "string"}}, "path": "v1/{+parent}/vmwareAdminClusters", "response": {"$ref": "ListVmwareAdminClustersResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single VMware admin cluster.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters/{vmwareAdminClustersId}", "httpMethod": "PATCH", "id": "gkeonprem.projects.locations.vmwareAdminClusters.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. The VMware admin cluster resource name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareAdminClusters/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the VMwareAdminCluster resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all populated fields in the VmwareAdminCluster message will be updated. Empty fields will be ignored unless a field mask is used.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Validate the request without actually doing any updates.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "VmwareAdminCluster"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters/{vmwareAdminClustersId}:setIamPolicy", "httpMethod": "POST", "id": "gkeonprem.projects.locations.vmwareAdminClusters.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareAdminClusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters/{vmwareAdminClustersId}:testIamPermissions", "httpMethod": "POST", "id": "gkeonprem.projects.locations.vmwareAdminClusters.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareAdminClusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "unenroll": {"description": "Unenrolls an existing VMware admin cluster from the Anthos On-Prem API within a given project and location. Unenrollment removes the Cloud reference to the cluster without modifying the underlying OnPrem Resources. Clusters will continue to run; however, they will no longer be accessible through the Anthos On-Prem API or its clients.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters/{vmwareAdminClustersId}:unenroll", "httpMethod": "DELETE", "id": "gkeonprem.projects.locations.vmwareAdminClusters.unenroll", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "If set to true, and the VMware admin cluster is not found, the request will succeed but no action will be taken on the server and return a completed LRO.", "location": "query", "type": "boolean"}, "etag": {"description": "The current etag of the VMware admin cluster. If an etag is provided and does not match the current etag of the cluster, deletion will be blocked and an ABORTED error will be returned.", "location": "query", "type": "string"}, "name": {"description": "Required. Name of the VMware admin cluster to be unenrolled. Format: \"projects/{project}/locations/{location}/vmwareAdminClusters/{cluster}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareAdminClusters/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Validate the request without actually doing any updates.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}:unenroll", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters/{vmwareAdminClustersId}/operations/{operationsId}", "httpMethod": "GET", "id": "gkeonprem.projects.locations.vmwareAdminClusters.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareAdminClusters/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters/{vmwareAdminClustersId}/operations", "httpMethod": "GET", "id": "gkeonprem.projects.locations.vmwareAdminClusters.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareAdminClusters/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "vmwareClusters": {"methods": {"create": {"description": "Creates a new VMware user cluster in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters", "httpMethod": "POST", "id": "gkeonprem.projects.locations.vmwareClusters.create", "parameterOrder": ["parent"], "parameters": {"allowPreflightFailure": {"description": "Optional. If set to true, CLM will force CCFE to persist the cluster resource in RMS when the creation fails during standalone preflight checks. In that case the subsequent create call will fail with \"cluster already exists\" error and hence a update cluster is required to fix the cluster.", "location": "query", "type": "boolean"}, "parent": {"description": "Required. The parent of the project and location where this cluster is created in. Format: \"projects/{project}/locations/{location}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Validate the request without actually doing any updates.", "location": "query", "type": "boolean"}, "vmwareClusterId": {"description": "User provided identifier that is used as part of the resource name; This value must be up to 40 characters and follow RFC-1123 (https://tools.ietf.org/html/rfc1123) format.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/vmwareClusters", "request": {"$ref": "VmwareCluster"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single VMware Cluster.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}", "httpMethod": "DELETE", "id": "gkeonprem.projects.locations.vmwareClusters.delete", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "If set to true, and the VMware cluster is not found, the request will succeed but no action will be taken on the server and return a completed LRO.", "location": "query", "type": "boolean"}, "etag": {"description": "The current etag of the VMware cluster. If an etag is provided and does not match the current etag of the cluster, deletion will be blocked and an ABORTED error will be returned.", "location": "query", "type": "string"}, "force": {"description": "If set to true, any node pools from the cluster will also be deleted.", "location": "query", "type": "boolean"}, "ignoreErrors": {"description": "If set to true, the deletion of a VMware user cluster resource will succeed even if errors occur during deletion. This parameter can be used when you want to delete GCP's cluster resource and the on-prem admin cluster that hosts your user cluster is disconnected / unreachable or deleted. WARNING: Using this parameter when your user cluster still exists may result in a deleted GCP user cluster but an existing on-prem user cluster.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Name of the VMware user cluster to be deleted. Format: \"projects/{project}/locations/{location}/vmwareClusters/{vmware_cluster}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareClusters/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Validate the request without actually doing any updates.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "enroll": {"description": "Enrolls an existing VMware user cluster and its node pools to the Anthos On-Prem API within a given project and location. Through enrollment, an existing cluster will become Anthos On-Prem API managed. The corresponding GCP resources will be created and all future modifications to the cluster and/or its node pools will be expected to be performed through the API.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters:enroll", "httpMethod": "POST", "id": "gkeonprem.projects.locations.vmwareClusters.enroll", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent of the project and location where the cluster is Enrolled in. Format: \"projects/{project}/locations/{location}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/vmwareClusters:enroll", "request": {"$ref": "EnrollVmwareClusterRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single VMware Cluster.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}", "httpMethod": "GET", "id": "gkeonprem.projects.locations.vmwareClusters.get", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If true, return Vmware Cluster including the one that only exists in RMS.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Name of the VMware user cluster to be returned. Format: \"projects/{project}/locations/{location}/vmwareClusters/{vmware_cluster}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareClusters/[^/]+$", "required": true, "type": "string"}, "view": {"description": "View for VMware user cluster. When `BASIC` is specified, only the cluster resource name and admin cluster membership are returned. The default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL', which returns the complete cluster configuration details.", "enum": ["CLUSTER_VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["If the value is not set, the default `FULL` view is used.", "Includes basic information of a cluster resource including cluster resource name and admin cluster membership.", "Includes the complete configuration for VMware cluster resource. This is the default value for GetVmwareClusterRequest method."], "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "VmwareCluster"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}:getIamPolicy", "httpMethod": "GET", "id": "gkeonprem.projects.locations.vmwareClusters.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareClusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists VMware Clusters in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters", "httpMethod": "GET", "id": "gkeonprem.projects.locations.vmwareClusters.list", "parameterOrder": ["parent"], "parameters": {"allowMissing": {"description": "Optional. If true, return list of Vmware Clusters including the ones that only exists in RMS.", "location": "query", "type": "boolean"}, "filter": {"description": "A resource filtering expression following https://google.aip.dev/160. When non-empty, only resource's whose attributes field matches the filter are returned.", "location": "query", "type": "string"}, "pageSize": {"description": "Requested page size. Server may return fewer items than requested. If unspecified, at most 50 clusters will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent of the project and location where the clusters are listed in. Format: \"projects/{project}/locations/{location}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "view": {"description": "View for VMware clusters. When `BASIC` is specified, only the cluster resource name and admin cluster membership are returned. The default/unset value `CLUSTER_VIEW_UNSPECIFIED` is the same as `FULL', which returns the complete cluster configuration details.", "enum": ["CLUSTER_VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["If the value is not set, the default `FULL` view is used.", "Includes basic information of a cluster resource including cluster resource name and admin cluster membership.", "Includes the complete configuration for VMware cluster resource. This is the default value for ListVmwareClustersRequest method."], "location": "query", "type": "string"}}, "path": "v1/{+parent}/vmwareClusters", "response": {"$ref": "ListVmwareClustersResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single VMware cluster.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}", "httpMethod": "PATCH", "id": "gkeonprem.projects.locations.vmwareClusters.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. The VMware user cluster resource name.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareClusters/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the VMwareCluster resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all populated fields in the VmwareCluster message will be updated. Empty fields will be ignored unless a field mask is used.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Validate the request without actually doing any updates.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "VmwareCluster"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "queryVersionConfig": {"description": "Queries the VMware user cluster version config.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters:queryVersionConfig", "httpMethod": "POST", "id": "gkeonprem.projects.locations.vmwareClusters.queryVersionConfig", "parameterOrder": ["parent"], "parameters": {"createConfig.adminClusterMembership": {"description": "The admin cluster membership. This is the full resource name of the admin cluster's fleet membership. Format: \"projects/{project}/locations/{location}/memberships/{membership}\"", "location": "query", "type": "string"}, "createConfig.adminClusterName": {"description": "The admin cluster resource name. This is the full resource name of the admin cluster resource. Format: \"projects/{project}/locations/{location}/vmwareAdminClusters/{vmware_admin_cluster}\"", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent of the project and location to query for version config. Format: \"projects/{project}/locations/{location}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "upgradeConfig.clusterName": {"description": "The user cluster resource name. This is the full resource name of the user cluster resource. Format: \"projects/{project}/locations/{location}/vmwareClusters/{vmware_cluster}\"", "location": "query", "type": "string"}}, "path": "v1/{+parent}/vmwareClusters:queryVersionConfig", "response": {"$ref": "QueryVmwareVersionConfigResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}:setIamPolicy", "httpMethod": "POST", "id": "gkeonprem.projects.locations.vmwareClusters.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareClusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}:testIamPermissions", "httpMethod": "POST", "id": "gkeonprem.projects.locations.vmwareClusters.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareClusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "unenroll": {"description": "Unenrolls an existing VMware user cluster and its node pools from the Anthos On-Prem API within a given project and location. Unenrollment removes the Cloud reference to the cluster without modifying the underlying OnPrem Resources. Clusters and node pools will continue to run; however, they will no longer be accessible through the Anthos On-Prem API or UI.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}:unenroll", "httpMethod": "DELETE", "id": "gkeonprem.projects.locations.vmwareClusters.unenroll", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "If set to true, and the VMware cluster is not found, the request will succeed but no action will be taken on the server and return a completed LRO.", "location": "query", "type": "boolean"}, "etag": {"description": "The current etag of the VMware Cluster. If an etag is provided and does not match the current etag of the cluster, deletion will be blocked and an ABORTED error will be returned.", "location": "query", "type": "string"}, "force": {"description": "This is required if the cluster has any associated node pools. When set, any child node pools will also be unenrolled.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Name of the VMware user cluster to be unenrolled. Format: \"projects/{project}/locations/{location}/vmwareClusters/{vmware_cluster}\"", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareClusters/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "Validate the request without actually doing any updates.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}:unenroll", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/operations/{operationsId}", "httpMethod": "GET", "id": "gkeonprem.projects.locations.vmwareClusters.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareClusters/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/operations", "httpMethod": "GET", "id": "gkeonprem.projects.locations.vmwareClusters.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareClusters/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "vmwareNodePools": {"methods": {"create": {"description": "Creates a new VMware node pool in a given project, location and VMWare cluster.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools", "httpMethod": "POST", "id": "gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource where this node pool will be created. projects/{project}/locations/{location}/vmwareClusters/{cluster}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareClusters/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "If set, only validate the request, but do not actually create the node pool.", "location": "query", "type": "boolean"}, "vmwareNodePoolId": {"description": "The ID to use for the node pool, which will become the final component of the node pool's resource name. This value must be up to 40 characters and follow RFC-1123 (https://tools.ietf.org/html/rfc1123) format. The value must not be permitted to be a UUID (or UUID-like: anything matching /^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$/i).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/vmwareNodePools", "request": {"$ref": "VmwareNodePool"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single VMware node pool.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools/{vmwareNodePoolsId}", "httpMethod": "DELETE", "id": "gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.delete", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "If set to true, and the VMware node pool is not found, the request will succeed but no action will be taken on the server and return a completed LRO.", "location": "query", "type": "boolean"}, "etag": {"description": "The current etag of the VmwareNodePool. If an etag is provided and does not match the current etag of the node pool, deletion will be blocked and an ABORTED error will be returned.", "location": "query", "type": "string"}, "ignoreErrors": {"description": "If set to true, the deletion of a VMware node pool resource will succeed even if errors occur during deletion. This parameter can be used when you want to delete GCP's node pool resource and you've already deleted the on-prem admin cluster that hosted your node pool. WARNING: Using this parameter when your user cluster still exists may result in a deleted GCP node pool but an existing on-prem node pool.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The name of the node pool to delete. Format: projects/{project}/locations/{location}/vmwareClusters/{cluster}/vmwareNodePools/{nodepool}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareClusters/[^/]+/vmwareNodePools/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "If set, only validate the request, but do not actually delete the node pool.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "enroll": {"description": "Enrolls a VMware node pool to Anthos On-Prem API", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools:enroll", "httpMethod": "POST", "id": "gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.enroll", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource where the node pool is enrolled in.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareClusters/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/vmwareNodePools:enroll", "request": {"$ref": "EnrollVmwareNodePoolRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single VMware node pool.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools/{vmwareNodePoolsId}", "httpMethod": "GET", "id": "gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the node pool to retrieve. projects/{project}/locations/{location}/vmwareClusters/{cluster}/vmwareNodePools/{nodepool}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareClusters/[^/]+/vmwareNodePools/[^/]+$", "required": true, "type": "string"}, "view": {"description": "View for VMware node pool. When `BASIC` is specified, only the node pool resource name is returned. The default/unset value `NODE_POOL_VIEW_UNSPECIFIED` is the same as `FULL', which returns the complete node pool configuration details.", "enum": ["NODE_POOL_VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["If the value is not set, the default `FULL` view is used.", "Includes basic information of a node pool resource including node pool resource name.", "Includes the complete configuration for VMware node pool resource. This is the default value for GetVmwareNodePoolRequest method."], "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "VmwareNodePool"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools/{vmwareNodePoolsId}:getIamPolicy", "httpMethod": "GET", "id": "gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareClusters/[^/]+/vmwareNodePools/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists VMware node pools in a given project, location and VMWare cluster.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools", "httpMethod": "GET", "id": "gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of node pools to return. The service may return fewer than this value. If unspecified, at most 50 node pools will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListVmwareNodePools` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListVmwareNodePools` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent, which owns this collection of node pools. Format: projects/{project}/locations/{location}/vmwareClusters/{vmwareCluster}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareClusters/[^/]+$", "required": true, "type": "string"}, "view": {"description": "View for VMware node pools. When `BASIC` is specified, only the node pool resource name is returned. The default/unset value `NODE_POOL_VIEW_UNSPECIFIED` is the same as `FULL', which returns the complete node pool configuration details.", "enum": ["NODE_POOL_VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["If the value is not set, the default `FULL` view is used.", "Includes basic information of a node pool resource including node pool resource name.", "Includes the complete configuration for VMware node pool resource. This is the default value for ListVmwareNodePoolsRequest method."], "location": "query", "type": "string"}}, "path": "v1/{+parent}/vmwareNodePools", "response": {"$ref": "ListVmwareNodePoolsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single VMware node pool.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools/{vmwareNodePoolsId}", "httpMethod": "PATCH", "id": "gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Immutable. The resource name of this node pool.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareClusters/[^/]+/vmwareNodePools/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the VMwareNodePool resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all populated fields in the VMwareNodePool message will be updated. Empty fields will be ignored unless a field mask is used.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Validate the request without actually doing any updates.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "VmwareNodePool"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools/{vmwareNodePoolsId}:setIamPolicy", "httpMethod": "POST", "id": "gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareClusters/[^/]+/vmwareNodePools/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools/{vmwareNodePoolsId}:testIamPermissions", "httpMethod": "POST", "id": "gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareClusters/[^/]+/vmwareNodePools/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "unenroll": {"description": "Unenrolls a VMware node pool to Anthos On-Prem API", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools/{vmwareNodePoolsId}:unenroll", "httpMethod": "DELETE", "id": "gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.unenroll", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "If set to true, and the VMware node pool is not found, the request will succeed but no action will be taken on the server and return a completed LRO.", "location": "query", "type": "boolean"}, "etag": {"description": "The current etag of the VMware node pool. If an etag is provided and does not match the current etag of node pool, deletion will be blocked and an ABORTED error will be returned.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the node pool to unenroll. Format: projects/{project}/locations/{location}/vmwareClusters/{cluster}/vmwareNodePools/{nodepool}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareClusters/[^/]+/vmwareNodePools/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "If set, only validate the request, but do not actually unenroll the node pool.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}:unenroll", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools/{vmwareNodePoolsId}/operations/{operationsId}", "httpMethod": "GET", "id": "gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareClusters/[^/]+/vmwareNodePools/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools/{vmwareNodePoolsId}/operations", "httpMethod": "GET", "id": "gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/vmwareClusters/[^/]+/vmwareNodePools/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}}}}}, "revision": "20250423", "rootUrl": "https://gkeonprem.googleapis.com/", "schemas": {"Authorization": {"description": "Authorization defines the On-Prem cluster authorization configuration to bootstrap onto the admin cluster.", "id": "Authorization", "properties": {"adminUsers": {"description": "For VMware and bare metal user clusters, users will be granted the cluster-admin role on the cluster, which provides full administrative access to the cluster. For bare metal admin clusters, users will be granted the cluster-view role, which limits users to read-only access.", "items": {"$ref": "ClusterUser"}, "type": "array"}}, "type": "object"}, "BareMetalAdminApiServerArgument": {"description": "BareMetalAdminApiServerArgument represents an arg name->value pair. Only a subset of customized flags are supported. Please refer to the API server documentation below to know the exact format: https://kubernetes.io/docs/reference/command-line-tools-reference/kube-apiserver/", "id": "BareMetalAdminApiServerArgument", "properties": {"argument": {"description": "Required. The argument name as it appears on the API Server command line please make sure to remove the leading dashes.", "type": "string"}, "value": {"description": "Required. The value of the arg as it will be passed to the API Server command line.", "type": "string"}}, "type": "object"}, "BareMetalAdminCluster": {"description": "Resource that represents a bare metal admin cluster.", "id": "BareMetalAdminCluster", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Annotations on the bare metal admin cluster. This field has the same restrictions as Kubernetes annotations. The total size of all keys and values combined is limited to 256k. Key can have 2 segments: prefix (optional) and name (required), separated by a slash (/). Prefix must be a DNS subdomain. Name must be 63 characters or less, begin and end with alphanumerics, with dashes (-), underscores (_), dots (.), and alphanumerics between.", "type": "object"}, "bareMetalVersion": {"description": "The Anthos clusters on bare metal version for the bare metal admin cluster.", "type": "string"}, "binaryAuthorization": {"$ref": "BinaryAuthorization", "description": "Binary Authorization related configurations."}, "clusterOperations": {"$ref": "BareMetalAdminClusterOperationsConfig", "description": "Cluster operations configuration."}, "controlPlane": {"$ref": "BareMetalAdminControlPlaneConfig", "description": "Control plane configuration."}, "createTime": {"description": "Output only. The time at which this bare metal admin cluster was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deleteTime": {"description": "Output only. The time at which this bare metal admin cluster was deleted. If the resource is not deleted, this must be empty", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "A human readable description of this bare metal admin cluster.", "type": "string"}, "endpoint": {"description": "Output only. The IP address name of bare metal admin cluster's API server.", "readOnly": true, "type": "string"}, "etag": {"description": "This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding. Allows clients to perform consistent read-modify-writes through optimistic concurrency control.", "type": "string"}, "fleet": {"$ref": "Fleet", "description": "Output only. Fleet configuration for the cluster.", "readOnly": true}, "loadBalancer": {"$ref": "BareMetalAdminLoadBalancerConfig", "description": "Load balancer configuration."}, "localName": {"description": "Output only. The object name of the bare metal cluster custom resource. This field is used to support conflicting names when enrolling existing clusters to the API. When used as a part of cluster enrollment, this field will differ from the ID in the resource name. For new clusters, this field will match the user provided cluster name and be visible in the last component of the resource name. It is not modifiable. All users should use this name to access their cluster using gkectl or kubectl and should expect to see the local name when viewing admin cluster controller logs.", "readOnly": true, "type": "string"}, "maintenanceConfig": {"$ref": "BareMetalAdminMaintenanceConfig", "description": "Maintenance configuration."}, "maintenanceStatus": {"$ref": "BareMetalAdminMaintenanceStatus", "description": "Output only. MaintenanceStatus representing state of maintenance.", "readOnly": true}, "name": {"description": "Immutable. The bare metal admin cluster resource name.", "type": "string"}, "networkConfig": {"$ref": "BareMetalAdminNetworkConfig", "description": "Network configuration."}, "nodeAccessConfig": {"$ref": "BareMetalAdminNodeAccessConfig", "description": "Node access related configurations."}, "nodeConfig": {"$ref": "BareMetalAdminWorkloadNodeConfig", "description": "Workload node configuration."}, "osEnvironmentConfig": {"$ref": "BareMetalAdminOsEnvironmentConfig", "description": "OS environment related configurations."}, "proxy": {"$ref": "BareMetalAdminProxyConfig", "description": "Proxy configuration."}, "reconciling": {"description": "Output only. If set, there are currently changes in flight to the bare metal Admin Cluster.", "readOnly": true, "type": "boolean"}, "securityConfig": {"$ref": "BareMetalAdminSecurityConfig", "description": "Security related configuration."}, "state": {"description": "Output only. The current state of the bare metal admin cluster.", "enum": ["STATE_UNSPECIFIED", "PROVISIONING", "RUNNING", "RECONCILING", "STOPPING", "ERROR", "DEGRADED"], "enumDescriptions": ["Not set.", "The PROVISIONING state indicates the cluster is being created.", "The RUNNING state indicates the cluster has been created and is fully usable.", "The RECONCILING state indicates that the cluster is being updated. It remains available, but potentially with degraded performance.", "The STOPPING state indicates the cluster is being deleted.", "The ERROR state indicates the cluster is in a broken unrecoverable state.", "The DEGRADED state indicates the cluster requires user action to restore full functionality."], "readOnly": true, "type": "string"}, "status": {"$ref": "ResourceStatus", "description": "Output only. ResourceStatus representing detailed cluster status.", "readOnly": true}, "storage": {"$ref": "BareMetalAdminStorageConfig", "description": "Storage configuration."}, "uid": {"description": "Output only. The unique identifier of the bare metal admin cluster.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time at which this bare metal admin cluster was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "validationCheck": {"$ref": "ValidationCheck", "description": "Output only. ValidationCheck representing the result of the preflight check.", "readOnly": true}}, "type": "object"}, "BareMetalAdminClusterOperationsConfig": {"description": "BareMetalAdminClusterOperationsConfig specifies the admin cluster's observability infrastructure.", "id": "BareMetalAdminClusterOperationsConfig", "properties": {"enableApplicationLogs": {"description": "Whether collection of application logs/metrics should be enabled (in addition to system logs/metrics).", "type": "boolean"}}, "type": "object"}, "BareMetalAdminControlPlaneConfig": {"description": "BareMetalAdminControlPlaneConfig specifies the control plane configuration.", "id": "BareMetalAdminControlPlaneConfig", "properties": {"apiServerArgs": {"description": "Customizes the default API server args. Only a subset of customized flags are supported. Please refer to the API server documentation below to know the exact format: https://kubernetes.io/docs/reference/command-line-tools-reference/kube-apiserver/", "items": {"$ref": "BareMetalAdminApiServerArgument"}, "type": "array"}, "controlPlaneNodePoolConfig": {"$ref": "BareMetalAdminControlPlaneNodePoolConfig", "description": "Required. Configures the node pool running the control plane. If specified the corresponding NodePool will be created for the cluster's control plane. The NodePool will have the same name and namespace as the cluster."}}, "type": "object"}, "BareMetalAdminControlPlaneNodePoolConfig": {"description": "BareMetalAdminControlPlaneNodePoolConfig specifies the control plane node pool configuration. We have a control plane specific node pool config so that we can flexible about supporting control plane specific fields in the future.", "id": "BareMetalAdminControlPlaneNodePoolConfig", "properties": {"nodePoolConfig": {"$ref": "BareMetalNodePoolConfig", "description": "Required. The generic configuration for a node pool running the control plane."}}, "type": "object"}, "BareMetalAdminDrainedMachine": {"description": "BareMetalAdminDrainedMachine represents the machines that are drained.", "id": "BareMetalAdminDrainedMachine", "properties": {"nodeIp": {"description": "Drained machine IP address.", "type": "string"}}, "type": "object"}, "BareMetalAdminDrainingMachine": {"description": "BareMetalAdminDrainingMachine represents the machines that are currently draining.", "id": "BareMetalAdminDrainingMachine", "properties": {"nodeIp": {"description": "Draining machine IP address.", "type": "string"}, "podCount": {"description": "The count of pods yet to drain.", "format": "int32", "type": "integer"}}, "type": "object"}, "BareMetalAdminIslandModeCidrConfig": {"description": "BareMetalAdminIslandModeCidrConfig specifies the cluster CIDR configuration while running in island mode.", "id": "BareMetalAdminIslandModeCidrConfig", "properties": {"podAddressCidrBlocks": {"description": "Required. All pods in the cluster are assigned an RFC1918 IPv4 address from these ranges. This field cannot be changed after creation.", "items": {"type": "string"}, "type": "array"}, "serviceAddressCidrBlocks": {"description": "Required. All services in the cluster are assigned an RFC1918 IPv4 address from these ranges. This field cannot be changed after creation.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "BareMetalAdminLoadBalancerConfig": {"description": "BareMetalAdminLoadBalancerConfig specifies the load balancer configuration.", "id": "BareMetalAdminLoadBalancerConfig", "properties": {"manualLbConfig": {"$ref": "BareMetalAdminManualLbConfig", "description": "Manually configured load balancers."}, "portConfig": {"$ref": "BareMetalAdminPortConfig", "description": "Configures the ports that the load balancer will listen on."}, "vipConfig": {"$ref": "BareMetalAdminVipConfig", "description": "The VIPs used by the load balancer."}}, "type": "object"}, "BareMetalAdminMachineDrainStatus": {"description": "BareMetalAdminMachineDrainStatus represents the status of bare metal node machines that are undergoing drain operations.", "id": "BareMetalAdminMachineDrainStatus", "properties": {"drainedMachines": {"description": "The list of drained machines.", "items": {"$ref": "BareMetalAdminDrainedMachine"}, "type": "array"}, "drainingMachines": {"description": "The list of draning machines.", "items": {"$ref": "BareMetalAdminDrainingMachine"}, "type": "array"}}, "type": "object"}, "BareMetalAdminMaintenanceConfig": {"description": "BareMetalAdminMaintenanceConfig specifies configurations to put bare metal Admin cluster CRs nodes in and out of maintenance.", "id": "BareMetalAdminMaintenanceConfig", "properties": {"maintenanceAddressCidrBlocks": {"description": "Required. All IPv4 address from these ranges will be placed into maintenance mode. Nodes in maintenance mode will be cordoned and drained. When both of these are true, the \"baremetal.cluster.gke.io/maintenance\" annotation will be set on the node resource.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "BareMetalAdminMaintenanceStatus": {"description": "BareMetalAdminMaintenanceStatus represents the maintenance status for bare metal Admin cluster CR's nodes.", "id": "BareMetalAdminMaintenanceStatus", "properties": {"machineDrainStatus": {"$ref": "BareMetalAdminMachineDrainStatus", "description": "Represents the status of draining and drained machine nodes. This is used to show the progress of cluster upgrade."}}, "type": "object"}, "BareMetalAdminManualLbConfig": {"description": "BareMetalAdminManualLbConfig represents configuration parameters for a manual load balancer.", "id": "BareMetalAdminManualLbConfig", "properties": {"enabled": {"description": "Whether manual load balancing is enabled.", "type": "boolean"}}, "type": "object"}, "BareMetalAdminNetworkConfig": {"description": "BareMetalAdminNetworkConfig specifies the cluster network configuration.", "id": "BareMetalAdminNetworkConfig", "properties": {"islandModeCidr": {"$ref": "BareMetalAdminIslandModeCidrConfig", "description": "Configuration for Island mode CIDR."}}, "type": "object"}, "BareMetalAdminNodeAccessConfig": {"description": "Specifies the node access related settings for the bare metal admin cluster.", "id": "BareMetalAdminNodeAccessConfig", "properties": {"loginUser": {"description": "Required. LoginUser is the user name used to access node machines. It defaults to \"root\" if not set.", "type": "string"}}, "type": "object"}, "BareMetalAdminOsEnvironmentConfig": {"description": "Specifies operating system operation settings for cluster provisioning.", "id": "BareMetalAdminOsEnvironmentConfig", "properties": {"packageRepoExcluded": {"description": "Whether the package repo should be added when initializing bare metal machines.", "type": "boolean"}}, "type": "object"}, "BareMetalAdminPortConfig": {"description": "BareMetalAdminPortConfig is the specification of load balancer ports.", "id": "BareMetalAdminPortConfig", "properties": {"controlPlaneLoadBalancerPort": {"description": "The port that control plane hosted load balancers will listen on.", "format": "int32", "type": "integer"}}, "type": "object"}, "BareMetalAdminProxyConfig": {"description": "BareMetalAdminProxyConfig specifies the cluster proxy configuration.", "id": "BareMetalAdminProxyConfig", "properties": {"noProxy": {"description": "A list of IPs, hostnames, and domains that should skip the proxy. Examples: [\"127.0.0.1\", \"example.com\", \".corp\", \"localhost\"].", "items": {"type": "string"}, "type": "array"}, "uri": {"description": "Required. Specifies the address of your proxy server. Examples: `http://domain` WARNING: Do not provide credentials in the format `http://(username:password@)domain` these will be rejected by the server.", "type": "string"}}, "type": "object"}, "BareMetalAdminSecurityConfig": {"description": "Specifies the security related settings for the bare metal admin cluster.", "id": "BareMetalAdminSecurityConfig", "properties": {"authorization": {"$ref": "Authorization", "description": "Configures user access to the admin cluster."}}, "type": "object"}, "BareMetalAdminStorageConfig": {"description": "BareMetalAdminStorageConfig specifies the cluster storage configuration.", "id": "BareMetalAdminStorageConfig", "properties": {"lvpNodeMountsConfig": {"$ref": "BareMetalLvpConfig", "description": "Required. Specifies the config for local PersistentVolumes backed by mounted node disks. These disks need to be formatted and mounted by the user, which can be done before or after cluster creation."}, "lvpShareConfig": {"$ref": "BareMetalLvpShareConfig", "description": "Required. Specifies the config for local PersistentVolumes backed by subdirectories in a shared filesystem. These subdirectores are automatically created during cluster creation."}}, "type": "object"}, "BareMetalAdminVipConfig": {"description": "BareMetalAdminVipConfig for bare metal load balancer configurations.", "id": "BareMetalAdminVipConfig", "properties": {"controlPlaneVip": {"description": "The VIP which you previously set aside for the Kubernetes API of this bare metal admin cluster.", "type": "string"}}, "type": "object"}, "BareMetalAdminWorkloadNodeConfig": {"description": "BareMetalAdminWorkloadNodeConfig specifies the workload node configurations.", "id": "BareMetalAdminWorkloadNodeConfig", "properties": {"maxPodsPerNode": {"description": "The maximum number of pods a node can run. The size of the CIDR range assigned to the node will be derived from this parameter. By default 110 Pods are created per Node. Upper bound is 250 for both HA and non-HA admin cluster. Lower bound is 64 for non-HA admin cluster and 32 for HA admin cluster.", "format": "int64", "type": "string"}}, "type": "object"}, "BareMetalApiServerArgument": {"description": "Represents an arg name->value pair. Only a subset of customized flags are supported. For the exact format, refer to the [API server documentation](https://kubernetes.io/docs/reference/command-line-tools-reference/kube-apiserver/).", "id": "BareMetalApiServerArgument", "properties": {"argument": {"description": "Required. The argument name as it appears on the API Server command line, make sure to remove the leading dashes.", "type": "string"}, "value": {"description": "Required. The value of the arg as it will be passed to the API Server command line.", "type": "string"}}, "type": "object"}, "BareMetalBgpLbConfig": {"description": "BareMetalBgpLbConfig represents configuration parameters for a Border Gateway Protocol (BGP) load balancer.", "id": "BareMetalBgpLbConfig", "properties": {"addressPools": {"description": "Required. AddressPools is a list of non-overlapping IP pools used by load balancer typed services. All addresses must be routable to load balancer nodes. IngressVIP must be included in the pools.", "items": {"$ref": "BareMetalLoadBalancerAddressPool"}, "type": "array"}, "asn": {"description": "Required. BGP autonomous system number (ASN) of the cluster. This field can be updated after cluster creation.", "format": "int64", "type": "string"}, "bgpPeerConfigs": {"description": "Required. The list of BGP peers that the cluster will connect to. At least one peer must be configured for each control plane node. Control plane nodes will connect to these peers to advertise the control plane VIP. The Services load balancer also uses these peers by default. This field can be updated after cluster creation.", "items": {"$ref": "BareMetalBgpPeerConfig"}, "type": "array"}, "loadBalancerNodePoolConfig": {"$ref": "BareMetalLoadBalancerNodePoolConfig", "description": "Specifies the node pool running data plane load balancing. L2 connectivity is required among nodes in this pool. If missing, the control plane node pool is used for data plane load balancing."}}, "type": "object"}, "BareMetalBgpPeerConfig": {"description": "BareMetalBgpPeerConfig represents configuration parameters for a Border Gateway Protocol (BGP) peer.", "id": "BareMetalBgpPeerConfig", "properties": {"asn": {"description": "Required. BGP autonomous system number (ASN) for the network that contains the external peer device.", "format": "int64", "type": "string"}, "controlPlaneNodes": {"description": "The IP address of the control plane node that connects to the external peer. If you don't specify any control plane nodes, all control plane nodes can connect to the external peer. If you specify one or more IP addresses, only the nodes specified participate in peering sessions.", "items": {"type": "string"}, "type": "array"}, "ipAddress": {"description": "Required. The IP address of the external peer device.", "type": "string"}}, "type": "object"}, "BareMetalCluster": {"description": "Resource that represents a bare metal user cluster.", "id": "BareMetalCluster", "properties": {"adminClusterMembership": {"description": "Required. The admin cluster this bare metal user cluster belongs to. This is the full resource name of the admin cluster's fleet membership.", "type": "string"}, "adminClusterName": {"description": "Output only. The resource name of the bare metal admin cluster managing this user cluster.", "readOnly": true, "type": "string"}, "annotations": {"additionalProperties": {"type": "string"}, "description": "Annotations on the bare metal user cluster. This field has the same restrictions as Kubernetes annotations. The total size of all keys and values combined is limited to 256k. Key can have 2 segments: prefix (optional) and name (required), separated by a slash (/). Prefix must be a DNS subdomain. Name must be 63 characters or less, begin and end with alphanumerics, with dashes (-), underscores (_), dots (.), and alphanumerics between.", "type": "object"}, "bareMetalVersion": {"description": "Required. The Anthos clusters on bare metal version for your user cluster.", "type": "string"}, "binaryAuthorization": {"$ref": "BinaryAuthorization", "description": "Binary Authorization related configurations."}, "clusterOperations": {"$ref": "BareMetalClusterOperationsConfig", "description": "Cluster operations configuration."}, "controlPlane": {"$ref": "BareMetalControlPlaneConfig", "description": "Required. Control plane configuration."}, "createTime": {"description": "Output only. The time when the bare metal user cluster was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deleteTime": {"description": "Output only. The time when the bare metal user cluster was deleted. If the resource is not deleted, this must be empty", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "A human readable description of this bare metal user cluster.", "type": "string"}, "endpoint": {"description": "Output only. The IP address of the bare metal user cluster's API server.", "readOnly": true, "type": "string"}, "etag": {"description": "Output only. This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding. Allows clients to perform consistent read-modify-writes through optimistic concurrency control.", "readOnly": true, "type": "string"}, "fleet": {"$ref": "Fleet", "description": "Output only. Fleet configuration for the cluster.", "readOnly": true}, "loadBalancer": {"$ref": "BareMetalLoadBalancerConfig", "description": "Required. Load balancer configuration."}, "localName": {"description": "Output only. The object name of the bare metal user cluster custom resource on the associated admin cluster. This field is used to support conflicting names when enrolling existing clusters to the API. When used as a part of cluster enrollment, this field will differ from the name in the resource name. For new clusters, this field will match the user provided cluster name and be visible in the last component of the resource name. It is not modifiable. When the local name and cluster name differ, the local name is used in the admin cluster controller logs. You use the cluster name when accessing the cluster using bmctl and kubectl.", "readOnly": true, "type": "string"}, "localNamespace": {"description": "Output only. The namespace of the cluster.", "readOnly": true, "type": "string"}, "maintenanceConfig": {"$ref": "BareMetalMaintenanceConfig", "description": "Maintenance configuration."}, "maintenanceStatus": {"$ref": "BareMetalMaintenanceStatus", "description": "Output only. Status of on-going maintenance tasks.", "readOnly": true}, "name": {"description": "Immutable. The bare metal user cluster resource name.", "type": "string"}, "networkConfig": {"$ref": "BareMetalNetworkConfig", "description": "Required. Network configuration."}, "nodeAccessConfig": {"$ref": "BareMetalNodeAccessConfig", "description": "Node access related configurations."}, "nodeConfig": {"$ref": "BareMetalWorkloadNodeConfig", "description": "Workload node configuration."}, "osEnvironmentConfig": {"$ref": "BareMetalOsEnvironmentConfig", "description": "OS environment related configurations."}, "proxy": {"$ref": "BareMetalProxyConfig", "description": "Proxy configuration."}, "reconciling": {"description": "Output only. If set, there are currently changes in flight to the bare metal user cluster.", "readOnly": true, "type": "boolean"}, "securityConfig": {"$ref": "BareMetalSecurityConfig", "description": "Security related setting configuration."}, "state": {"description": "Output only. The current state of the bare metal user cluster.", "enum": ["STATE_UNSPECIFIED", "PROVISIONING", "RUNNING", "RECONCILING", "STOPPING", "ERROR", "DEGRADED"], "enumDescriptions": ["Not set.", "The PROVISIONING state indicates the cluster is being created.", "The RUNNING state indicates the cluster has been created and is fully usable.", "The RECONCILING state indicates that the cluster is being updated. It remains available, but potentially with degraded performance.", "The STOPPING state indicates the cluster is being deleted.", "The ERROR state indicates the cluster is in a broken unrecoverable state.", "The DEGRADED state indicates the cluster requires user action to restore full functionality."], "readOnly": true, "type": "string"}, "status": {"$ref": "ResourceStatus", "description": "Output only. Detailed cluster status.", "readOnly": true}, "storage": {"$ref": "BareMetalStorageConfig", "description": "Required. Storage configuration."}, "uid": {"description": "Output only. The unique identifier of the bare metal user cluster.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time when the bare metal user cluster was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "upgradePolicy": {"$ref": "BareMetalClusterUpgradePolicy", "description": "The cluster upgrade policy."}, "validationCheck": {"$ref": "ValidationCheck", "description": "Output only. The result of the preflight check.", "readOnly": true}}, "type": "object"}, "BareMetalClusterOperationsConfig": {"description": "Specifies the bare metal user cluster's observability infrastructure.", "id": "BareMetalClusterOperationsConfig", "properties": {"enableApplicationLogs": {"description": "Whether collection of application logs/metrics should be enabled (in addition to system logs/metrics).", "type": "boolean"}}, "type": "object"}, "BareMetalClusterUpgradePolicy": {"description": "BareMetalClusterUpgradePolicy defines the cluster upgrade policy.", "id": "BareMetalClusterUpgradePolicy", "properties": {"pause": {"description": "Output only. Pause is used to show the upgrade pause status. It's view only for now.", "readOnly": true, "type": "boolean"}, "policy": {"description": "Specifies which upgrade policy to use.", "enum": ["NODE_POOL_POLICY_UNSPECIFIED", "SERIAL", "CONCURRENT"], "enumDescriptions": ["No upgrade policy selected.", "Upgrade worker node pools sequentially.", "Upgrade all worker node pools in parallel."], "type": "string"}}, "type": "object"}, "BareMetalControlPlaneConfig": {"description": "Specifies the control plane configuration.", "id": "BareMetalControlPlaneConfig", "properties": {"apiServerArgs": {"description": "Customizes the default API server args. Only a subset of customized flags are supported. For the exact format, refer to the [API server documentation](https://kubernetes.io/docs/reference/command-line-tools-reference/kube-apiserver/).", "items": {"$ref": "BareMetalApiServerArgument"}, "type": "array"}, "controlPlaneNodePoolConfig": {"$ref": "BareMetalControlPlaneNodePoolConfig", "description": "Required. Configures the node pool running the control plane."}}, "type": "object"}, "BareMetalControlPlaneNodePoolConfig": {"description": "Specifies the control plane node pool configuration.", "id": "BareMetalControlPlaneNodePoolConfig", "properties": {"nodePoolConfig": {"$ref": "BareMetalNodePoolConfig", "description": "Required. The generic configuration for a node pool running the control plane."}}, "type": "object"}, "BareMetalDrainedMachine": {"description": "Represents a machine that is currently drained.", "id": "BareMetalDrainedMachine", "properties": {"nodeIp": {"description": "Drained machine IP address.", "type": "string"}}, "type": "object"}, "BareMetalDrainingMachine": {"description": "Represents a machine that is currently draining.", "id": "BareMetalDrainingMachine", "properties": {"nodeIp": {"description": "Draining machine IP address.", "type": "string"}, "podCount": {"description": "The count of pods yet to drain.", "format": "int32", "type": "integer"}}, "type": "object"}, "BareMetalIslandModeCidrConfig": {"description": "Specifies the cluster CIDR configuration while running in island mode.", "id": "BareMetalIslandModeCidrConfig", "properties": {"podAddressCidrBlocks": {"description": "Required. All pods in the cluster are assigned an RFC1918 IPv4 address from these ranges. This field cannot be changed after creation.", "items": {"type": "string"}, "type": "array"}, "serviceAddressCidrBlocks": {"description": "Required. All services in the cluster are assigned an RFC1918 IPv4 address from these ranges. This field is mutable after creation starting with version 1.15.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "BareMetalKubeletConfig": {"description": "KubeletConfig defines the modifiable kubelet configurations for bare metal machines. Note: this list includes fields supported in GKE (see https://cloud.google.com/kubernetes-engine/docs/how-to/node-system-config#kubelet-options).", "id": "BareMetalKubeletConfig", "properties": {"registryBurst": {"description": "The maximum size of bursty pulls, temporarily allows pulls to burst to this number, while still not exceeding registry_pull_qps. The value must not be a negative number. Updating this field may impact scalability by changing the amount of traffic produced by image pulls. Defaults to 10.", "format": "int32", "type": "integer"}, "registryPullQps": {"description": "The limit of registry pulls per second. Setting this value to 0 means no limit. Updating this field may impact scalability by changing the amount of traffic produced by image pulls. Defaults to 5.", "format": "int32", "type": "integer"}, "serializeImagePullsDisabled": {"description": "Prevents the Kubelet from pulling multiple images at a time. We recommend *not* changing the default value on nodes that run docker daemon with version < 1.9 or an Another Union File System (Aufs) storage backend. Issue https://github.com/kubernetes/kubernetes/issues/10959 has more details.", "type": "boolean"}}, "type": "object"}, "BareMetalLoadBalancerAddressPool": {"description": "Represents an IP pool used by the load balancer.", "id": "BareMetalLoadBalancerAddressPool", "properties": {"addresses": {"description": "Required. The addresses that are part of this pool. Each address must be either in the CIDR form (*******/24) or range form (*******-*******).", "items": {"type": "string"}, "type": "array"}, "avoidBuggyIps": {"description": "If true, avoid using IPs ending in .0 or .255. This avoids buggy consumer devices mistakenly dropping IPv4 traffic for those special IP addresses.", "type": "boolean"}, "manualAssign": {"description": "If true, prevent IP addresses from being automatically assigned.", "type": "boolean"}, "pool": {"description": "Required. The name of the address pool.", "type": "string"}}, "type": "object"}, "BareMetalLoadBalancerConfig": {"description": "Specifies the load balancer configuration.", "id": "BareMetalLoadBalancerConfig", "properties": {"bgpLbConfig": {"$ref": "BareMetalBgpLbConfig", "description": "Configuration for BGP typed load balancers. When set network_config.advanced_networking is automatically set to true."}, "manualLbConfig": {"$ref": "BareMetalManualLbConfig", "description": "Manually configured load balancers."}, "metalLbConfig": {"$ref": "BareMetalMetalLbConfig", "description": "Configuration for MetalLB load balancers."}, "portConfig": {"$ref": "BareMetalPortConfig", "description": "Configures the ports that the load balancer will listen on."}, "vipConfig": {"$ref": "BareMetalVipConfig", "description": "The VIPs used by the load balancer."}}, "type": "object"}, "BareMetalLoadBalancerNodePoolConfig": {"description": "Specifies the load balancer's node pool configuration.", "id": "BareMetalLoadBalancerNodePoolConfig", "properties": {"nodePoolConfig": {"$ref": "BareMetalNodePoolConfig", "description": "The generic configuration for a node pool running a load balancer."}}, "type": "object"}, "BareMetalLvpConfig": {"description": "Specifies the configs for local persistent volumes (PVs).", "id": "BareMetalLvpConfig", "properties": {"path": {"description": "Required. The host machine path.", "type": "string"}, "storageClass": {"description": "Required. The StorageClass name that PVs will be created with.", "type": "string"}}, "type": "object"}, "BareMetalLvpShareConfig": {"description": "Specifies the configs for local persistent volumes under a shared file system.", "id": "BareMetalLvpShareConfig", "properties": {"lvpConfig": {"$ref": "BareMetalLvpConfig", "description": "Required. Defines the machine path and storage class for the LVP Share."}, "sharedPathPvCount": {"description": "The number of subdirectories to create under path.", "format": "int32", "type": "integer"}}, "type": "object"}, "BareMetalMachineDrainStatus": {"description": "Represents the status of node machines that are undergoing drain operations.", "id": "BareMetalMachineDrainStatus", "properties": {"drainedMachines": {"description": "The list of drained machines.", "items": {"$ref": "BareMetalDrainedMachine"}, "type": "array"}, "drainingMachines": {"description": "The list of draning machines.", "items": {"$ref": "BareMetalDrainingMachine"}, "type": "array"}}, "type": "object"}, "BareMetalMaintenanceConfig": {"description": "Specifies configurations to put bare metal nodes in and out of maintenance.", "id": "BareMetalMaintenanceConfig", "properties": {"maintenanceAddressCidrBlocks": {"description": "Required. All IPv4 address from these ranges will be placed into maintenance mode. Nodes in maintenance mode will be cordoned and drained. When both of these are true, the \"baremetal.cluster.gke.io/maintenance\" annotation will be set on the node resource.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "BareMetalMaintenanceStatus": {"description": "Represents the maintenance status of the bare metal user cluster.", "id": "BareMetalMaintenanceStatus", "properties": {"machineDrainStatus": {"$ref": "BareMetalMachineDrainStatus", "description": "The maintenance status of node machines."}}, "type": "object"}, "BareMetalManualLbConfig": {"description": "Represents configuration parameters for a manual load balancer.", "id": "BareMetalManualLbConfig", "properties": {"enabled": {"description": "Whether manual load balancing is enabled.", "type": "boolean"}}, "type": "object"}, "BareMetalMetalLbConfig": {"description": "Represents configuration parameters for a MetalLB load balancer.", "id": "BareMetalMetalLbConfig", "properties": {"addressPools": {"description": "Required. AddressPools is a list of non-overlapping IP pools used by load balancer typed services. All addresses must be routable to load balancer nodes. IngressVIP must be included in the pools.", "items": {"$ref": "BareMetalLoadBalancerAddressPool"}, "type": "array"}, "loadBalancerNodePoolConfig": {"$ref": "BareMetalLoadBalancerNodePoolConfig", "description": "Specifies the node pool running the load balancer. L2 connectivity is required among nodes in this pool. If missing, the control plane node pool is used as the load balancer pool."}}, "type": "object"}, "BareMetalMultipleNetworkInterfacesConfig": {"description": "Specifies the multiple networking interfaces cluster configuration.", "id": "BareMetalMultipleNetworkInterfacesConfig", "properties": {"enabled": {"description": "Whether to enable multiple network interfaces for your pods. When set network_config.advanced_networking is automatically set to true.", "type": "boolean"}}, "type": "object"}, "BareMetalNetworkConfig": {"description": "Specifies the cluster network configuration.", "id": "BareMetalNetworkConfig", "properties": {"advancedNetworking": {"description": "Enables the use of advanced Anthos networking features, such as Bundled Load Balancing with BGP or the egress NAT gateway. Setting configuration for advanced networking features will automatically set this flag.", "type": "boolean"}, "islandModeCidr": {"$ref": "BareMetalIslandModeCidrConfig", "description": "Configuration for island mode CIDR. In an island-mode network, nodes have unique IP addresses, but pods don't have unique addresses across clusters. This doesn't cause problems because pods in one cluster never directly communicate with pods in another cluster. Instead, there are gateways that mediate between a pod in one cluster and a pod in another cluster."}, "multipleNetworkInterfacesConfig": {"$ref": "BareMetalMultipleNetworkInterfacesConfig", "description": "Configuration for multiple network interfaces."}, "srIovConfig": {"$ref": "BareMetalSrIovConfig", "description": "Configuration for SR-IOV."}}, "type": "object"}, "BareMetalNodeAccessConfig": {"description": "Specifies the node access related settings for the bare metal user cluster.", "id": "BareMetalNodeAccessConfig", "properties": {"loginUser": {"description": "LoginUser is the user name used to access node machines. It defaults to \"root\" if not set.", "type": "string"}}, "type": "object"}, "BareMetalNodeConfig": {"description": "BareMetalNodeConfig lists machine addresses to access Nodes.", "id": "BareMetalNodeConfig", "properties": {"labels": {"additionalProperties": {"type": "string"}, "description": "The labels assigned to this node. An object containing a list of key/value pairs. The labels here, unioned with the labels set on BareMetalNodePoolConfig are the set of labels that will be applied to the node. If there are any conflicts, the BareMetalNodeConfig labels take precedence. Example: { \"name\": \"wrench\", \"mass\": \"1.3kg\", \"count\": \"3\" }.", "type": "object"}, "nodeIp": {"description": "The default IPv4 address for SSH access and Kubernetes node. Example: ***********", "type": "string"}}, "type": "object"}, "BareMetalNodePool": {"description": "Resource that represents a bare metal node pool.", "id": "BareMetalNodePool", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Annotations on the bare metal node pool. This field has the same restrictions as Kubernetes annotations. The total size of all keys and values combined is limited to 256k. Key can have 2 segments: prefix (optional) and name (required), separated by a slash (/). Prefix must be a DNS subdomain. Name must be 63 characters or less, begin and end with alphanumerics, with dashes (-), underscores (_), dots (.), and alphanumerics between.", "type": "object"}, "createTime": {"description": "Output only. The time at which this bare metal node pool was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deleteTime": {"description": "Output only. The time at which this bare metal node pool was deleted. If the resource is not deleted, this must be empty", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "The display name for the bare metal node pool.", "type": "string"}, "etag": {"description": "This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding. Allows clients to perform consistent read-modify-writes through optimistic concurrency control.", "type": "string"}, "name": {"description": "Immutable. The bare metal node pool resource name.", "type": "string"}, "nodePoolConfig": {"$ref": "BareMetalNodePoolConfig", "description": "Required. Node pool configuration."}, "reconciling": {"description": "Output only. If set, there are currently changes in flight to the bare metal node pool.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. The current state of the bare metal node pool.", "enum": ["STATE_UNSPECIFIED", "PROVISIONING", "RUNNING", "RECONCILING", "STOPPING", "ERROR", "DEGRADED"], "enumDescriptions": ["Not set.", "The PROVISIONING state indicates the bare metal node pool is being created.", "The RUNNING state indicates the bare metal node pool has been created and is fully usable.", "The RECONCILING state indicates that the bare metal node pool is being updated. It remains available, but potentially with degraded performance.", "The STOPPING state indicates the bare metal node pool is being deleted.", "The ERROR state indicates the bare metal node pool is in a broken unrecoverable state.", "The DEGRADED state indicates the bare metal node pool requires user action to restore full functionality."], "readOnly": true, "type": "string"}, "status": {"$ref": "ResourceStatus", "description": "Output only. ResourceStatus representing the detailed node pool status.", "readOnly": true}, "uid": {"description": "Output only. The unique identifier of the bare metal node pool.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time at which this bare metal node pool was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "upgradePolicy": {"$ref": "BareMetalNodePoolUpgradePolicy", "description": "The worker node pool upgrade policy."}}, "type": "object"}, "BareMetalNodePoolConfig": {"description": "BareMetalNodePoolConfig describes the configuration of all nodes within a given bare metal node pool.", "id": "BareMetalNodePoolConfig", "properties": {"kubeletConfig": {"$ref": "BareMetalKubeletConfig", "description": "The modifiable kubelet configurations for the bare metal machines."}, "labels": {"additionalProperties": {"type": "string"}, "description": "The labels assigned to nodes of this node pool. An object containing a list of key/value pairs. Example: { \"name\": \"wrench\", \"mass\": \"1.3kg\", \"count\": \"3\" }.", "type": "object"}, "nodeConfigs": {"description": "Required. The list of machine addresses in the bare metal node pool.", "items": {"$ref": "BareMetalNodeConfig"}, "type": "array"}, "operatingSystem": {"description": "Specifies the nodes operating system (default: LINUX).", "enum": ["OPERATING_SYSTEM_UNSPECIFIED", "LINUX"], "enumDescriptions": ["No operating system runtime selected.", "Linux operating system."], "type": "string"}, "taints": {"description": "The initial taints assigned to nodes of this node pool.", "items": {"$ref": "NodeTaint"}, "type": "array"}}, "type": "object"}, "BareMetalNodePoolUpgradePolicy": {"description": "BareMetalNodePoolUpgradePolicy defines the node pool upgrade policy.", "id": "BareMetalNodePoolUpgradePolicy", "properties": {"parallelUpgradeConfig": {"$ref": "BareMetalParallelUpgradeConfig", "description": "The parallel upgrade settings for worker node pools."}}, "type": "object"}, "BareMetalOsEnvironmentConfig": {"description": "Specifies operating system settings for cluster provisioning.", "id": "BareMetalOsEnvironmentConfig", "properties": {"packageRepoExcluded": {"description": "Whether the package repo should not be included when initializing bare metal machines.", "type": "boolean"}}, "type": "object"}, "BareMetalParallelUpgradeConfig": {"description": "BareMetalParallelUpgradeConfig defines the parallel upgrade settings for worker node pools.", "id": "BareMetalParallelUpgradeConfig", "properties": {"concurrentNodes": {"description": "The maximum number of nodes that can be upgraded at once.", "format": "int32", "type": "integer"}, "minimumAvailableNodes": {"description": "The minimum number of nodes that should be healthy and available during an upgrade. If set to the default value of 0, it is possible that none of the nodes will be available during an upgrade.", "format": "int32", "type": "integer"}}, "type": "object"}, "BareMetalPortConfig": {"description": "Specifies load balancer ports for the bare metal user cluster.", "id": "BareMetalPortConfig", "properties": {"controlPlaneLoadBalancerPort": {"description": "The port that control plane hosted load balancers will listen on.", "format": "int32", "type": "integer"}}, "type": "object"}, "BareMetalProxyConfig": {"description": "Specifies the cluster proxy configuration.", "id": "BareMetalProxyConfig", "properties": {"noProxy": {"description": "A list of IPs, hostnames, and domains that should skip the proxy. Examples: [\"127.0.0.1\", \"example.com\", \".corp\", \"localhost\"].", "items": {"type": "string"}, "type": "array"}, "uri": {"description": "Required. Specifies the address of your proxy server. Examples: `http://domain` Do not provide credentials in the format `http://(username:password@)domain` these will be rejected by the server.", "type": "string"}}, "type": "object"}, "BareMetalSecurityConfig": {"description": "Specifies the security related settings for the bare metal user cluster.", "id": "BareMetalSecurityConfig", "properties": {"authorization": {"$ref": "Authorization", "description": "Configures user access to the user cluster."}}, "type": "object"}, "BareMetalSrIovConfig": {"description": "Specifies the SR-IOV networking operator config.", "id": "BareMetalSrIovConfig", "properties": {"enabled": {"description": "Whether to install the SR-IOV operator.", "type": "boolean"}}, "type": "object"}, "BareMetalStorageConfig": {"description": "BareMetalStorageConfig specifies the cluster storage configuration.", "id": "BareMetalStorageConfig", "properties": {"lvpNodeMountsConfig": {"$ref": "BareMetalLvpConfig", "description": "Required. Specifies the config for local PersistentVolumes backed by mounted node disks. These disks need to be formatted and mounted by the user, which can be done before or after cluster creation."}, "lvpShareConfig": {"$ref": "BareMetalLvpShareConfig", "description": "Required. Specifies the config for local PersistentVolumes backed by subdirectories in a shared filesystem. These subdirectores are automatically created during cluster creation."}}, "type": "object"}, "BareMetalVersionInfo": {"description": "Contains information about a specific Anth<PERSON> on bare metal version.", "id": "BareMetalVersionInfo", "properties": {"dependencies": {"description": "The list of upgrade dependencies for this version.", "items": {"$ref": "UpgradeDependency"}, "type": "array"}, "hasDependencies": {"description": "If set, the cluster dependencies (e.g. the admin cluster, other user clusters managed by the same admin cluster, version skew policy, etc) must be upgraded before this version can be installed or upgraded to.", "type": "boolean"}, "version": {"description": "Version number e.g. 1.13.1.", "type": "string"}}, "type": "object"}, "BareMetalVipConfig": {"description": "Specifies the VIP config for the bare metal load balancer.", "id": "BareMetalVipConfig", "properties": {"controlPlaneVip": {"description": "The VIP which you previously set aside for the Kubernetes API of this bare metal user cluster.", "type": "string"}, "ingressVip": {"description": "The VIP which you previously set aside for ingress traffic into this bare metal user cluster.", "type": "string"}}, "type": "object"}, "BareMetalWorkloadNodeConfig": {"description": "Specifies the workload node configurations.", "id": "BareMetalWorkloadNodeConfig", "properties": {"containerRuntime": {"description": "Specifies which container runtime will be used.", "enum": ["CONTAINER_RUNTIME_UNSPECIFIED", "CONTAINERD"], "enumDescriptions": ["No container runtime selected.", "Containerd runtime."], "type": "string"}, "maxPodsPerNode": {"description": "The maximum number of pods a node can run. The size of the CIDR range assigned to the node will be derived from this parameter.", "format": "int64", "type": "string"}}, "type": "object"}, "BinaryAuthorization": {"description": "Configuration for Binary Authorization.", "id": "BinaryAuthorization", "properties": {"evaluationMode": {"description": "Mode of operation for binauthz policy evaluation. If unspecified, defaults to DISABLED.", "enum": ["EVALUATION_MODE_UNSPECIFIED", "DISABLED", "PROJECT_SINGLETON_POLICY_ENFORCE"], "enumDescriptions": ["Default value", "Disable BinaryAuthorization", "Enforce Kubernetes admission requests with BinaryAuthorization using the project's singleton policy."], "type": "string"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "ClusterUser": {"description": "ClusterUser configures user principals for an RBAC policy.", "id": "ClusterUser", "properties": {"username": {"description": "Required. The name of the user, e.g. `<EMAIL>`.", "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "EnrollBareMetalAdminClusterRequest": {"description": "Message for enrolling an existing bare metal admin cluster to the GKE on-prem API.", "id": "EnrollBareMetalAdminClusterRequest", "properties": {"bareMetalAdminClusterId": {"description": "User provided OnePlatform identifier that is used as part of the resource name. This must be unique among all GKE on-prem clusters within a project and location and will return a 409 if the cluster already exists. (https://tools.ietf.org/html/rfc1123) format.", "type": "string"}, "membership": {"description": "Required. This is the full resource name of this admin cluster's fleet membership.", "type": "string"}}, "type": "object"}, "EnrollBareMetalClusterRequest": {"description": "Message for enrolling an existing bare metal cluster to the Anthos On-Prem API.", "id": "EnrollBareMetalClusterRequest", "properties": {"adminClusterMembership": {"description": "Required. The admin cluster this bare metal user cluster belongs to. This is the full resource name of the admin cluster's fleet membership. In the future, references to other resource types might be allowed if admin clusters are modeled as their own resources.", "type": "string"}, "bareMetalClusterId": {"description": "User provided OnePlatform identifier that is used as part of the resource name. This must be unique among all bare metal clusters within a project and location and will return a 409 if the cluster already exists. (https://tools.ietf.org/html/rfc1123) format.", "type": "string"}, "localName": {"description": "Optional. The object name of the bare metal cluster custom resource on the associated admin cluster. This field is used to support conflicting resource names when enrolling existing clusters to the API. When not provided, this field will resolve to the bare_metal_cluster_id. Otherwise, it must match the object name of the bare metal cluster custom resource. It is not modifiable outside / beyond the enrollment operation.", "type": "string"}}, "type": "object"}, "EnrollBareMetalNodePoolRequest": {"description": "Message for enrolling an existing bare metal node pool to the GKE on-prem API.", "id": "EnrollBareMetalNodePoolRequest", "properties": {"bareMetalNodePoolId": {"description": "User provided OnePlatform identifier that is used as part of the resource name. (https://tools.ietf.org/html/rfc1123) format.", "type": "string"}, "validateOnly": {"description": "If set, only validate the request, but do not actually enroll the node pool.", "type": "boolean"}}, "type": "object"}, "EnrollVmwareAdminClusterRequest": {"description": "Message for enrolling an existing VMware admin cluster to the GKE on-prem API.", "id": "EnrollVmwareAdminClusterRequest", "properties": {"membership": {"description": "Required. This is the full resource name of this admin cluster's fleet membership.", "type": "string"}, "vmwareAdminClusterId": {"description": "User provided OnePlatform identifier that is used as part of the resource name. This must be unique among all GKE on-prem clusters within a project and location and will return a 409 if the cluster already exists. (https://tools.ietf.org/html/rfc1123) format.", "type": "string"}}, "type": "object"}, "EnrollVmwareClusterRequest": {"description": "Message for enrolling an existing VMware cluster to the Anthos On-Prem API.", "id": "EnrollVmwareClusterRequest", "properties": {"adminClusterMembership": {"description": "Required. The admin cluster this VMware user cluster belongs to. This is the full resource name of the admin cluster's fleet membership. In the future, references to other resource types might be allowed if admin clusters are modeled as their own resources.", "type": "string"}, "localName": {"description": "Optional. The object name of the VMware OnPremUserCluster custom resource on the associated admin cluster. This field is used to support conflicting resource names when enrolling existing clusters to the API. When not provided, this field will resolve to the vmware_cluster_id. Otherwise, it must match the object name of the VMware OnPremUserCluster custom resource. It is not modifiable outside / beyond the enrollment operation.", "type": "string"}, "validateOnly": {"description": "Validate the request without actually doing any updates.", "type": "boolean"}, "vmwareClusterId": {"description": "User provided OnePlatform identifier that is used as part of the resource name. This must be unique among all GKE on-prem clusters within a project and location and will return a 409 if the cluster already exists. (https://tools.ietf.org/html/rfc1123) format.", "type": "string"}}, "type": "object"}, "EnrollVmwareNodePoolRequest": {"description": "Message for enrolling a VMware node pool.", "id": "EnrollVmwareNodePoolRequest", "properties": {"vmwareNodePoolId": {"description": "The target node pool id to be enrolled.", "type": "string"}}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "Fleet": {"description": "Fleet related configuration. Fleets are a Google Cloud concept for logically organizing clusters, letting you use and manage multi-cluster capabilities and apply consistent policies across your systems. See [Anthos Fleets](`https://cloud.google.com/anthos/multicluster-management/fleets`) for more details on Anthos multi-cluster capabilities using Fleets. ##", "id": "Fleet", "properties": {"membership": {"description": "Output only. The name of the managed fleet Membership resource associated to this cluster. Membership names are formatted as `projects//locations//memberships/`.", "readOnly": true, "type": "string"}}, "type": "object"}, "ListBareMetalAdminClustersResponse": {"description": "Response message for listing bare metal admin clusters.", "id": "ListBareMetalAdminClustersResponse", "properties": {"bareMetalAdminClusters": {"description": "The list of bare metal admin cluster.", "items": {"$ref": "BareMetalAdminCluster"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return. If the token is not empty this means that more results are available and should be retrieved by repeating the request with the provided page token.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListBareMetalClustersResponse": {"description": "Response message for listing bare metal Clusters.", "id": "ListBareMetalClustersResponse", "properties": {"bareMetalClusters": {"description": "The list of bare metal Clusters.", "items": {"$ref": "BareMetalCluster"}, "type": "array"}, "nextPageToken": {"description": "A token identifying a page of results the server should return. If the token is not empty this means that more results are available and should be retrieved by repeating the request with the provided page token.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListBareMetalNodePoolsResponse": {"description": "Response message for listing bare metal node pools.", "id": "ListBareMetalNodePoolsResponse", "properties": {"bareMetalNodePools": {"description": "The node pools from the specified parent resource.", "items": {"$ref": "BareMetalNodePool"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListVmwareAdminClustersResponse": {"description": "Response message for listing VMware admin clusters.", "id": "ListVmwareAdminClustersResponse", "properties": {"nextPageToken": {"description": "A token identifying a page of results the server should return. If the token is not empty this means that more results are available and should be retrieved by repeating the request with the provided page token.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}, "vmwareAdminClusters": {"description": "The list of VMware admin cluster.", "items": {"$ref": "VmwareAdminCluster"}, "type": "array"}}, "type": "object"}, "ListVmwareClustersResponse": {"description": "Response message for listing VMware Clusters.", "id": "ListVmwareClustersResponse", "properties": {"nextPageToken": {"description": "A token identifying a page of results the server should return. If the token is not empty this means that more results are available and should be retrieved by repeating the request with the provided page token.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}, "vmwareClusters": {"description": "The list of VMware Cluster.", "items": {"$ref": "VmwareCluster"}, "type": "array"}}, "type": "object"}, "ListVmwareNodePoolsResponse": {"description": "Response message for listing VMware node pools.", "id": "ListVmwareNodePoolsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}, "vmwareNodePools": {"description": "The node pools from the specified parent resource.", "items": {"$ref": "VmwareNodePool"}, "type": "array"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "Metric": {"description": "Progress metric is (string, int|float|string) pair.", "id": "Metric", "properties": {"doubleValue": {"description": "For metrics with floating point value.", "format": "double", "type": "number"}, "intValue": {"description": "For metrics with integer value.", "format": "int64", "type": "string"}, "metric": {"description": "Required. The metric name.", "enum": ["METRIC_ID_UNSPECIFIED", "NODES_TOTAL", "NODES_DRAINING", "NODES_UPGRADING", "NODES_PENDING_UPGRADE", "NODES_UPGRADED", "NODES_FAILED", "NODES_HEALTHY", "NODES_RECONCILING", "NODES_IN_MAINTENANCE", "PREFLIGHTS_COMPLETED", "PREFLIGHTS_RUNNING", "PREFLIGHTS_FAILED", "PREFLIGHTS_TOTAL"], "enumDescriptions": ["Not set.", "The total number of nodes being actuated.", "The number of nodes draining.", "The number of nodes actively upgrading.", "The number of nodes to be upgraded.", "The number of nodes upgraded.", "The number of nodes to fail actuation.", "The number of nodes healthy.", "The number of nodes reconciling.", "The number of nodes in maintenance mode.", "The number of completed preflight checks.", "The number of preflight checks running.", "The number of preflight checks failed.", "The total number of preflight checks."], "type": "string"}, "stringValue": {"description": "For metrics with custom values (ratios, visual progress, etc.).", "type": "string"}}, "type": "object"}, "NodeTaint": {"description": "NodeTaint applied to every Kubernetes node in a node pool. Kubernetes taints can be used together with tolerations to control how workloads are scheduled to your nodes. Node taints are permanent.", "id": "NodeTaint", "properties": {"effect": {"description": "The taint effect.", "enum": ["EFFECT_UNSPECIFIED", "NO_SCHEDULE", "PREFER_NO_SCHEDULE", "NO_EXECUTE"], "enumDescriptions": ["Not set.", "Do not allow new pods to schedule onto the node unless they tolerate the taint, but allow all pods submitted to Ku<PERSON><PERSON> without going through the scheduler to start, and allow all already-running pods to continue running. Enforced by the scheduler.", "Like TaintEffectNoSchedule, but the scheduler tries not to schedule new pods onto the node, rather than prohibiting new pods from scheduling onto the node entirely. Enforced by the scheduler.", "Evict any already-running pods that do not tolerate the taint. Currently enforced by NodeController."], "type": "string"}, "key": {"description": "Key associated with the effect.", "type": "string"}, "value": {"description": "Value associated with the effect.", "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "controlPlaneDisconnected": {"description": "Output only. Denotes if the local managing cluster's control plane is currently disconnected. This is expected to occur temporarily during self-managed cluster upgrades.", "readOnly": true, "type": "boolean"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "progress": {"$ref": "OperationProgress", "description": "Output only. Detailed progress information for the operation.", "readOnly": true}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have [Operation.error] value with a [google.rpc.Status.code] of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "type": {"description": "Output only. Type of operation being executed.", "enum": ["OPERATION_TYPE_UNSPECIFIED", "CREATE", "DELETE", "UPDATE", "UPGRADE", "PLATFORM_UPGRADE"], "enumDescriptions": ["Not set.", "The resource is being created.", "The resource is being deleted.", "The resource is being updated.", "The resource is being upgraded.", "The platform is being upgraded."], "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "OperationProgress": {"description": "Information about operation progress.", "id": "OperationProgress", "properties": {"stages": {"description": "The stages of the operation.", "items": {"$ref": "OperationStage"}, "type": "array"}}, "type": "object"}, "OperationStage": {"description": "Information about a particular stage of an operation.", "id": "OperationStage", "properties": {"endTime": {"description": "Time the stage ended.", "format": "google-datetime", "type": "string"}, "metrics": {"description": "Progress metric bundle.", "items": {"$ref": "Metric"}, "type": "array"}, "stage": {"description": "The high-level stage of the operation.", "enum": ["STAGE_UNSPECIFIED", "PREFLIGHT_CHECK", "CONFIGURE", "DEPLOY", "HEALTH_CHECK", "UPDATE"], "enumDescriptions": ["Not set.", "Preflight checks are running.", "Resource is being configured.", "Resource is being deployed.", "Waiting for the resource to become healthy.", "Resource is being updated."], "type": "string"}, "startTime": {"description": "Time the stage started.", "format": "google-datetime", "type": "string"}, "state": {"description": "Output only. State of the stage.", "enum": ["STATE_UNSPECIFIED", "PENDING", "RUNNING", "SUCCEEDED", "FAILED"], "enumDescriptions": ["Not set.", "The stage is pending.", "The stage is running", "The stage has completed successfully.", "The stage has failed."], "readOnly": true, "type": "string"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "QueryBareMetalAdminVersionConfigResponse": {"description": "Response message for querying bare metal admin cluster version config.", "id": "QueryBareMetalAdminVersionConfigResponse", "properties": {"versions": {"description": "List of available versions to install or to upgrade to.", "items": {"$ref": "BareMetalVersionInfo"}, "type": "array"}}, "type": "object"}, "QueryBareMetalVersionConfigResponse": {"description": "Response message for querying bare metal admin cluster version config.", "id": "QueryBareMetalVersionConfigResponse", "properties": {"versions": {"description": "List of available versions to install or to upgrade to.", "items": {"$ref": "BareMetalVersionInfo"}, "type": "array"}}, "type": "object"}, "QueryVmwareVersionConfigResponse": {"description": "Response message for querying VMware user cluster version config.", "id": "QueryVmwareVersionConfigResponse", "properties": {"versions": {"description": "List of available versions to install or to upgrade to.", "items": {"$ref": "VmwareVersionInfo"}, "type": "array"}}, "type": "object"}, "ResourceCondition": {"description": "ResourceCondition provides a standard mechanism for higher-level status reporting from controller.", "id": "ResourceCondition", "properties": {"lastTransitionTime": {"description": "Last time the condition transit from one status to another.", "format": "google-datetime", "type": "string"}, "message": {"description": "Human-readable message indicating details about last transition.", "type": "string"}, "reason": {"description": "Machine-readable message indicating details about last transition.", "type": "string"}, "state": {"description": "state of the condition.", "enum": ["STATE_UNSPECIFIED", "STATE_TRUE", "STATE_FALSE", "STATE_UNKNOWN"], "enumDescriptions": ["Not set.", "Resource is in the condition.", "Resource is not in the condition.", "Kubernetes controller can't decide if the resource is in the condition or not."], "type": "string"}, "type": {"description": "Type of the condition. (e.g., ClusterRunning, NodePoolRunning or ServerSidePreflightReady)", "type": "string"}}, "type": "object"}, "ResourceStatus": {"description": "ResourceStatus describes why a cluster or node pool has a certain status. (e.g., ERROR or DEGRADED).", "id": "ResourceStatus", "properties": {"conditions": {"description": "ResourceCondition provide a standard mechanism for higher-level status reporting from controller.", "items": {"$ref": "ResourceCondition"}, "type": "array"}, "errorMessage": {"description": "Human-friendly representation of the error message from controller. The error message can be temporary as the controller controller creates a cluster or node pool. If the error message persists for a longer period of time, it can be used to surface error message to indicate real problems requiring user intervention.", "type": "string"}, "version": {"description": "Reflect current version of the resource.", "type": "string"}, "versions": {"$ref": "Versions", "description": "Shows the mapping of a given version to the number of machines under this version."}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "UpgradeDependency": {"description": "UpgradeDependency represents a dependency when upgrading a resource.", "id": "UpgradeDependency", "properties": {"currentVersion": {"description": "Current version of the dependency e.g. 1.15.0.", "type": "string"}, "membership": {"description": "Membership names are formatted as `projects//locations//memberships/`.", "type": "string"}, "resourceName": {"description": "Resource name of the dependency.", "type": "string"}, "targetVersion": {"description": "Target version of the dependency e.g. 1.16.1. This is the version the dependency needs to be upgraded to before a resource can be upgraded.", "type": "string"}}, "type": "object"}, "ValidationCheck": {"description": "ValidationCheck represents the result of preflight check.", "id": "ValidationCheck", "properties": {"option": {"description": "Options used for the validation check", "enum": ["OPTIONS_UNSPECIFIED", "SKIP_VALIDATION_CHECK_BLOCKING", "SKIP_VALIDATION_ALL"], "enumDescriptions": ["Default value. Standard preflight validation check will be used.", "Prevent failed preflight checks from failing.", "Skip all preflight check validations."], "type": "string"}, "scenario": {"description": "Output only. The scenario when the preflight checks were run.", "enum": ["SCENARIO_UNSPECIFIED", "CREATE", "UPDATE"], "enumDescriptions": ["Default value. This value is unused.", "The validation check occurred during a create flow.", "The validation check occurred during an update flow."], "readOnly": true, "type": "string"}, "status": {"$ref": "ValidationCheckStatus", "description": "Output only. The detailed validation check status.", "readOnly": true}}, "type": "object"}, "ValidationCheckResult": {"description": "ValidationCheckResult defines the details about the validation check.", "id": "ValidationCheckResult", "properties": {"category": {"description": "The category of the validation.", "type": "string"}, "description": {"description": "The description of the validation check.", "type": "string"}, "details": {"description": "Detailed failure information, which might be unformatted.", "type": "string"}, "reason": {"description": "A human-readable message of the check failure.", "type": "string"}, "state": {"description": "The validation check state.", "enum": ["STATE_UNKNOWN", "STATE_FAILURE", "STATE_SKIPPED", "STATE_FATAL", "STATE_WARNING"], "enumDescriptions": ["The default value. The check result is unknown.", "The check failed.", "The check was skipped.", "The check itself failed to complete.", "The check encountered a warning."], "type": "string"}}, "type": "object"}, "ValidationCheckStatus": {"description": "ValidationCheckStatus defines the detailed validation check status.", "id": "ValidationCheckStatus", "properties": {"result": {"description": "Individual checks which failed as part of the Preflight check execution.", "items": {"$ref": "ValidationCheckResult"}, "type": "array"}}, "type": "object"}, "Version": {"description": "Version describes the number of nodes at a given version under a resource.", "id": "Version", "properties": {"count": {"description": "Number of machines under the above version.", "format": "int64", "type": "string"}, "version": {"description": "Resource version.", "type": "string"}}, "type": "object"}, "Versions": {"description": "Versions describes the mapping of a given version to the number of machines under this version.", "id": "Versions", "properties": {"versions": {"description": "Shows the mapping of a given version to the number of machines under this version.", "items": {"$ref": "Version"}, "type": "array"}}, "type": "object"}, "VmwareAAGConfig": {"description": "Specifies anti affinity group config for the VMware user cluster.", "id": "VmwareAAGConfig", "properties": {"aagConfigDisabled": {"description": "Spread nodes across at least three physical hosts (requires at least three hosts). Enabled by default.", "type": "boolean"}}, "type": "object"}, "VmwareAddressPool": {"description": "Represents an IP pool used by the load balancer.", "id": "VmwareAddressPool", "properties": {"addresses": {"description": "Required. The addresses that are part of this pool. Each address must be either in the CIDR form (*******/24) or range form (*******-*******).", "items": {"type": "string"}, "type": "array"}, "avoidBuggyIps": {"description": "If true, avoid using IPs ending in .0 or .255. This avoids buggy consumer devices mistakenly dropping IPv4 traffic for those special IP addresses.", "type": "boolean"}, "manualAssign": {"description": "If true, prevent IP addresses from being automatically assigned.", "type": "boolean"}, "pool": {"description": "Required. The name of the address pool.", "type": "string"}}, "type": "object"}, "VmwareAdminAddonNodeConfig": {"description": "VmwareAdminAddonNodeConfig contains add-on node configurations for VMware admin cluster.", "id": "VmwareAdminAddonNodeConfig", "properties": {"autoResizeConfig": {"$ref": "VmwareAutoResizeConfig", "description": "VmwareAutoResizeConfig config specifies auto resize config."}}, "type": "object"}, "VmwareAdminAuthorizationConfig": {"description": "VmwareAdminAuthorizationConfig represents configuration for admin cluster authorization.", "id": "VmwareAdminAuthorizationConfig", "properties": {"viewerUsers": {"description": "For VMware admin clusters, users will be granted the cluster-viewer role on the cluster.", "items": {"$ref": "ClusterUser"}, "type": "array"}}, "type": "object"}, "VmwareAdminCluster": {"description": "Resource that represents a VMware admin cluster.", "id": "VmwareAdminCluster", "properties": {"addonNode": {"$ref": "VmwareAdminAddonNodeConfig", "description": "The VMware admin cluster addon node configuration."}, "annotations": {"additionalProperties": {"type": "string"}, "description": "Annotations on the VMware admin cluster. This field has the same restrictions as Kubernetes annotations. The total size of all keys and values combined is limited to 256k. Key can have 2 segments: prefix (optional) and name (required), separated by a slash (/). Prefix must be a DNS subdomain. Name must be 63 characters or less, begin and end with alphanumerics, with dashes (-), underscores (_), dots (.), and alphanumerics between.", "type": "object"}, "antiAffinityGroups": {"$ref": "VmwareAAGConfig", "description": "The VMware admin cluster anti affinity group configuration."}, "authorization": {"$ref": "VmwareAdminAuthorizationConfig", "description": "The VMware admin cluster authorization configuration."}, "autoRepairConfig": {"$ref": "VmwareAutoRepairConfig", "description": "The VMware admin cluster auto repair configuration."}, "bootstrapClusterMembership": {"description": "The bootstrap cluster this VMware admin cluster belongs to.", "type": "string"}, "controlPlaneNode": {"$ref": "VmwareAdminControlPlaneNodeConfig", "description": "The VMware admin cluster control plane node configuration."}, "createTime": {"description": "Output only. The time at which VMware admin cluster was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "A human readable description of this VMware admin cluster.", "type": "string"}, "enableAdvancedCluster": {"description": "Enable advanced cluster.", "type": "boolean"}, "endpoint": {"description": "Output only. The DNS name of VMware admin cluster's API server.", "readOnly": true, "type": "string"}, "etag": {"description": "This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding. Allows clients to perform consistent read-modify-writes through optimistic concurrency control.", "type": "string"}, "fleet": {"$ref": "Fleet", "description": "Output only. Fleet configuration for the cluster.", "readOnly": true}, "imageType": {"description": "The OS image type for the VMware admin cluster.", "type": "string"}, "loadBalancer": {"$ref": "VmwareAdminLoadBalancerConfig", "description": "The VMware admin cluster load balancer configuration."}, "localName": {"description": "Output only. The object name of the VMware OnPremAdminCluster custom resource. This field is used to support conflicting names when enrolling existing clusters to the API. When used as a part of cluster enrollment, this field will differ from the ID in the resource name. For new clusters, this field will match the user provided cluster name and be visible in the last component of the resource name. It is not modifiable. All users should use this name to access their cluster using gkectl or kubectl and should expect to see the local name when viewing admin cluster controller logs.", "readOnly": true, "type": "string"}, "name": {"description": "Immutable. The VMware admin cluster resource name.", "type": "string"}, "networkConfig": {"$ref": "VmwareAdminNetworkConfig", "description": "The VMware admin cluster network configuration."}, "onPremVersion": {"description": "The Anthos clusters on the VMware version for the admin cluster.", "type": "string"}, "platformConfig": {"$ref": "VmwarePlatformConfig", "description": "The VMware platform configuration."}, "preparedSecrets": {"$ref": "VmwareAdminPreparedSecretsConfig", "description": "Output only. The VMware admin cluster prepared secrets configuration. It should always be enabled by the Central API, instead of letting users set it.", "readOnly": true}, "reconciling": {"description": "Output only. If set, there are currently changes in flight to the VMware admin cluster.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. The current state of VMware admin cluster.", "enum": ["STATE_UNSPECIFIED", "PROVISIONING", "RUNNING", "RECONCILING", "STOPPING", "ERROR", "DEGRADED"], "enumDescriptions": ["Not set.", "The PROVISIONING state indicates the cluster is being created.", "The RUNNING state indicates the cluster has been created and is fully usable.", "The RECONCILING state indicates that the cluster is being updated. It remains available, but potentially with degraded performance.", "The STOPPING state indicates the cluster is being deleted.", "The ERROR state indicates the cluster is in a broken unrecoverable state.", "The DEGRADED state indicates the cluster requires user action to restore full functionality."], "readOnly": true, "type": "string"}, "status": {"$ref": "ResourceStatus", "description": "Output only. ResourceStatus representing detailed cluster state.", "readOnly": true}, "uid": {"description": "Output only. The unique identifier of the VMware admin cluster.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time at which VMware admin cluster was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "validationCheck": {"$ref": "ValidationCheck", "description": "Output only. ValidationCheck represents the result of the preflight check job.", "readOnly": true}, "vcenter": {"$ref": "VmwareAdminVCenterConfig", "description": "The VMware admin cluster VCenter configuration."}}, "type": "object"}, "VmwareAdminControlPlaneNodeConfig": {"description": "VmwareAdminControlPlaneNodeConfig contains control plane node configuration for VMware admin cluster.", "id": "VmwareAdminControlPlaneNodeConfig", "properties": {"cpus": {"description": "The number of vCPUs for the control-plane node of the admin cluster.", "format": "int64", "type": "string"}, "memory": {"description": "The number of mebibytes of memory for the control-plane node of the admin cluster.", "format": "int64", "type": "string"}, "replicas": {"description": "The number of control plane nodes for this VMware admin cluster. (default: 1 replica).", "format": "int64", "type": "string"}}, "type": "object"}, "VmwareAdminF5BigIpConfig": {"description": "VmwareAdminF5BigIpConfig represents configuration parameters for an F5 BIG-IP load balancer.", "id": "VmwareAdminF5BigIpConfig", "properties": {"address": {"description": "The load balancer's IP address.", "type": "string"}, "partition": {"description": "The preexisting partition to be used by the load balancer. This partition is usually created for the admin cluster for example: 'my-f5-admin-partition'.", "type": "string"}, "snatPool": {"description": "The pool name. Only necessary, if using SNAT.", "type": "string"}}, "type": "object"}, "VmwareAdminHAControlPlaneConfig": {"description": "Specifies HA admin control plane config.", "id": "VmwareAdminHAControlPlaneConfig", "properties": {"controlPlaneIpBlock": {"$ref": "VmwareIpBlock", "description": "Static IP addresses for the admin control plane nodes."}}, "type": "object"}, "VmwareAdminLoadBalancerConfig": {"description": "VmwareAdminLoadBalancerConfig contains load balancer configuration for VMware admin cluster.", "id": "VmwareAdminLoadBalancerConfig", "properties": {"f5Config": {"$ref": "VmwareAdminF5BigIpConfig", "description": "Configuration for F5 Big IP typed load balancers."}, "manualLbConfig": {"$ref": "VmwareAdminManualLbConfig", "description": "Manually configured load balancers."}, "metalLbConfig": {"$ref": "VmwareAdminMetalLbConfig", "description": "MetalLB load balancers."}, "seesawConfig": {"$ref": "VmwareAdminSeesawConfig", "description": "Output only. Configuration for Seesaw typed load balancers.", "readOnly": true}, "vipConfig": {"$ref": "VmwareAdminVipConfig", "description": "The VIPs used by the load balancer."}}, "type": "object"}, "VmwareAdminManualLbConfig": {"id": "VmwareAdminManualLbConfig", "properties": {"addonsNodePort": {"description": "NodePort for add-ons server in the admin cluster.", "format": "int32", "type": "integer"}, "controlPlaneNodePort": {"description": "NodePort for control plane service. The Kubernetes API server in the admin cluster is implemented as a Service of type NodePort (ex. 30968).", "format": "int32", "type": "integer"}, "ingressHttpNodePort": {"description": "NodePort for ingress service's http. The ingress service in the admin cluster is implemented as a Service of type NodePort (ex. 32527).", "format": "int32", "type": "integer"}, "ingressHttpsNodePort": {"description": "NodePort for ingress service's https. The ingress service in the admin cluster is implemented as a Service of type NodePort (ex. 30139).", "format": "int32", "type": "integer"}, "konnectivityServerNodePort": {"description": "NodePort for konnectivity server service running as a sidecar in each kube-apiserver pod (ex. 30564).", "format": "int32", "type": "integer"}}, "type": "object"}, "VmwareAdminMetalLbConfig": {"description": "VmwareAdminMetalLbConfig represents configuration parameters for a MetalLB load balancer. For admin clusters, currently no configurations is needed.", "id": "VmwareAdminMetalLbConfig", "properties": {"enabled": {"description": "Whether MetalLB is enabled.", "type": "boolean"}}, "type": "object"}, "VmwareAdminNetworkConfig": {"description": "VmwareAdminNetworkConfig contains network configuration for VMware admin cluster.", "id": "VmwareAdminNetworkConfig", "properties": {"dhcpIpConfig": {"$ref": "VmwareDhcpIpConfig", "description": "Configuration settings for a DHCP IP configuration."}, "haControlPlaneConfig": {"$ref": "VmwareAdminHAControlPlaneConfig", "description": "Configuration for HA admin cluster control plane."}, "hostConfig": {"$ref": "VmwareHostConfig", "description": "Represents common network settings irrespective of the host's IP address."}, "podAddressCidrBlocks": {"description": "Required. All pods in the cluster are assigned an RFC1918 IPv4 address from these ranges. Only a single range is supported. This field cannot be changed after creation.", "items": {"type": "string"}, "type": "array"}, "serviceAddressCidrBlocks": {"description": "Required. All services in the cluster are assigned an RFC1918 IPv4 address from these ranges. Only a single range is supported. This field cannot be changed after creation.", "items": {"type": "string"}, "type": "array"}, "staticIpConfig": {"$ref": "VmwareStaticIpConfig", "description": "Configuration settings for a static IP configuration."}, "vcenterNetwork": {"description": "vcenter_network specifies vCenter network name.", "type": "string"}}, "type": "object"}, "VmwareAdminPreparedSecretsConfig": {"description": "VmwareAdminPreparedSecretsConfig represents configuration for admin cluster prepared secrets.", "id": "VmwareAdminPreparedSecretsConfig", "properties": {"enabled": {"description": "Whether prepared secrets is enabled.", "type": "boolean"}}, "type": "object"}, "VmwareAdminSeesawConfig": {"description": "VmwareSeesawConfig represents configuration parameters for an already existing Seesaw load balancer. IMPORTANT: Please note that the Anthos On-Prem API will not generate or update Seesaw configurations it can only bind a pre-existing configuration to a new user cluster. IMPORTANT: When attempting to create a user cluster with a pre-existing Seesaw load balancer you will need to follow some preparation steps before calling the 'CreateVmwareCluster' API method. First you will need to create the user cluster's namespace via kubectl. The namespace will need to use the following naming convention : -gke-onprem-mgmt or -gke-onprem-mgmt depending on whether you used the 'VmwareCluster.local_name' to disambiguate collisions; for more context see the documentation of 'VmwareCluster.local_name'. Once the namespace is created you will need to create a secret resource via kubectl. This secret will contain copies of your Seesaw credentials. The Secret must be called 'user-cluster-creds' and contain Seesaw's SSH and Cert credentials. The credentials must be keyed with the following names: 'seesaw-ssh-private-key', 'seesaw-ssh-public-key', 'seesaw-ssh-ca-key', 'seesaw-ssh-ca-cert'.", "id": "VmwareAdminSeesawConfig", "properties": {"enableHa": {"description": "Enable two load balancer VMs to achieve a highly-available Seesaw load balancer.", "type": "boolean"}, "group": {"description": "In general the following format should be used for the Seesaw group name: seesaw-for-[cluster_name].", "type": "string"}, "ipBlocks": {"description": "The IP Blocks to be used by the Seesaw load balancer", "items": {"$ref": "VmwareIpBlock"}, "type": "array"}, "masterIp": {"description": "MasterIP is the IP announced by the master of Seesaw group.", "type": "string"}, "stackdriverName": {"description": "Name to be used by Stackdriver.", "type": "string"}, "vms": {"description": "Names of the VMs created for this Seesaw group.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "VmwareAdminVCenterConfig": {"description": "VmwareAdminVCenterConfig contains VCenter configuration for VMware admin cluster.", "id": "VmwareAdminVCenterConfig", "properties": {"address": {"description": "The vCenter IP address.", "type": "string"}, "caCertData": {"description": "Contains the vCenter CA certificate public key for SSL verification.", "type": "string"}, "cluster": {"description": "The name of the vCenter cluster for the admin cluster.", "type": "string"}, "dataDisk": {"description": "The name of the virtual machine disk (VMDK) for the admin cluster.", "type": "string"}, "datacenter": {"description": "The name of the vCenter datacenter for the admin cluster.", "type": "string"}, "datastore": {"description": "The name of the vCenter datastore for the admin cluster.", "type": "string"}, "folder": {"description": "The name of the vCenter folder for the admin cluster.", "type": "string"}, "resourcePool": {"description": "The name of the vCenter resource pool for the admin cluster.", "type": "string"}, "storagePolicyName": {"description": "The name of the vCenter storage policy for the user cluster.", "type": "string"}}, "type": "object"}, "VmwareAdminVipConfig": {"description": "VmwareAdminVipConfig for VMware load balancer configurations.", "id": "VmwareAdminVipConfig", "properties": {"addonsVip": {"description": "The VIP to configure the load balancer for add-ons.", "type": "string"}, "controlPlaneVip": {"description": "The VIP which you previously set aside for the Kubernetes API of the admin cluster.", "type": "string"}}, "type": "object"}, "VmwareAutoRepairConfig": {"description": "Specifies config to enable/disable auto repair. The cluster-health-controller is deployed only if Enabled is true.", "id": "VmwareAutoRepairConfig", "properties": {"enabled": {"description": "Whether auto repair is enabled.", "type": "boolean"}}, "type": "object"}, "VmwareAutoResizeConfig": {"description": "Represents auto resizing configurations for the VMware user cluster.", "id": "VmwareAutoResizeConfig", "properties": {"enabled": {"description": "Whether to enable controle plane node auto resizing.", "type": "boolean"}}, "type": "object"}, "VmwareBundleConfig": {"description": "VmwareBundleConfig represents configuration for the bundle.", "id": "VmwareBundleConfig", "properties": {"status": {"$ref": "ResourceStatus", "description": "Output only. Resource status for the bundle.", "readOnly": true}, "version": {"description": "The version of the bundle.", "type": "string"}}, "type": "object"}, "VmwareCluster": {"description": "Resource that represents a VMware user cluster. ##", "id": "VmwareCluster", "properties": {"adminClusterMembership": {"description": "Required. The admin cluster this VMware user cluster belongs to. This is the full resource name of the admin cluster's fleet membership. In the future, references to other resource types might be allowed if admin clusters are modeled as their own resources.", "type": "string"}, "adminClusterName": {"description": "Output only. The resource name of the VMware admin cluster hosting this user cluster.", "readOnly": true, "type": "string"}, "annotations": {"additionalProperties": {"type": "string"}, "description": "Annotations on the VMware user cluster. This field has the same restrictions as Kubernetes annotations. The total size of all keys and values combined is limited to 256k. Key can have 2 segments: prefix (optional) and name (required), separated by a slash (/). Prefix must be a DNS subdomain. Name must be 63 characters or less, begin and end with alphanumerics, with dashes (-), underscores (_), dots (.), and alphanumerics between.", "type": "object"}, "antiAffinityGroups": {"$ref": "VmwareAAGConfig", "description": "AAGConfig specifies whether to spread VMware user cluster nodes across at least three physical hosts in the datacenter."}, "authorization": {"$ref": "Authorization", "description": "RBAC policy that will be applied and managed by the Anthos On-Prem API."}, "autoRepairConfig": {"$ref": "VmwareAutoRepairConfig", "description": "Configuration for auto repairing."}, "binaryAuthorization": {"$ref": "BinaryAuthorization", "description": "Binary Authorization related configurations."}, "controlPlaneNode": {"$ref": "VmwareControlPlaneNodeConfig", "description": "VMware user cluster control plane nodes must have either 1 or 3 replicas."}, "createTime": {"description": "Output only. The time at which VMware user cluster was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dataplaneV2": {"$ref": "VmwareDataplaneV2Config", "description": "VmwareDataplaneV2Config specifies configuration for Dataplane V2."}, "deleteTime": {"description": "Output only. The time at which VMware user cluster was deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "A human readable description of this VMware user cluster.", "type": "string"}, "disableBundledIngress": {"description": "Disable bundled ingress.", "type": "boolean"}, "enableAdvancedCluster": {"description": "Enable advanced cluster.", "type": "boolean"}, "enableControlPlaneV2": {"description": "Enable control plane V2. Default to false.", "type": "boolean"}, "endpoint": {"description": "Output only. The DNS name of VMware user cluster's API server.", "readOnly": true, "type": "string"}, "etag": {"description": "This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding. Allows clients to perform consistent read-modify-writes through optimistic concurrency control.", "type": "string"}, "fleet": {"$ref": "Fleet", "description": "Output only. Fleet configuration for the cluster.", "readOnly": true}, "loadBalancer": {"$ref": "VmwareLoadBalancerConfig", "description": "Load balancer configuration."}, "localName": {"description": "Output only. The object name of the VMware OnPremUserCluster custom resource on the associated admin cluster. This field is used to support conflicting names when enrolling existing clusters to the API. When used as a part of cluster enrollment, this field will differ from the ID in the resource name. For new clusters, this field will match the user provided cluster name and be visible in the last component of the resource name. It is not modifiable. All users should use this name to access their cluster using gkectl or kubectl and should expect to see the local name when viewing admin cluster controller logs.", "readOnly": true, "type": "string"}, "name": {"description": "Immutable. The VMware user cluster resource name.", "type": "string"}, "networkConfig": {"$ref": "VmwareNetworkConfig", "description": "The VMware user cluster network configuration."}, "onPremVersion": {"description": "Required. The Anthos clusters on the VMware version for your user cluster.", "type": "string"}, "reconciling": {"description": "Output only. If set, there are currently changes in flight to the VMware user cluster.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. The current state of VMware user cluster.", "enum": ["STATE_UNSPECIFIED", "PROVISIONING", "RUNNING", "RECONCILING", "STOPPING", "ERROR", "DEGRADED"], "enumDescriptions": ["Not set.", "The PROVISIONING state indicates the cluster is being created.", "The RUNNING state indicates the cluster has been created and is fully usable.", "The RECONCILING state indicates that the cluster is being updated. It remains available, but potentially with degraded performance.", "The STOPPING state indicates the cluster is being deleted.", "The ERROR state indicates the cluster is in a broken unrecoverable state.", "The DEGRADED state indicates the cluster requires user action to restore full functionality."], "readOnly": true, "type": "string"}, "status": {"$ref": "ResourceStatus", "description": "Output only. ResourceStatus representing detailed cluster state.", "readOnly": true}, "storage": {"$ref": "VmwareStorageConfig", "description": "Storage configuration."}, "uid": {"description": "Output only. The unique identifier of the VMware user cluster.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time at which VMware user cluster was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "upgradePolicy": {"$ref": "VmwareClusterUpgradePolicy", "description": "Specifies upgrade policy for the cluster."}, "validationCheck": {"$ref": "ValidationCheck", "description": "Output only. ValidationCheck represents the result of the preflight check job.", "readOnly": true}, "vcenter": {"$ref": "VmwareVCenterConfig", "description": "VmwareVCenterConfig specifies vCenter config for the user cluster. If unspecified, it is inherited from the admin cluster."}, "vmTrackingEnabled": {"description": "Enable VM tracking.", "type": "boolean"}}, "type": "object"}, "VmwareClusterUpgradePolicy": {"description": "VmwareClusterUpgradePolicy defines the cluster upgrade policy.", "id": "VmwareClusterUpgradePolicy", "properties": {"controlPlaneOnly": {"description": "Controls whether the upgrade applies to the control plane only.", "type": "boolean"}}, "type": "object"}, "VmwareControlPlaneNodeConfig": {"description": "Specifies control plane node config for the VMware user cluster.", "id": "VmwareControlPlaneNodeConfig", "properties": {"autoResizeConfig": {"$ref": "VmwareAutoResizeConfig", "description": "AutoResizeConfig provides auto resizing configurations."}, "cpus": {"description": "The number of CPUs for each admin cluster node that serve as control planes for this VMware user cluster. (default: 4 CPUs)", "format": "int64", "type": "string"}, "memory": {"description": "The megabytes of memory for each admin cluster node that serves as a control plane for this VMware user cluster (default: 8192 MB memory).", "format": "int64", "type": "string"}, "replicas": {"description": "The number of control plane nodes for this VMware user cluster. (default: 1 replica).", "format": "int64", "type": "string"}, "vsphereConfig": {"$ref": "VmwareControlPlaneVsphereConfig", "description": "Vsphere-specific config."}}, "type": "object"}, "VmwareControlPlaneV2Config": {"description": "Specifies control plane V2 config.", "id": "VmwareControlPlaneV2Config", "properties": {"controlPlaneIpBlock": {"$ref": "VmwareIpBlock", "description": "Static IP addresses for the control plane nodes."}}, "type": "object"}, "VmwareControlPlaneVsphereConfig": {"description": "Specifies control plane node config.", "id": "VmwareControlPlaneVsphereConfig", "properties": {"datastore": {"description": "The Vsphere datastore used by the control plane Node.", "type": "string"}, "storagePolicyName": {"description": "The Vsphere storage policy used by the control plane Node.", "type": "string"}}, "type": "object"}, "VmwareDataplaneV2Config": {"description": "Contains configurations for Dataplane V2, which is optimized dataplane for Kubernetes networking. For more information, see: https://cloud.google.com/kubernetes-engine/docs/concepts/dataplane-v2", "id": "VmwareDataplaneV2Config", "properties": {"advancedNetworking": {"description": "Enable advanced networking which requires dataplane_v2_enabled to be set true.", "type": "boolean"}, "dataplaneV2Enabled": {"description": "Enables Dataplane V2.", "type": "boolean"}, "forwardMode": {"description": "Configure ForwardMode for Dataplane v2.", "type": "string"}, "windowsDataplaneV2Enabled": {"description": "Enable Dataplane V2 for clusters with Windows nodes.", "type": "boolean"}}, "type": "object"}, "VmwareDhcpIpConfig": {"description": "Represents the network configuration required for the VMware user clusters with DHCP IP configurations.", "id": "VmwareDhcpIpConfig", "properties": {"enabled": {"description": "enabled is a flag to mark if DHCP IP allocation is used for VMware user clusters.", "type": "boolean"}}, "type": "object"}, "VmwareF5BigIpConfig": {"description": "Represents configuration parameters for an F5 BIG-IP load balancer.", "id": "VmwareF5BigIpConfig", "properties": {"address": {"description": "The load balancer's IP address.", "type": "string"}, "partition": {"description": "The preexisting partition to be used by the load balancer. This partition is usually created for the admin cluster for example: 'my-f5-admin-partition'.", "type": "string"}, "snatPool": {"description": "The pool name. Only necessary, if using SNAT.", "type": "string"}}, "type": "object"}, "VmwareHostConfig": {"description": "Represents the common parameters for all the hosts irrespective of their IP address.", "id": "VmwareHostConfig", "properties": {"dnsSearchDomains": {"description": "DNS search domains.", "items": {"type": "string"}, "type": "array"}, "dnsServers": {"description": "DNS servers.", "items": {"type": "string"}, "type": "array"}, "ntpServers": {"description": "NTP servers.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "VmwareHostIp": {"description": "Represents VMware user cluster node's network configuration.", "id": "VmwareHostIp", "properties": {"hostname": {"description": "Hostname of the machine. VM's name will be used if this field is empty.", "type": "string"}, "ip": {"description": "IP could be an IP address (like *******) or a CIDR (like *******/24).", "type": "string"}}, "type": "object"}, "VmwareIpBlock": {"description": "Represents a collection of IP addresses to assign to nodes.", "id": "VmwareIpBlock", "properties": {"gateway": {"description": "The network gateway used by the VMware user cluster.", "type": "string"}, "ips": {"description": "The node's network configurations used by the VMware user cluster.", "items": {"$ref": "VmwareHostIp"}, "type": "array"}, "netmask": {"description": "The netmask used by the VMware user cluster.", "type": "string"}}, "type": "object"}, "VmwareLoadBalancerConfig": {"description": "Specifies the locad balancer config for the VMware user cluster.", "id": "VmwareLoadBalancerConfig", "properties": {"f5Config": {"$ref": "VmwareF5BigIpConfig", "description": "Configuration for F5 Big IP typed load balancers."}, "manualLbConfig": {"$ref": "VmwareManualLbConfig", "description": "Manually configured load balancers."}, "metalLbConfig": {"$ref": "VmwareMetalLbConfig", "description": "Configuration for MetalLB typed load balancers."}, "seesawConfig": {"$ref": "VmwareSeesawConfig", "description": "Output only. Configuration for Seesaw typed load balancers.", "readOnly": true}, "vipConfig": {"$ref": "VmwareVipConfig", "description": "The VIPs used by the load balancer."}}, "type": "object"}, "VmwareManualLbConfig": {"description": "Represents configuration parameters for an already existing manual load balancer. Given the nature of manual load balancers it is expected that said load balancer will be fully managed by users. IMPORTANT: Please note that the Anthos On-Prem API will not generate or update ManualLB configurations it can only bind a pre-existing configuration to a new VMware user cluster.", "id": "VmwareManualLbConfig", "properties": {"controlPlaneNodePort": {"description": "NodePort for control plane service. The Kubernetes API server in the admin cluster is implemented as a Service of type NodePort (ex. 30968).", "format": "int32", "type": "integer"}, "ingressHttpNodePort": {"description": "NodePort for ingress service's http. The ingress service in the admin cluster is implemented as a Service of type NodePort (ex. 32527).", "format": "int32", "type": "integer"}, "ingressHttpsNodePort": {"description": "NodePort for ingress service's https. The ingress service in the admin cluster is implemented as a Service of type NodePort (ex. 30139).", "format": "int32", "type": "integer"}, "konnectivityServerNodePort": {"description": "NodePort for konnectivity server service running as a sidecar in each kube-apiserver pod (ex. 30564).", "format": "int32", "type": "integer"}}, "type": "object"}, "VmwareMetalLbConfig": {"description": "Represents configuration parameters for the MetalLB load balancer.", "id": "VmwareMetalLbConfig", "properties": {"addressPools": {"description": "Required. AddressPools is a list of non-overlapping IP pools used by load balancer typed services. All addresses must be routable to load balancer nodes. IngressVIP must be included in the pools.", "items": {"$ref": "VmwareAddressPool"}, "type": "array"}}, "type": "object"}, "VmwareNetworkConfig": {"description": "Specifies network config for the VMware user cluster.", "id": "VmwareNetworkConfig", "properties": {"controlPlaneV2Config": {"$ref": "VmwareControlPlaneV2Config", "description": "Configuration for control plane V2 mode."}, "dhcpIpConfig": {"$ref": "VmwareDhcpIpConfig", "description": "Configuration settings for a DHCP IP configuration."}, "hostConfig": {"$ref": "VmwareHostConfig", "description": "Represents common network settings irrespective of the host's IP address."}, "podAddressCidrBlocks": {"description": "Required. All pods in the cluster are assigned an RFC1918 IPv4 address from these ranges. Only a single range is supported. This field cannot be changed after creation.", "items": {"type": "string"}, "type": "array"}, "serviceAddressCidrBlocks": {"description": "Required. All services in the cluster are assigned an RFC1918 IPv4 address from these ranges. Only a single range is supported. This field cannot be changed after creation.", "items": {"type": "string"}, "type": "array"}, "staticIpConfig": {"$ref": "VmwareStaticIpConfig", "description": "Configuration settings for a static IP configuration."}, "vcenterNetwork": {"description": "vcenter_network specifies vCenter network name. Inherited from the admin cluster.", "type": "string"}}, "type": "object"}, "VmwareNodeConfig": {"description": "Parameters that describe the configuration of all nodes within a given node pool.", "id": "VmwareNodeConfig", "properties": {"bootDiskSizeGb": {"description": "VMware disk size to be used during creation.", "format": "int64", "type": "string"}, "cpus": {"description": "The number of CPUs for each node in the node pool.", "format": "int64", "type": "string"}, "enableLoadBalancer": {"description": "Allow node pool traffic to be load balanced. Only works for clusters with MetalLB load balancers.", "type": "boolean"}, "image": {"description": "The OS image name in vCenter, only valid when using Windows.", "type": "string"}, "imageType": {"description": "Required. The OS image to be used for each node in a node pool. Currently `cos`, `cos_cgv2`, `ubuntu`, `ubuntu_cgv2`, `ubuntu_containerd` and `windows` are supported.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "The map of Kubernetes labels (key/value pairs) to be applied to each node. These will added in addition to any default label(s) that Kubernetes may apply to the node. In case of conflict in label keys, the applied set may differ depending on the Kubernetes version -- it's best to assume the behavior is undefined and conflicts should be avoided. For more information, including usage and the valid values, see: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/", "type": "object"}, "memoryMb": {"description": "The megabytes of memory for each node in the node pool.", "format": "int64", "type": "string"}, "replicas": {"description": "The number of nodes in the node pool.", "format": "int64", "type": "string"}, "taints": {"description": "The initial taints assigned to nodes of this node pool.", "items": {"$ref": "NodeTaint"}, "type": "array"}, "vsphereConfig": {"$ref": "VmwareVsphereConfig", "description": "Specifies the vSphere config for node pool."}}, "type": "object"}, "VmwareNodePool": {"description": "Resource VmwareNodePool represents a VMware node pool. ##", "id": "VmwareNodePool", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Annotations on the node pool. This field has the same restrictions as Kubernetes annotations. The total size of all keys and values combined is limited to 256k. Key can have 2 segments: prefix (optional) and name (required), separated by a slash (/). Prefix must be a DNS subdomain. Name must be 63 characters or less, begin and end with alphanumerics, with dashes (-), underscores (_), dots (.), and alphanumerics between.", "type": "object"}, "config": {"$ref": "VmwareNodeConfig", "description": "Required. The node configuration of the node pool."}, "createTime": {"description": "Output only. The time at which this node pool was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deleteTime": {"description": "Output only. The time at which this node pool was deleted. If the resource is not deleted, this must be empty", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "The display name for the node pool.", "type": "string"}, "etag": {"description": "This checksum is computed by the server based on the value of other fields, and may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding. Allows clients to perform consistent read-modify-writes through optimistic concurrency control.", "type": "string"}, "name": {"description": "Immutable. The resource name of this node pool.", "type": "string"}, "nodePoolAutoscaling": {"$ref": "VmwareNodePoolAutoscalingConfig", "description": "Node pool autoscaling config for the node pool."}, "onPremVersion": {"description": "Anthos version for the node pool. Defaults to the user cluster version.", "type": "string"}, "reconciling": {"description": "Output only. If set, there are currently changes in flight to the node pool.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. The current state of the node pool.", "enum": ["STATE_UNSPECIFIED", "PROVISIONING", "RUNNING", "RECONCILING", "STOPPING", "ERROR", "DEGRADED"], "enumDescriptions": ["Not set.", "The PROVISIONING state indicates the node pool is being created.", "The RUNNING state indicates the node pool has been created and is fully usable.", "The RECONCILING state indicates that the node pool is being updated. It remains available, but potentially with degraded performance.", "The STOPPING state indicates the cluster is being deleted", "The ERROR state indicates the node pool is in a broken unrecoverable state.", "The DEGRADED state indicates the node pool requires user action to restore full functionality."], "readOnly": true, "type": "string"}, "status": {"$ref": "ResourceStatus", "description": "Output only. ResourceStatus representing the detailed VMware node pool state.", "readOnly": true}, "uid": {"description": "Output only. The unique identifier of the node pool.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time at which this node pool was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "VmwareNodePoolAutoscalingConfig": {"description": "NodePoolAutoscaling config for the NodePool to allow for the kubernetes to scale NodePool.", "id": "VmwareNodePoolAutoscalingConfig", "properties": {"maxReplicas": {"description": "Maximum number of replicas in the NodePool.", "format": "int32", "type": "integer"}, "minReplicas": {"description": "Minimum number of replicas in the NodePool.", "format": "int32", "type": "integer"}}, "type": "object"}, "VmwarePlatformConfig": {"description": "VmwarePlatformConfig represents configuration for the VMware platform.", "id": "VmwarePlatformConfig", "properties": {"bundles": {"description": "Output only. The list of bundles installed in the admin cluster.", "items": {"$ref": "VmwareBundleConfig"}, "readOnly": true, "type": "array"}, "platformVersion": {"description": "Output only. The platform version e.g. 1.13.2.", "readOnly": true, "type": "string"}, "requiredPlatformVersion": {"description": "Input only. The required platform version e.g. 1.13.1. If the current platform version is lower than the target version, the platform version will be updated to the target version. If the target version is not installed in the platform (bundle versions), download the target version bundle.", "type": "string"}, "status": {"$ref": "ResourceStatus", "description": "Output only. Resource status for the platform.", "readOnly": true}}, "type": "object"}, "VmwareSeesawConfig": {"description": "VmwareSeesawConfig represents configuration parameters for an already existing Seesaw load balancer. IMPORTANT: Please note that the Anthos On-Prem API will not generate or update Seesaw configurations it can only bind a pre-existing configuration to a new user cluster. IMPORTANT: When attempting to create a user cluster with a pre-existing Seesaw load balancer you will need to follow some preparation steps before calling the 'CreateVmwareCluster' API method. First you will need to create the user cluster's namespace via kubectl. The namespace will need to use the following naming convention : -gke-onprem-mgmt or -gke-onprem-mgmt depending on whether you used the 'VmwareCluster.local_name' to disambiguate collisions; for more context see the documentation of 'VmwareCluster.local_name'. Once the namespace is created you will need to create a secret resource via kubectl. This secret will contain copies of your Seesaw credentials. The Secret must be called 'user-cluster-creds' and contain Seesaw's SSH and Cert credentials. The credentials must be keyed with the following names: 'seesaw-ssh-private-key', 'seesaw-ssh-public-key', 'seesaw-ssh-ca-key', 'seesaw-ssh-ca-cert'.", "id": "VmwareSeesawConfig", "properties": {"enableHa": {"description": "Enable two load balancer VMs to achieve a highly-available Seesaw load balancer.", "type": "boolean"}, "group": {"description": "Required. In general the following format should be used for the Seesaw group name: seesaw-for-[cluster_name].", "type": "string"}, "ipBlocks": {"description": "Required. The IP Blocks to be used by the Seesaw load balancer", "items": {"$ref": "VmwareIpBlock"}, "type": "array"}, "masterIp": {"description": "Required. MasterIP is the IP announced by the master of Seesaw group.", "type": "string"}, "stackdriverName": {"description": "Name to be used by Stackdriver.", "type": "string"}, "vms": {"description": "Names of the VMs created for this Seesaw group.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "VmwareStaticIpConfig": {"description": "Represents the network configuration required for the VMware user clusters with Static IP configurations.", "id": "VmwareStaticIpConfig", "properties": {"ipBlocks": {"description": "Represents the configuration values for static IP allocation to nodes.", "items": {"$ref": "VmwareIpBlock"}, "type": "array"}}, "type": "object"}, "VmwareStorageConfig": {"description": "Specifies vSphere CSI components deployment config in the VMware user cluster.", "id": "VmwareStorageConfig", "properties": {"vsphereCsiDisabled": {"description": "Whether or not to deploy vSphere CSI components in the VMware user cluster. Enabled by default.", "type": "boolean"}}, "type": "object"}, "VmwareVCenterConfig": {"description": "Represents configuration for the VMware VCenter for the user cluster.", "id": "VmwareVCenterConfig", "properties": {"address": {"description": "Output only. The vCenter IP address.", "readOnly": true, "type": "string"}, "caCertData": {"description": "Contains the vCenter CA certificate public key for SSL verification.", "type": "string"}, "cluster": {"description": "The name of the vCenter cluster for the user cluster.", "type": "string"}, "datacenter": {"description": "The name of the vCenter datacenter for the user cluster.", "type": "string"}, "datastore": {"description": "The name of the vCenter datastore for the user cluster.", "type": "string"}, "folder": {"description": "The name of the vCenter folder for the user cluster.", "type": "string"}, "resourcePool": {"description": "The name of the vCenter resource pool for the user cluster.", "type": "string"}, "storagePolicyName": {"description": "The name of the vCenter storage policy for the user cluster.", "type": "string"}}, "type": "object"}, "VmwareVersionInfo": {"description": "Contains information about a specific Anthos on VMware version.", "id": "VmwareVersionInfo", "properties": {"dependencies": {"description": "The list of upgrade dependencies for this version.", "items": {"$ref": "UpgradeDependency"}, "type": "array"}, "hasDependencies": {"description": "If set, the cluster dependencies (e.g. the admin cluster, other user clusters managed by the same admin cluster) must be upgraded before this version can be installed or upgraded to.", "type": "boolean"}, "isInstalled": {"description": "If set, the version is installed in the admin cluster. Otherwise, the version bundle must be downloaded and installed before a user cluster can be created at or upgraded to this version.", "type": "boolean"}, "version": {"description": "Version number e.g. 1.13.1-gke.1000.", "type": "string"}}, "type": "object"}, "VmwareVipConfig": {"description": "Specifies the VIP config for the VMware user cluster load balancer.", "id": "VmwareVipConfig", "properties": {"controlPlaneVip": {"description": "The VIP which you previously set aside for the Kubernetes API of this cluster.", "type": "string"}, "ingressVip": {"description": "The VIP which you previously set aside for ingress traffic into this cluster.", "type": "string"}}, "type": "object"}, "VmwareVsphereConfig": {"description": "VmwareVsphereConfig represents configuration for the VMware VCenter for node pool.", "id": "VmwareVsphereConfig", "properties": {"datastore": {"description": "The name of the vCenter datastore. Inherited from the user cluster.", "type": "string"}, "hostGroups": {"description": "Vsphere host groups to apply to all VMs in the node pool", "items": {"type": "string"}, "type": "array"}, "tags": {"description": "Tags to apply to VMs.", "items": {"$ref": "VmwareVsphereTag"}, "type": "array"}}, "type": "object"}, "VmwareVsphereTag": {"description": "VmwareVsphereTag describes a vSphere tag to be placed on VMs in the node pool. For more information, see https://docs.vmware.com/en/VMware-vSphere/7.0/com.vmware.vsphere.vcenterhost.doc/GUID-E8E854DD-AA97-4E0C-8419-CE84F93C4058.html", "id": "VmwareVsphereTag", "properties": {"category": {"description": "The Vsphere tag category.", "type": "string"}, "tag": {"description": "The Vsphere tag name.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "GKE On-Prem API", "version": "v1", "version_module": true}