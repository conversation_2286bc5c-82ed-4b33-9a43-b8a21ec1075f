"""
Configuration settings for Me? Reincarnated? game
"""
from pathlib import Path

# API Configuration
GEMINI_API_KEY = "AIzaSyBVkAcu9f0KtSW8MYWQ5tkGtQtlWBZtIbU"

# Game Configuration
GAME_TITLE = "Me? Reincarnated?"
GAME_VERSION = "0.1.0"

# File Paths
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "data"
SAVES_DIR = BASE_DIR / "saves"
ASSETS_DIR = BASE_DIR / "assets"

# Ensure directories exist
DATA_DIR.mkdir(exist_ok=True)
SAVES_DIR.mkdir(exist_ok=True)
ASSETS_DIR.mkdir(exist_ok=True)

# UI Configuration
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
FONT_SIZE = 12
FONT_FAMILY = "Consolas"

# Game Settings
MAX_SHORT_TERM_MEMORY = 15
MAX_DISPLAY_LINES = 100
AUTO_SAVE_INTERVAL = 300  # seconds

# Gemini Model Settings
GEMINI_MODEL = "gemini-1.5-flash"
GEMINI_TEMPERATURE = 0.7
GEMINI_MAX_TOKENS = 1000

# Response Consistency Settings
RESPONSE_MIN_LENGTH = 50  # Minimum response length in characters
RESPONSE_MAX_LENGTH = 800  # Maximum response length in characters
ENABLE_RESPONSE_VALIDATION = True  # Enable post-processing validation
ENABLE_CONTEXT_CONTINUITY = True  # Enable narrative continuity tracking
CONSISTENCY_TEMPERATURE = 0.6  # Lower temperature for more consistent responses

# Starting Creatures - Enhanced with new options
STARTING_CREATURES = [
    {
        "name": "Slime",
        "description": "A gelatinous blob with incredible adaptability",
        "base_stats": {"hp": 50, "mp": 30, "attack": 5, "defense": 8, "speed": 3},
        "abilities": ["Absorb", "Acid Resistance"],
        "evolution_paths": ["Elemental Slime", "King Slime", "Mimic Slime"]
    },
    {
        "name": "Spider",
        "description": "A small but cunning arachnid with web-spinning abilities",
        "base_stats": {"hp": 30, "mp": 40, "attack": 8, "defense": 4, "speed": 9},
        "abilities": ["Web Spin", "Poison Bite"],
        "evolution_paths": ["Arachne", "Widow Spider", "Phase Spider"]
    },
    {
        "name": "Goblin",
        "description": "A small humanoid with natural cunning and tool use",
        "base_stats": {"hp": 40, "mp": 20, "attack": 7, "defense": 5, "speed": 6},
        "abilities": ["Tool Use", "Pack Tactics"],
        "evolution_paths": ["Hobgoblin", "Goblin Shaman", "Goblin King"]
    },
    {
        "name": "Wisp",
        "description": "A floating orb of pure magical energy with light and illusion powers",
        "base_stats": {"hp": 25, "mp": 60, "attack": 6, "defense": 2, "speed": 8},
        "abilities": ["Light Manipulation", "Magic Sense"],
        "evolution_paths": ["Elemental Wisp", "Guardian Spirit", "Arcane Wisp"]
    },
    {
        "name": "Rat",
        "description": "A small, agile rodent with keen senses and survival instincts",
        "base_stats": {"hp": 35, "mp": 25, "attack": 6, "defense": 3, "speed": 10},
        "abilities": ["Keen Senses", "Scavenge"],
        "evolution_paths": ["Dire Rat", "Plague Rat", "Shadow Rat"]
    },
    {
        "name": "Mushroom",
        "description": "A sentient fungus with spore-based abilities and natural healing powers",
        "base_stats": {"hp": 45, "mp": 35, "attack": 4, "defense": 7, "speed": 2},
        "abilities": ["Spore Release", "Natural Healing"],
        "evolution_paths": ["Mycelium Network", "Toxic Mushroom", "Healing Mushroom"]
    }
]
